import {Component, OnInit} from "@angular/core";
import {ConstantsService} from "../ConstantsService";
import {AutorizacaoService} from "../../services/autorizacao.service";
import {ActivatedRoute} from "@angular/router";
import {EmpresasService} from "../../superadmin/services/empresas.service";

@Component({
  selector: 'app-tela-config-mesas',
  templateUrl: './tela-config-mesas.component.html',
  styleUrls: ['./tela-config-mesas.component.scss']
})
export class TelaConfigMesasComponent implements OnInit {
  empresa: any = {}
  salvando = false;
  mensagemSucesso: string;
  constructor(private constantsService: ConstantsService, private autorizacaoService: AutorizacaoService,
              private  activatedRoute: ActivatedRoute,
              private empresasService: EmpresasService) {


  }


  ngOnInit() {
    this.constantsService.empresa$.subscribe( empresa => {
      if (empresa)      this.empresa = empresa;

    })
  }

  salveConfigMesas() {
    if(this.salvando) return;

    this.salvando = true;

    this.empresasService.atualizeConfigPedidoMesaNaoIndentificada(this.empresa).then( () => {
      this.salvando = false;
      this.mensagemSucesso = 'Configuração salva com sucesso!'
    }).catch( (erro: string) => {
      this.salvando = false;
      alert(erro)
    })
  }

  fecheMensagemSucesso() {
    delete this.mensagemSucesso;
  }


}
