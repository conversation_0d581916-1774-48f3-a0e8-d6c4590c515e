<h4>Configurações das Mesas</h4>

<p>Configurações de pedidos feitos em mesa </p>
<div class="card">
  <div class="card-body">
    <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
          novalidate #frm="ngForm" (ngSubmit)="salveConfigMesas()" >

      <div class="form-group col-12">
        <label class="k-checkbox-label">
          <input type="checkbox"
                 name="usarCartaoCliente"
                 class="k-checkbox"
                 kendoCheckBox
                 [(ngModel)]="empresa.usarCartaoCliente" />
          Usar cartão cliente nas mesas <span class="badge badge-pill badge-success ml-1">Novo</span></label>
      </div>

      <div class="form-group  col-12"  >
        <label class="k-checkbox-label">
          <input type="checkbox"   name="permitirMultiplasComandasMesa" class="k-checkbox "  kendoCheckBox  [(ngModel)]="empresa.permitirMultiplasComandasMesa" />
          Permitir múltiplas comandas por mesa
          <span class="badge badge-pill badge-success ml-1">Novo</span>
        </label>
      </div>

      <div class="form-group  col-12"  *ngIf="empresa.usarCartaoCliente && empresa.permitirMultiplasComandasMesa">
        <label class="k-checkbox-label">
          <input type="checkbox"   name="associarCartaoFechamentoPedido" class="k-checkbox "  kendoCheckBox
                 [(ngModel)]="empresa.associarCartaoFechamentoPedido" />
          Associar de Cartão Cliente no Fechamento do Pedido
          <span class="badge badge-pill badge-success ml-1">Novo</span>
        </label>
      </div>

      <div class="form-group  col-12"  >
        <label class="k-checkbox-label">
          <input type="checkbox"   name="pedidoMesaNaoIdentificado" class="k-checkbox "  kendoCheckBox  [(ngModel)]="empresa.pedidoMesaNaoIdentificado" />
          Permitir compra de consumidor não identificado</label>
      </div>

      <div class="form-group  col-12"  >
        <label class="k-checkbox-label">
          <input type="checkbox"   name="destaque" class="k-checkbox "  kendoCheckBox  [(ngModel)]="empresa.avisosDeMesa" />
          Ativar avisos de solicitação de garçom e fechamento de mesa pelo cardápio digital</label>
      </div>


      <div class="form-group  col-12"  >
        <label class="k-checkbox-label">
          <input type="checkbox"   name="taxaDeServico" class="k-checkbox "  kendoCheckBox [(ngModel)]="empresa.cobrarTaxaServico" />
          Cobrar taxa de serviço</label>
      </div>

      <div class="form-group col col-sm-5 col-lg-4" *ngIf="empresa.cobrarTaxaServico" >
        <h5 class="card-title">Valor da taxa de serviço</h5>

        <input type="text" class="form-control" autocomplete="off"  #valorTaxaServico=ngModel
               currencyMask [options]="{ prefix:'', suffix: '%', thousands: '.', decimal: ',', align: 'left' }"
               name="valorMinimoPedidoEntrega" [(ngModel)]="empresa.valorTaxaServico"
               [minimo]="0" value="10"
               placeholder="Valor da taxa em % (ex: 10%)"   >


        <span class="text-muted"  >
                   <small>
                     Os pedidos de mesa terão <b>{{empresa.valorTaxaServico}}%</b> adicionados no valor no fechamento.
                    </small>
              </span>

      </div>

      <div class="form-group  col-12"  >
        <label class="k-checkbox-label">
          <input type="checkbox"   name="permitirCupomMesas" class="k-checkbox "  kendoCheckBox  [(ngModel)]="empresa.permitirCupomMesas" />
          Permitir cupons em mesas</label>
      </div>


      <div class="form-group col-12">
        <label class="k-checkbox-label">
          <input type="checkbox"
                 name="garcomFecharComandas"
                 class="k-checkbox"
                 kendoCheckBox
                 [(ngModel)]="empresa.garcomFecharComandas" />
          Permitir garçom fechar comandas  </label>
      </div>

      <div class="form-group mb-2 col-6">
        <label for="token">Escolha um prefixo que será inserido na frente de todos as mesas cadastradas (padrão: Mesa) </label>
        <input kendoTextBox id="token" name="token"  placeholder="Mesa"
               class="form-control"   #token="ngModel"
               [(ngModel)]="empresa.identificadorMesa" required/>

        <div class="invalid-feedback">
          <p  >Identificador é obrigatório</p>
        </div>

      </div>



      <div class="col-12">
        <button class="btn btn-success"  type="submit" [disabled]="salvando || !frm.dirty " >
          <i class="k-i-loading k-icon mr-1" *ngIf="salvando"></i>
          Salvar</button>
        <a class="btn btn-blue ml-1" [routerLink]="['/admin/mesas']">
          <i class="fa fa-table fa-lg mr-1"></i> Ver Mesas</a>


        <div class="alert alert-success alert-dismissible fade show" role="alert" *ngIf="mensagemSucesso">
          <i class="mdi mdi-check-all mr-1"></i> {{mensagemSucesso}}
          <button type="button" class="close" data-dismiss="alert" aria-label="Fechar" (click)="fecheMensagemSucesso()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>

      </div>

    </form>
  </div>
</div>
