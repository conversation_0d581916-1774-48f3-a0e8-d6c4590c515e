<div class="modal-fechar-loja">
  <div class="header">
    <h4><PERSON><PERSON><PERSON><PERSON></h4>
    <button class="btn btn-link btn-fechar" (click)="fechar()">
      <i class="fas fa-times"></i>
    </button>
  </div>

  <div class="content">
    <!-- Header com título dinâmico e ícone -->
    <div class="tipo-fechamento-header">
      <div class="header-content">
        <i *ngIf="!tipoReaberturaSelecionado" class="fas fa-store"></i>
        <i *ngIf="tipoReaberturaSelecionado === 'agora'" class="fas fa-power-off"></i>
        <i *ngIf="tipoReaberturaSelecionado === 'programar'" class="fas fa-calendar-alt"></i>

        <h5 *ngIf="!tipoReaberturaSelecionado">Como você deseja reabrir a loja?</h5>
        <h5 *ngIf="tipoReaberturaSelecionado === 'agora'">Reabertura Imediata</h5>
        <h5 *ngIf="tipoReaberturaSelecionado === 'programar'">Reabrir e Programar Novo Fechamento</h5>
      </div>
    </div>

    <!-- Opções de Reabertura -->
    <div class="options-container" *ngIf="!tipoReaberturaSelecionado">
      <!-- Opção 1: Reabrir Agora -->
      <div class="option-card" (click)="selecionarTipoReabertura('agora')">
        <i class="fas fa-power-off"></i>
        <div class="option-content">
          <h5>Reabrir Agora</h5>
          <p>Reabrir a loja e cancelar a pausa programada atual</p>
        </div>
      </div>

      <!-- Opção 2: Reabrir e Programar -->
      <div class="option-card" (click)="selecionarTipoReabertura('programar')">
        <i class="fas fa-calendar-alt"></i>
        <div class="option-content">
          <h5>Reabrir e Programar</h5>
          <p>Reabrir agora e agendar um novo fechamento</p>
        </div>
      </div>
    </div>

    <!-- Conteúdo específico para cada tipo de reabertura -->
    <div class="fechamento-content" *ngIf="tipoReaberturaSelecionado">

      <!-- Opção 1: Reabrir Agora -->
      <div class="option-section" *ngIf="!reprogramarNova()">
        <div class="confirmacao-reabertura">
          <div class="alert alert-info">
            <i class="fas fa-info-circle"></i>
            <div class="mensagem">
              <h6>Atenção!</h6>
              <p>A loja será reaberta imediatamente e a pausa programada atual será cancelada.</p>
            </div>
          </div>


          <div class="alert alert-danger mb-3 mt-2" *ngIf="msgErro">
            {{msgErro}}
          </div>

          <button class="btn btn-primary btn-block mt-3" (click)="reabrirAgora()" [disabled]="salvando">
            <i class="k-icon k-i-loading mr-2" *ngIf="salvando"></i>
            <i class="fas fa-check-circle mr-2" [hidden]="salvando"></i>
            Confirmar Reabertura
          </button>

          <!-- Botão Voltar -->
          <button class="btn btn-link mt-2" (click)="voltarSelecao()">
            <i class="fas fa-arrow-left"></i> Voltar
          </button>

        </div>
      </div>

      <!-- Opção 2: Programar Novo Fechamento -->
      <div class="option-section" *ngIf="reprogramarNova()">
        <form [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
              novalidate #frm="ngForm" (ngSubmit)="confirmarReabertura()">
          <h5>Defina o novo período de fechamento</h5>

          <!-- Data e Hora de Fechamento -->
          <div class="periodo-grupo">
            <div class="periodo-header">
              <i class="fas fa-stop-circle"></i>
              <span>Horário de Fechamento</span>
            </div>
            <div class="periodo-campos">
              <div class="form-group">
                <label>Data</label>
                <kendo-datepicker
                  [(ngModel)]="dataFechamento" required #dtFechamento="ngModel" name="dtFechamento"
                  [format]="'dd/MM/yyyy'">
                </kendo-datepicker>

                <div class="invalid-feedback">
                  <p *ngIf="dtFechamento.errors?.required">Data obrigatório</p>
                </div>

              </div>
              <div class="form-group">
                <label>Hora</label>
                <kendo-timepicker  required #hrFechamento="ngModel" name="hrFechamento"
                  [(ngModel)]="horaFechamento"
                  format="HH:mm">
                </kendo-timepicker>
                <div class="invalid-feedback">
                  <p *ngIf="hrFechamento.errors?.required">Horário obrigatório</p>
                </div>

              </div>
            </div>
          </div>

          <!-- Data e Hora de Reabertura (Opcional) -->
          <div class="periodo-grupo">
            <div class="periodo-header">
              <i class="fas fa-play-circle"></i>
              <span>Horário de Reabertura (Opcional)</span>
            </div>
            <div class="periodo-campos">
              <div class="form-group">
                <label>Data</label>
                <kendo-datepicker
                  [(ngModel)]="dataReabertura" #dtReabertura="ngModel" name="dtReabertura"
                  [format]="'dd/MM/yyyy'">
                </kendo-datepicker>
              </div>
              <div class="form-group">
                <label>Hora</label>
                <kendo-timepicker  [required]=" dataReabertura != null" #hrReabertura="ngModel" name="hrReabertura"
                  [(ngModel)]="horaReabertura"
                  format="HH:mm">
                </kendo-timepicker>
              </div>
            </div>
          </div>


          <div class="row mt-2"  >
            <div class="form-group mb-3 col">
              <label for="descricao">Descrição da pausa programada</label>
              <input type="text" class="form-control" autocomplete="off"
                     id="descricao" name="descricao" [(ngModel)]="novaPausaProgramada.descricao" #descricao="ngModel"
                     placeholder="Descricao da Pausa Programada" value="" required   appAutoFocus [autoFocus]="true">
              <div class="invalid-feedback">
                <p *ngIf="descricao.errors?.required">Descrição é obrigatório</p>
              </div>
            </div>
          </div>

          <div class="row mt-2">
            <div class="form-group mb-3 col">
              <label for="mensagem">Mensagem para cliente (opcional)</label>
              <input type="text" class="form-control" autocomplete="off"
                     id="mensagem" name="mensagem" [(ngModel)]="novaPausaProgramada.mensagem" #mensagem="ngModel"
                     placeholder="Mensagem que será exibida no cardápio durante a pausa" value="">
            </div>
          </div>


          <div class="alert alert-danger mb-3 mt-2" *ngIf="msgErro">
            {{msgErro}}
          </div>


          <button class="btn btn-primary mt-3"  type="submit"  [disabled]="salvando">
            <i class="k-icon k-i-loading mr-2" *ngIf="salvando"></i>
            Confirmar Reabertura
          </button>

          <!-- Botão Voltar -->
          <button class="btn btn-link mt-2" (click)="voltarSelecao()" type="button">
            <i class="fas fa-arrow-left"></i> Voltar
          </button>
        </form>

      </div>
    </div>
  </div>
</div>
