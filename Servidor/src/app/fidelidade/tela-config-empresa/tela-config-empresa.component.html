<h4 class="page-title">  <i class="far fa-building"></i>
  Loja</h4>

<div class="card vertical-tabstrip">
  <div class="card-box">
    <kendo-tabstrip #tabstrip  [scrollable]="{ scrollButtons: 'visible' }"
                      [style.width.%]="100">
      <kendo-tabstrip-tab [selected]="true">
        <ng-template kendoTabTitle>
          <i class="fas fa-truck mr-2"></i>
          Formas de entrega
        </ng-template>
        <ng-template kendoTabContent>
          <app-form-formasdeentrega #frmFormaEntrega [empresa]="empresa" ></app-form-formasdeentrega>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab  id="horarios" [title]="nomeTabHorario" >
        <ng-template kendoTabTitle>
          <i class="fas fa-clock mr-2"></i>
          {{nomeTabHorario}}
        </ng-template>
        <ng-template kendoTabContent>
          <h4>Horário de funcionamento</h4>

          <p>Configure os dias e horários da loja </p>

          <div class="form-group mb-2  " style="max-width: 350px">
            <label  >Fuso Horário</label>

            <kendo-combobox   name="fusoHorario" [data]="fusos"   (valueChange)="selecionouFuso()"
                              class="form-control" appAutoFocus   [allowCustom]="false" [required]="true"
                              #cbFusoHorario="ngModel" [(ngModel)]="fusoHorario"
                              [textField]="'descricao'">
            </kendo-combobox>
          </div>


          <kendo-tabstrip   class="nav-bordered horarios " #tabHorarios    [scrollable]="{ scrollButtons: 'visible' }"  >
            <kendo-tabstrip-tab [selected]="true">
              <ng-template kendoTabTitle>
                <i class="fas fa-business-time mr-2"></i>
                Horários da loja para pedidos
              </ng-template>
              <ng-template kendoTabContent>
                <app-cad-horariosfuncionamento [empresa]="empresa"></app-cad-horariosfuncionamento>
              </ng-template>
            </kendo-tabstrip-tab>

            <kendo-tabstrip-tab>
              <ng-template kendoTabTitle>
                <i class="fas fa-pause-circle mr-2"></i>
                Pausa Programada
              </ng-template>
              <ng-template kendoTabContent>
                <app-tela-pausas-programadas [empresa]="empresa"></app-tela-pausas-programadas>
              </ng-template>
            </kendo-tabstrip-tab>
          </kendo-tabstrip>

        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-utensils mr-2"></i>
          Cardápio
        </ng-template>
        <ng-template kendoTabContent>
          <app-upload-cardapio [empresa]="empresa"></app-upload-cardapio>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-print mr-2"></i>
          Impressão
        </ng-template>
        <ng-template kendoTabContent>
          <app-config-impressao [empresa]="empresa"></app-config-impressao>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab id="formaspagamento" [title]="nomeTabFormasPagamento" *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-credit-card mr-2"></i>
          {{nomeTabFormasPagamento}}
        </ng-template>
        <ng-template kendoTabContent>
          <app-ativar-formas-de-pagamento  *ngIf="empresa.exibirBandeiras"></app-ativar-formas-de-pagamento>

          <app-cadastro-formas-de-pagamento *ngIf="!empresa.exibirBandeiras"></app-cadastro-formas-de-pagamento>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-table mr-2"></i>
          Mesas
        </ng-template>
        <ng-template kendoTabContent>
            <app-tela-config-mesas></app-tela-config-mesas>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-ticket-alt mr-2"></i>
          Cupons
        </ng-template>
        <ng-template kendoTabContent>
          <app-tela-cupons [exibirTitulo]="false"></app-tela-cupons>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos')">
        <ng-template kendoTabTitle>
          <i class="fas fa-percent mr-2"></i>
          Promoções
        </ng-template>
        <ng-template kendoTabContent>
          <app-tela-promocoes [exibirTitulo]="false"></app-tela-promocoes>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab>
        <ng-template kendoTabTitle>
          <i class="fas fa-plus-circle mr-2"></i>
          Adicionais De Pedido
        </ng-template>
        <ng-template kendoTabContent>
          <app-cad-adicionais-pedido #frmFormaEntrega></app-cad-adicionais-pedido>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab *ngIf="moduloAtivo('pedidos') && moduloAtivo('fidelidade')">
        <ng-template kendoTabTitle>
          <i class="fas fa-star mr-2"></i>
          Fidelidade
        </ng-template>
        <ng-template kendoTabContent>

          <kendo-tabstrip   class="nav-bordered fidelidade "   >
            <kendo-tabstrip-tab [selected]="true">
              <ng-template kendoTabTitle>
                <i class="fas fa-store-alt mr-2"></i>
                Integração com Loja
              </ng-template>
              <ng-template kendoTabContent>
                <app-form-integracaofidelidade [usuario]=usuario [empresa]="empresa"></app-form-integracaofidelidade>
              </ng-template>
            </kendo-tabstrip-tab>

            <kendo-tabstrip-tab>
              <ng-template kendoTabTitle>
                <i class="fas fa-layer-group mr-2"></i>
                Planos
              </ng-template>
              <ng-template kendoTabContent>
                <app-planos [empresa]="empresa"></app-planos>
              </ng-template>
            </kendo-tabstrip-tab>

            <kendo-tabstrip-tab>
              <ng-template kendoTabTitle>
                <i class="fas fa-tasks mr-2"></i>
                Atividades
              </ng-template>
              <ng-template kendoTabContent>
                <app-atividades [empresa]="empresa"></app-atividades>
              </ng-template>
            </kendo-tabstrip-tab>

            <kendo-tabstrip-tab>
              <ng-template kendoTabTitle>
                <i class="fas fa-gift mr-2"></i>
                Brindes
              </ng-template>
              <ng-template kendoTabContent>
                <app-brindes [empresa]="empresa"></app-brindes>
              </ng-template>
            </kendo-tabstrip-tab>

          </kendo-tabstrip>

        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab  [title]="'Integração com Pdv'" *ngIf="moduloAtivo('pedidos')  ">
        <ng-template kendoTabTitle>
          <i class="fas fa-cash-register mr-2"></i>
          Integração com Pdv
        </ng-template>
        <ng-template kendoTabContent>
          <app-form-integracaopedidos  [empresa]="empresa"></app-form-integracaopedidos>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab [title]="'Integração com Delivery'" *ngIf="moduloAtivo('pedidos')  " >
        <ng-template kendoTabTitle>
          <i class="fas fa-motorcycle mr-2"></i>
          Integração com Delivery
        </ng-template>
        <ng-template kendoTabContent>
          <app-form-integracaodelivery  [empresa]="empresa"></app-form-integracaodelivery>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab [title]="'Integração com Ifood'" *ngIf="moduloAtivo('pedidos')   " >
        <ng-template kendoTabTitle>
          <i class="fas fa-store mr-2"></i>
          Integração com Ifood
        </ng-template>
        <ng-template kendoTabContent>
          <app-tela-integracao-ifood  [empresa]="empresa"></app-tela-integracao-ifood>
        </ng-template>
      </kendo-tabstrip-tab>

      <kendo-tabstrip-tab>
        <ng-template kendoTabTitle>
          <i class="fas fa-images mr-2"></i>
          Fotos
        </ng-template>
        <ng-template kendoTabContent>
          <app-fotos #appFotos   [empresa]="empresa" [fotosDoAmbiente]="false"></app-fotos>
        </ng-template>
      </kendo-tabstrip-tab>
    </kendo-tabstrip>
  </div>
</div>
