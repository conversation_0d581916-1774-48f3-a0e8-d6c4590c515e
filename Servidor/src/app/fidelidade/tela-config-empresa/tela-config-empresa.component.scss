@media (max-width: 768px) {
  .card .card-box{
    padding: 1.5em 0 !important;
  }

}

@media (min-width: 1500px) {
  .vertical-tabstrip{

    >.card-box{
      padding-left: 0;
      padding-top: 0;

      ::ng-deep  >.k-tabstrip{
        display: flex;
        flex-direction: row;
        min-height: 600px; /* Ajuste conforme necessário */
        ::ng-deep >.k-tabstrip-items-wrapper{

          /* Modifica o contêiner para empilhar os itens verticalmente */
          ::ng-deep .k-tabstrip-items {
            display: flex !important;
            flex-direction: column !important; /* Alinha os itens verticalmente */
            width: auto !important; /* Ajuste conforme necessário */
            min-width: 230px;
            margin: 5px;
          }

          /* Estiliza os itens para que se ajustem à nova orientação */
          ::ng-deep .k-tabstrip-items .k-item {
            margin: 0 !important;
            padding: 10px !important; /* Ajuste o espaçamento conforme necessário */
            border: 1px solid transparent !important; /* Mantenha a borda transparente ou ajuste conforme necessário */
            position: relative !important;
            flex-shrink: 0 !important;
            display: block !important; /* Muda de flex para block para ocupar toda a largura disponível */
            width: 100% !important; /* Certifica-se de que os itens ocupem toda a largura disponível */
            box-sizing: border-box !important; /* Inclui padding e border no cálculo da largura */
            min-width: 191px;
          }

          /* Opcional: Estilo para o item ativo */
          ::ng-deep .k-tabstrip-items .k-item.k-active {
            background-color: #f0f0f0 !important; /* Destaque para o item ativo */
            font-weight: bold !important; /* Destaca o texto do item ativo */
            border-radius: 0px;
          }

          ::ng-deep   .k-item:focus,  .k-item.k-focus {
            box-shadow: none !important;
          }

          ::ng-deep .k-tabstrip-prev, .k-tabstrip-next{
            width: 0 !important;
            padding: 0 !important;
            margin: 0 !important;
          }
        }
      }
    }

  }
}

