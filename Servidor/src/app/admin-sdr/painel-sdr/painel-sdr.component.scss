.fechar_loja {
  display: none;
}

::ng-deep .k-notification-group{
  position: fixed !important;
  top: 72px !important;

  .k-notification-content{
    font-weight: bold;
  }
}

.bloqueio{
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, .5);
  z-index: 1000;
  overflow: visible;
  top: 0;
  left: 0;
}

.modal-dialog.modal-lg{
  z-index: 10000;
}

.navbar-brand {
  display: none;
  max-width: 80vw;
  color: #fff;
  position: absolute;
  top: 15px;
  left: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.slimscroll {

  overflow-x: hidden;
  overflow-y: scroll;

}


.slimscroll .valor{
  position: absolute;
  right: 15px;
  top: 15px;
  color: #6db31b !important;
}

.notification-list .notify-item {
  padding-left: 10px;
}
.notify-icon{
  width: 60px;
}

.notify-icon small{
  position: relative;
  top: -5px;

}

.bloco_status{
  line-height: 18px !important;
  margin-top: 15px;

}

.bloco_status.maior{
  width: 270px;
}

::ng-deep .k-notification-info {
  background-color: #3a44b9;
  .k-notification-wrap {
    height: 50px;
    padding-top: 17px;
  }
}

@media (min-width: 600px) {
  .navbar-brand {
    max-width: 60vw;
  }
}

@media (max-width: 768px) {
  .container-fluid{
    padding-right: 5px !important;
    padding-left: 5px !important;
  }

  .navbar-brand {
    display: block;
  }

  .noti-scroll .dropdown-item{
    padding: 0;


  }

  .navbar-custom ::ng-deep .dropdown-lg {
    width: 270px !important;
  }

  .notify-icon{
    width: 45px;
  }
}

@media (min-width: 990px){
  ::ng-deep .modal-lg, .modal-xl {
    max-width: 1120px !important;
  }

  .fechar_loja {
    display: block;
  }
}


