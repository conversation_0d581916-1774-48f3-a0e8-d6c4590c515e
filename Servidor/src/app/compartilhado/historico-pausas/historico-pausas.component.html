<div class="historico-pausas">
  <kendo-grid
    [data]="gridView"
    [pageSize]="pageSize"
    [skip]="skip"
    [pageable]="true"
    [sortable]="true"  [loading]="carregando"
    (pageChange)="onPageChange($event)">
    <kendo-grid-column field="descricao" title="Descrição" [width]="150">
      <ng-template kendoGridCellTemplate let-dataItem>
           <span class="text-primary"><b>{{dataItem.descricao}}</b></span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="periodo" title="Periodo" [width]="160">
      <ng-template kendoGridCellTemplate let-dataItem>
        {{dataItem.dataInicio | date:'dd/MM/yy HH:mm'}}
         <span *ngIf="dataItem.dataFim">
             até   {{dataItem.dataFim | date:'dd/MM/yy HH:mm'}}
         </span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="mensagem" title="Mensagem Loja" [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <div class="mensagem-truncada" kendoTooltip title="{{dataItem.mensagem}}">
          <span class="font-11">"<i>{{dataItem.mensagem}}</i>"</span>
        </div>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="operadorCadastrou.nome" title="Operador" [width]="200">
      <ng-template kendoGridCellTemplate let-dataItem>
        <span class="font-12">
           <ng-container *ngIf="dataItem.operadorCadastrou">
             <b>{{dataItem.operadorCadastrou.nome}}</b> <br> </ng-container>
         <ng-container *ngIf="dataItem.operadorCancelou">
           <span  class="text-danger">cancelado feito por </span> <b>{{dataItem.operadorCancelou.nome}}</b>
               em {{dataItem.dataCancelamento | date: 'dd/MM/yy HH:mm'}}
             </ng-container>
        </span>
      </ng-template>
    </kendo-grid-column>
    <kendo-grid-column field="removida" title="Cancelada" [width]="100">
      <ng-template kendoGridCellTemplate let-dataItem>
            <span [class]="dataItem.cancelada ? 'text-danger' : 'text-success'">
              <b> {{dataItem.cancelada ? 'Sim' : 'Não'}}</b>
            </span>
      </ng-template>
    </kendo-grid-column>
  </kendo-grid>
</div>
