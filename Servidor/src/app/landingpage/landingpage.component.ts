import {Component, ElementRef, OnInit, ViewChild} from '@angular/core';
import {NgForm} from "@angular/forms";
import {HttpClient} from "@angular/common/http";

@Component({
  selector: 'app-landingpage',
  templateUrl: './landingpage.component.html',
  styleUrls: ['./landingpage.component.scss']
})
export class LandingpageComponent implements OnInit {
  @ViewChild('bgContato', { static: true}) private bgContato: ElementRef;
  @ViewChild('nome', {static: true}) nome: ElementRef;
  @ViewChild('frm', {static: true}) frm: NgForm;
  contato: any = {};
  erro: string;
  salvando: boolean;
  constructor(private httpClient: HttpClient) { }

  ngOnInit() {
  }

  solicitarContato() {
    window.scrollTo(0, document.body.scrollHeight - 700);
    this.nome.nativeElement.focus();
  }

  onSubmit() {
   delete this.erro;
   if(this.frm.valid){
      this.salvando = true;
      this.httpClient.post('/empresas/contato', this.contato).toPromise().then( (resposta: any ) => {
        this.salvando = false;
        if(resposta.sucesso){
          this.contato.salvo = true;
        }else{
          this.erro = resposta.mensagem;
        }
      }).catch( erro => {
        this.salvando = false;
         this.erro = 'Falha ao salvar contato! '
      })
   } else {
     this.erro = 'Verifique os campos obrigatórios'
   }
  }
}
