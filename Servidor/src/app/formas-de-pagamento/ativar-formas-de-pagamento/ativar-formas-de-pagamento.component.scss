.btn-migrar {
  background: linear-gradient(45deg, #dc3545, #c82333);
  border: none;
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);

  &:hover {
    background: linear-gradient(45deg, #c82333, #bd2130);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  }

  i {
    font-size: 1rem;
  }
}


::ng-deep .btn-nova-forma {
  &.k-button {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    width: auto;
    min-width: 250px;

    &:hover {
      background: linear-gradient(45deg, #1976D2, #1565C0);
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .k-icon {
      display: none; // Remove o ícone padrão do Kendo
    }
  }

  small {
    font-size: 0.8rem;
    opacity: 0.9;
  }
}
