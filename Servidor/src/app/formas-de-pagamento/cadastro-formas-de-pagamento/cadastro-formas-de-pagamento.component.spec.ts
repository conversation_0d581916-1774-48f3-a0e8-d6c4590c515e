import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { CadastroFormasDePagamentoComponent } from './cadastro-formas-de-pagamento.component';

describe('CadastroFormasDePagamentoComponent', () => {
  let component: CadastroFormasDePagamentoComponent;
  let fixture: ComponentFixture<CadastroFormasDePagamentoComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ CadastroFormasDePagamentoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CadastroFormasDePagamentoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
