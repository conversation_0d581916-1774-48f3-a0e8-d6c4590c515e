<div class="modal-content">
  <div class="modal-header">
    <h4 class="modal-title" id="myModalLabel">
      Regras
    </h4>
    <button type="button" class="close" aria-label="Close" (click)="fecheModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div #pnl class="modal-body">

    <h3>{{regra.titulo}}</h3>

    <p *ngIf="regra.descricao">{{regra.descricao}}</p>


    <ul *ngFor="let plano of regra.planos; let i = index;"  class=" list-group mt-2" >
      <h5 *ngIf="regra.planos.length > 1">{{i+1}} - {{plano.nome}}</h5>
      <p> {{plano.descricao}}</p>
      <li *ngFor="let item of plano.trocas" class="list-group-item">
        <p>{{item}}</p>
      </li>

      <div class="extras">
        <p *ngFor="let extra of plano.extras" class="mt-2">
          {{extra}}
        </p>
      </div>

    </ul>

   <div class="extras">
     <p *ngFor="let extra of regra.extras"  >
       {{extra}}
     </p>
   </div>
  </div>

</div>
