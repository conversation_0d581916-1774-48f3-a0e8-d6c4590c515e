import {AfterViewInit, Component, OnInit} from '@angular/core';
import StatusResponse = facebook.StatusResponse;
import {InstagramService} from "../../services/instagram.service";

@Component({
  selector: 'app-config-instagram',
  templateUrl: './config-instagram.component.html',
  styleUrls: ['./config-instagram.component.scss']
})
export class ConfigInstagramComponent implements OnInit, AfterViewInit {
  conectado = false;
  contas: Array<any>;
  accessToken: string;
  contaSelecionada: any;
  carregandoInstagram = false;
  mensagem = '';
  msgCarregando = '';
  dadosInstagram: any;
  nenhumaPaginaEncontrada = false;
  contaConectando: any = null;
  desconectando = false;

  constructor(private instagramService: InstagramService) {
    // ✅ Inicializa variáveis para evitar problemas de estado
    this.contas = null;
    this.contaSelecionada = null;
    this.dadosInstagram = null;
  }

  ngOnInit(): void {
  }

  ngAfterViewInit(): void {
    this.instagramService.obtenhaContaConectada().then((contaInsta) => {
      this.dadosInstagram = contaInsta;
      FB.getLoginStatus((response) => {
        if (response.status === "connected") {
          this.conectado = true;
          this.contaSelecionada = contaInsta;

          // ✅ Se está conectado mas não tem conta selecionada, tenta recarregar páginas
          if (!this.contaSelecionada && this.dadosInstagram?.accessToken) {
            this.tentaRecarregarPaginas();
          } else if (!this.contaSelecionada && !this.dadosInstagram?.accessToken) {
            // ✅ Se não tem token, força reconexão
            this.conectado = false;
          }

          if (this.contaSelecionada) {
            this.mensagem = 'Chatbot instalado. Teste conversando com sua conta no instagram.'
          }
        } else if (response.status === 'unknown') {
          this.conectado = false;
          if (contaInsta) {
            this.desconectar();
          }
        }

        setTimeout(() => {
          FB.XFBML.parse();
        });
      });
    });
  }

  private tentaRecarregarPaginas() {
    if (this.dadosInstagram?.accessToken) {
      this.carregandoInstagram = true;
      this.msgCarregando = 'Recarregando suas páginas do Facebook...';
      this.contas = null; // ✅ Limpa contas para forçar recarregamento

      this.obtenhaContasInstagram(this.dadosInstagram.accessToken)
        .then(() => {
          // ✅ Se não encontrou contas ou não tem conta selecionada, força reconexão
          if (!this.contas || this.contas.length === 0) {
            this.conectado = false;
            this.carregandoInstagram = false;
          }
        })
        .catch((erro) => {
          console.error('Erro ao recarregar páginas:', erro);
          this.carregandoInstagram = false;
          this.conectado = false; // ✅ Força reconexão se falhar
          this.contas = null;
        });
    } else {
      // ✅ Se não tem token, força reconexão
      this.conectado = false;
    }
  }

  desconectar() {
    this.desconectando = true;

    this.instagramService.desconectarConta().then(() => {
      FB.logout((response) => {
        this.conectado = false;
        this.contaSelecionada = null;
        this.desconectando = false;
        this.contas = null;
        this.accessToken = null;
        this.dadosInstagram = null;

        setTimeout(() => {
          FB.XFBML.parse();
        })
      });
    }).catch(erro => {
      this.desconectando = false;
      console.error('Erro ao desconectar:', erro);
    });
  }

  atualizarToken() {
    this.contaSelecionada = null;
    this.contas = null; // ✅ Limpa contas para forçar recarregamento

    if (this.dadosInstagram && this.dadosInstagram.accessToken) {
      this.carregandoInstagram = true;
      this.msgCarregando = 'Atualizando informações das páginas...';

      this.obtenhaContasInstagram(this.dadosInstagram.accessToken)
        .catch((erro) => {
          console.error('Erro ao atualizar token:', erro);
          this.carregandoInstagram = false;
          this.conectado = false; // ✅ Força reconexão se falhar
        });
    } else {
      this.conectar();
    }
  }

  conectar() {
    this.contaSelecionada = null;
    this.contas = null;
    this.mensagem = '';
    this.nenhumaPaginaEncontrada = false;

    // ✅ Exibe loading imediatamente quando o usuário clica
    this.carregandoInstagram = true;
    this.msgCarregando = 'Abrindo janela de autenticação do Facebook...';

    // ✅ Timeout para casos onde o Facebook SDK não responde
    const timeoutId = setTimeout(() => {
      if (this.carregandoInstagram) {
        this.carregandoInstagram = false;
        this.conectado = false;
        this.mensagem = 'Tempo limite excedido. Tente novamente.';
        console.warn('Timeout na autenticação do Facebook');
      }
    }, 30000); // 30 segundos

    FB.login((response: StatusResponse) => {
      // ✅ Limpa o timeout pois recebemos resposta
      clearTimeout(timeoutId);

      console.log('Resposta do Facebook Login:', response);

      // ✅ Verifica se conectou E se tem authResponse válido
      if (response.status === "connected" && response.authResponse?.accessToken) {
        this.conectado = true;
        this.msgCarregando = 'Processando autenticação do Facebook...';

        this.instagramService.estendaToken(response.authResponse.accessToken).then((resposta) => {
          this.conectou(resposta);
        }).catch((erro) => {
          this.carregandoInstagram = false;
          this.conectado = false;
          this.mensagem = 'Erro ao processar autenticação. Tente novamente.';
          console.error('Erro ao estender token:', erro);
        });

      } else if (response.status === "not_authorized") {
        // ✅ Usuário está logado no Facebook mas não autorizou o app
        this.conectado = false;
        this.carregandoInstagram = false;
        this.mensagem = 'É necessário autorizar o aplicativo para continuar.';
        console.log('Usuário não autorizou o aplicativo');

      } else if (response.status === "unknown") {
        // ✅ Usuário não está logado no Facebook ou cancelou
        this.conectado = false;
        this.carregandoInstagram = false;
        this.mensagem = 'Login cancelado ou não realizado.';
        console.log('Login cancelado ou usuário não logado');

      } else {
        // ✅ Qualquer outro caso inesperado
        this.conectado = false;
        this.carregandoInstagram = false;
        this.mensagem = 'Erro inesperado durante a autenticação.';
        console.log('Status inesperado:', response.status);
      }

      setTimeout(() => {
        FB.XFBML.parse();
      });
    }, {
      scope: 'email,public_profile,instagram_basic,instagram_manage_messages,pages_show_list,pages_manage_metadata'
    });
  }

  conectou(accessToken: string) {
    this.accessToken = accessToken;

    this.msgCarregando = 'Buscando suas páginas do Facebook...';

    this.obtenhaContasInstagram(this.accessToken).then((resp) => {
      const contasBusiness = this.contas?.filter(c => c.status === 'business_ativo') || [];

      if (contasBusiness.length === 1) {
        const conta = contasBusiness[0];
        this.mensagem = "Instalando chatbot no instagram selecionado."

        this.instagramService.obtenhaTokenDePagina(conta, accessToken, conta.tokenDePagina)
          .then((resposta) => {
            this.contaSelecionada = conta;
            this.mensagem = 'Chatbot instalado. Teste conversando com sua conta no instagram.'
          })
          .catch((erro) => {
            console.error('Erro ao obter token de página:', erro);
            this.mensagem = 'Erro ao configurar chatbot. Por favor, tente novamente.';
            this.carregandoInstagram = false;
          });
      }
    }).catch((erro) => {
      this.carregandoInstagram = false;
      this.conectado = false;
      this.contas = null;
      console.error('Erro ao obter contas Instagram:', erro);
    });
  }

  obtenhaContasInstagram(accessToken: string): Promise<any> {
    this.carregandoInstagram = true;
    this.nenhumaPaginaEncontrada = false;

    return new Promise((resolve => {
      this.contas = [];

      if (!this.msgCarregando) {
        this.msgCarregando = 'Buscando suas páginas do Facebook...';
      }
      FB.api(
        '/me/accounts?fields=id,name,username,access_token,category,category_list,' +
        'instagram_business_account&access_token=' + accessToken, (response) => {

        const paginas = response.data;

        if (!paginas || paginas.length === 0) {
          this.carregandoInstagram = false;
          this.nenhumaPaginaEncontrada = true;
          resolve(null);
          return;
        }

        let requests = [];
        let paginasComInstagram = [];

        for (let i = 0; i < paginas.length; i++) {
          const pagina = paginas[i];

          if (pagina.instagram_business_account) {
            const igId = pagina.instagram_business_account.id;
            requests.push({method: 'GET', relative_url: igId + '?fields=name,profile_picture_url'});
            paginasComInstagram.push({
              ...pagina,
              indiceRequest: requests.length - 1,
              temInstagram: true,
              ehBusiness: true
            });
          } else {
            this.contas.push({
              idPagina: pagina.id,
              nomePagina: pagina.name,
              tokenDePagina: pagina.access_token,
              temInstagram: false,
              ehBusiness: false,
              status: 'sem_instagram',
              mensagemStatus: 'Instagram não conectado a esta página',
              tipoAcao: 'conectar'
            });
          }
        }

        if (requests.length === 0) {
          this.carregandoInstagram = false;
          resolve(null);
          return;
        }

        this.msgCarregando = 'Verificando contas do Instagram...';
        FB.api('/?access_token=' + accessToken, 'POST', {
          batch: requests
        }, (responseInsta: any) => {
          this.carregandoInstagram = false;

          for (let i = 0; i < responseInsta.length; i++) {
            const respInsta = responseInsta[i];
            const paginaCorrespondente = paginasComInstagram.find(p => p.indiceRequest === i);

            if (!paginaCorrespondente) continue;

            if (respInsta.code === 200) {
              const contaInstagram = JSON.parse(respInsta.body);
              this.contas.push({
                tokenDePagina: paginaCorrespondente.access_token,
                nomeInstagram: contaInstagram.name,
                nomePagina: paginaCorrespondente.name,
                igid: paginaCorrespondente.instagram_business_account.id,
                imageProfileUrl: contaInstagram.profile_picture_url,
                idPagina: paginaCorrespondente.id,
                temInstagram: true,
                ehBusiness: true,
                status: 'business_ativo',
                mensagemStatus: 'Conta Business ativa',
                tipoAcao: 'selecionar'
              });
            } else {
              this.contas.push({
                idPagina: paginaCorrespondente.id,
                nomePagina: paginaCorrespondente.name,
                tokenDePagina: paginaCorrespondente.access_token,
                temInstagram: true,
                ehBusiness: false,
                status: 'instagram_pessoal',
                mensagemStatus: 'Instagram pessoal - Precisa converter para Business',
                tipoAcao: 'converter'
              });
            }
          }

          resolve(null);
        });
      });
    }));
  }

  selecionouInstagram(conta: any) {
    this.contaConectando = conta;
    conta.msgErro = null;

    this.instagramService.obtenhaTokenDePagina(conta, this.accessToken, conta.tokenDePagina)
      .then((resposta) => {
        this.contaSelecionada = conta;
        this.contaConectando = null;
        this.mensagem = 'Chatbot instalado com sucesso! Teste conversando com sua conta no Instagram.';
      })
      .catch(erro => {
        conta.msgErro = erro;
        this.contaConectando = null;
      });
  }
}
