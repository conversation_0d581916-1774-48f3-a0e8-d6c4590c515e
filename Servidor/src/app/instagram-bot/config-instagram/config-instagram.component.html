<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">
          <i class="mdi mdi-instagram mr-2"></i>
          Configuração Instagram BOT2
        </h4>
        <p class="text-muted">Configure a integração do seu chatbot com o Instagram Business</p>
      </div>
    </div>
  </div>

  <!-- Status da Conexão -->
  <div class="row mb-4">
    <div class="col-12">
      <!-- Card Inicial - Não Conectado -->
      <div class="card" *ngIf="!conectado">
        <div class="card-body text-center py-4">
          <div class="mb-3">
            <i class="mdi mdi-facebook text-primary" style="font-size: 48px;"></i>
          </div>
          <h5 class="card-title">Conecte sua conta do Facebook</h5>
          <p class="card-text text-muted">
            Para configurar o Instagram BOT, você precisa conectar sua conta do Facebook que possui as páginas com Instagram Business.
          </p>
          <div class="mt-3">
            <button class="btn btn-primary btn-lg mr-2" (click)="conectar()">
              <i class="mdi mdi-facebook mr-2"></i>
              Continuar Com Facebook
            </button>
          </div>
        </div>
      </div>

      <!-- Card de Reconexão Necessária -->
      <div class="card border-warning" *ngIf="conectado && !contaSelecionada && !carregandoInstagram && (!contas || contas.length === 0)">
        <div class="card-body text-center py-4">
          <div class="mb-3">
            <i class="mdi mdi-refresh-circle text-warning" style="font-size: 48px;"></i>
          </div>
          <h5 class="card-title">Reconexão Necessária</h5>
          <p class="card-text text-muted">
            Parece que sua sessão expirou ou os dados foram perdidos. Por favor, reconecte sua conta do Facebook para continuar.
          </p>
          <div class="mt-3">
            <button class="btn btn-primary btn-lg mr-2" (click)="conectar()">
              <i class="mdi mdi-facebook mr-2"></i>
              Reconectar Facebook
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Botões de Ação (sempre visível quando conectado) -->
  <div class="row mb-4" *ngIf="conectado">
    <div class="col-12">
      <div class="card actions-toolbar">
        <div class="card-body">
          <h6 class="card-title mb-4">
            <i class="mdi mdi-cog mr-2"></i>
            Ações Disponíveis
          </h6>
          <div class="actions-container">
            <button class="btn btn-primary btn-action" (click)="atualizarToken()" [disabled]="carregandoInstagram || desconectando">
              <span *ngIf="!carregandoInstagram">
                <i class="mdi mdi-refresh"></i>
                <span class="btn-text">Atualizar Token</span>
              </span>
              <span *ngIf="carregandoInstagram" class="loading-content">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="sr-only">Carregando...</span>
                </div>
                <span class="btn-text">Atualizando...</span>
              </span>
            </button>

            <button class="btn btn-outline-danger btn-action" (click)="desconectar()" [disabled]="carregandoInstagram || desconectando">
              <span *ngIf="!desconectando">
                <i class="mdi mdi-logout"></i>
                <span class="btn-text">Desconectar Facebook</span>
              </span>
              <span *ngIf="desconectando" class="loading-content">
                <div class="spinner-border spinner-border-sm" role="status">
                  <span class="sr-only">Carregando...</span>
                </div>
                <span class="btn-text">Desconectando...</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Status da Conta Conectada -->
  <div class="row mb-4" *ngIf="conectado && contaSelecionada">
    <div class="col-12">
      <div class="card border-success">
        <div class="card-body">
          <div class="row align-items-center">
            <div class="col-auto">
              <div class="avatar-lg">
                <img [src]="contaSelecionada.imageProfileUrl" class="rounded-circle img-thumbnail" style="width: 64px; height: 64px;">
              </div>
            </div>
            <div class="col">
              <h5 class="card-title text-success mb-1">
                <i class="mdi mdi-check-circle mr-2"></i>
                Instagram BOT Ativo
              </h5>
              <p class="card-text">
                <strong>{{ '@' + contaSelecionada.nomeInstagram }}</strong>
              </p>
              <p class="text-muted mb-0">{{mensagem}}</p>
            </div>
            <div class="col-auto">
              <button class="btn btn-outline-danger"
                      (click)="desconectar()"
                      [disabled]="desconectando">
                <span *ngIf="!desconectando">
                  <i class="mdi mdi-logout mr-2"></i>
                  Desconectar
                </span>
                <span *ngIf="desconectando">
                  <div class="spinner-border spinner-border-sm mr-2" role="status">
                    <span class="sr-only">Carregando...</span>
                  </div>
                  Desconectando...
                </span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading -->
  <div class="row mb-4" *ngIf="carregandoInstagram">
    <div class="col-12">
      <div class="card">
        <div class="card-body text-center py-5">
          <div class="spinner-border text-primary mb-3" role="status">
            <span class="sr-only">Carregando...</span>
          </div>
          <h5>{{msgCarregando}}</h5>
          <p class="text-muted">Aguarde enquanto verificamos suas contas...</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Nenhuma página encontrada -->
  <div class="row" *ngIf="nenhumaPaginaEncontrada && !carregandoInstagram">
    <div class="col-12">
      <div class="card border-warning">
        <div class="card-body text-center py-4">
          <div class="mb-3">
            <i class="mdi mdi-alert-circle text-warning" style="font-size: 48px;"></i>
          </div>
          <h5 class="card-title">Nenhuma página encontrada</h5>
          <p class="card-text">
            Você precisa ter uma página no Facebook para conectar o Instagram BOT.
          </p>
          <div class="mt-3">
            <a href="https://www.facebook.com/pages/create" target="_blank" class="btn btn-primary">
              <i class="mdi mdi-plus mr-2"></i>
              Criar Página no Facebook
            </a>
            <button class="btn btn-outline-secondary ml-2" (click)="atualizarToken()">
              <i class="mdi mdi-refresh mr-2"></i>
              Verificar Novamente
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Lista de Páginas -->
  <div class="row" *ngIf="contas && contas.length > 0 && !carregandoInstagram">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="card-title mb-0">
            <i class="mdi mdi-facebook mr-2"></i>
            Suas Páginas do Facebook
          </h5>
          <p class="text-muted mb-0" *ngIf="!contaSelecionada">Selecione uma página para conectar o Instagram BOT</p>
          <p class="text-success mb-0" *ngIf="contaSelecionada">
            <i class="mdi mdi-check-circle mr-1"></i>
            Página selecionada: <strong>{{contaSelecionada.nomePagina}}</strong>
          </p>
        </div>
        <div class="card-body p-0">
          <div class="list-group list-group-flush">
            <div class="list-group-item" *ngFor="let conta of contas">

              <!-- Página com Instagram Business Ativo -->
              <div class="row align-items-center" *ngIf="conta.status === 'business_ativo'">
                <div class="col-auto">
                  <img [src]="conta.imageProfileUrl" class="rounded-circle" style="width: 56px; height: 56px;">
                </div>
                <div class="col">
                  <h6 class="mb-1">{{conta.nomePagina}}</h6>
                  <p class="mb-1"><strong>{{ '@' + conta.nomeInstagram }}</strong></p>
                  <small class="text-success">
                    <i class="mdi mdi-check-circle mr-1"></i>
                    {{conta.mensagemStatus}}
                  </small>

                  <!-- Mensagem de status durante conexão -->
                  <div *ngIf="contaConectando === conta" class="mt-2">
                    <small class="text-info">
                      <i class="mdi mdi-loading mdi-spin mr-1"></i>
                      Configurando chatbot para esta conta...
                    </small>
                  </div>
                </div>
                <div class="col-auto">
                  <button class="btn btn-success"
                          (click)="selecionouInstagram(conta)"
                          [disabled]="contaConectando === conta">
                    <span *ngIf="contaConectando !== conta">
                      <i class="mdi mdi-check mr-2"></i>
                      Selecionar
                    </span>
                    <span *ngIf="contaConectando === conta">
                      <div class="spinner-border spinner-border-sm mr-2" role="status">
                        <span class="sr-only">Carregando...</span>
                      </div>
                      Conectando...
                    </span>
                  </button>
                </div>
              </div>

              <!-- Página sem Instagram -->
              <div class="row align-items-center" *ngIf="conta.status === 'sem_instagram'">
                <div class="col-auto">
                  <div class="avatar-md bg-light rounded-circle d-flex align-items-center justify-content-center">
                    <i class="mdi mdi-facebook text-primary" style="font-size: 24px;"></i>
                  </div>
                </div>
                <div class="col">
                  <h6 class="mb-1">{{conta.nomePagina}}</h6>
                  <small class="text-warning">
                    <i class="mdi mdi-alert-circle mr-1"></i>
                    {{conta.mensagemStatus}}
                  </small>
                </div>
                <div class="col-auto">
                  <a href="https://web.facebook.com/business/help/***************?id=332010350818053" target="_blank" class="btn btn-outline-primary btn-sm">
                    <i class="mdi mdi-link-variant mr-2"></i>
                    Como Conectar
                  </a>
                </div>
              </div>

              <!-- Página com Instagram Pessoal -->
              <div class="row align-items-center" *ngIf="conta.status === 'instagram_pessoal'">
                <div class="col-auto">
                  <div class="avatar-md bg-light rounded-circle d-flex align-items-center justify-content-center">
                    <i class="mdi mdi-instagram text-danger" style="font-size: 24px;"></i>
                  </div>
                </div>
                <div class="col">
                  <h6 class="mb-1">{{conta.nomePagina}}</h6>
                  <small class="text-danger">
                    <i class="mdi mdi-alert-circle mr-1"></i>
                    {{conta.mensagemStatus}}
                  </small>
                </div>
                <div class="col-auto">
                  <a href="https://help.instagram.com/***************" target="_blank" class="btn btn-outline-warning btn-sm">
                    <i class="mdi mdi-account-convert mr-2"></i>
                    Como Converter
                  </a>
                </div>
              </div>

              <!-- Erro na conta -->
              <div class="alert alert-danger mt-2 mb-0" *ngIf="conta.msgErro">
                <i class="mdi mdi-alert-circle mr-2"></i>
                <strong>Erro:</strong> {{conta.msgErro}}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Ajuda e Documentação -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card border-info">
        <div class="card-header bg-light">
          <h6 class="card-title mb-0">
            <i class="mdi mdi-help-circle mr-2"></i>
            Precisa de Ajuda?
          </h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <h6>Como criar uma página no Facebook</h6>
              <p class="text-muted small">Aprenda a criar uma página para sua empresa</p>
              <a href="https://www.facebook.com/business/help/104002523024878" target="_blank" class="btn btn-outline-info btn-sm">
                Ver Tutorial
              </a>
            </div>
            <div class="col-md-4">
              <h6>Como conectar Instagram à página</h6>
              <p class="text-muted small">Vincule sua conta do Instagram à página do Facebook</p>
              <a href="https://web.facebook.com/business/help/***************?id=332010350818053" target="_blank" class="btn btn-outline-info btn-sm">
                Ver Tutorial
              </a>
            </div>
            <div class="col-md-4">
              <h6>Como converter para conta Business</h6>
              <p class="text-muted small">Transforme sua conta pessoal em conta comercial</p>
              <a href="https://help.instagram.com/***************" target="_blank" class="btn btn-outline-info btn-sm">
                Ver Tutorial
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


