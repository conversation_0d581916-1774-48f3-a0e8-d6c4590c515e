.botoes_mais_menos {
  margin-left: 10px;
  background: #fff;
  min-width: 108px;
}

.headerCampo {
  margin-left: -10px;
  margin-right: -10px;
}

.font-14{
  font-size: 14px !important;
}

.radio label {
  font-weight: 500;
}

.radio label::before {
  margin-left: 0px;
}

.radio label::after {
  margin-left: -2px;
}

.btn-outline-light {
  border-color: transparent;
}

.headerCampo .badge {
  font-size: 11px;
}

label {
  font-size: 14px;
  color: var(--cor-texto-primaria, #333);
}

.k-checkbox, .k-radio {
  width: 22px;
  height: 22px;
}

.nome_opcao {
  margin-left: 10px;
  position: relative;
  top: 1px;
  display: inline-block;
}

.preco-extra {
  margin-top: 4px;
  float:  right;
}

.k-checkbox::before {
  width: 18px;
  height: 18px;
  font-size: 18px;
}

::ng-deep .black_friday_2022 {
  .btn-outline-light {
    border-color: #676767 !important;
    background: #000 !important;
  }

  .botoes_mais_menos {
    background: #000;

    .btn-outline-light {
      border: solid 1px #ccc;
    }
  }
}

::ng-deep .tema-personalizado {
  .btn-outline-light {
    border-color: var(--cor-texto-secundaria, #999) !important;
    background: var(--cor-fundo-site, #fff) !important;
  }

  .botoes_mais_menos {
    background: var(--cor-fundo-site, #fff) !important;

    .btn-outline-light {
      border: solid 1px var(--cor-texto-secundaria, #999);
    }
  }

  .nome_opcao {
    color: var(--cor-texto-primaria, #333) !important;
  }

  label {
    color: var(--cor-texto-primaria, #333) !important;
  }

  .badge-primary {
    background-color: var(--cor-preco-adicional, #007bff) !important;
    border-color: var(--cor-preco-adicional, #007bff) !important;
    color: var(--cor-texto-botao, #fff) !important;
  }
}
