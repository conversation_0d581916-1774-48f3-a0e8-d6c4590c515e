<h4 _ngcontent-mki-c5="" class="page-title">
  <i class="far fa-id-card"></i>
  Planos
</h4>

<div class="mt-2"></div>

<kendo-grid [data]="gruposDeLojas"
            [style]=""
            [loading]="carregando"
            [scrollable]="'none'" >
  <ng-template kendoGridToolbarTemplate [position]="'top'">
    <button class="k-button btn-primary" (click)="novoGrupoDeLojas()">
      Novo Grupo De Lojas
    </button>

  </ng-template>
  <kendo-grid-messages
    pagerPage="Página"
    pagerOf="de"
    pagerItems="itens"
    noRecords="Nenhum grupo cadastrado"
    loading="Carregando"
    pagerItemsPerPage="ítems por página"
  >
  </kendo-grid-messages>

  <kendo-grid-column title="ID" field="id"    [width]="40"></kendo-grid-column>

  <kendo-grid-column title="Nome"    >
    <ng-template kendoGridCellTemplate let-grupoDeLojas let-rowIndex="rowIndex">
      <span><b>{{grupoDeLojas.nome}}</b></span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Url"    >
    <ng-template kendoGridCellTemplate let-grupoDeLojas let-rowIndex="rowIndex">
      <a class="alert-link" [href]="'https://' + grupoDeLojas.hostname" target="_blank"><i class="fas fa-link"></i> {{grupoDeLojas.hostname}}</a>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Descrição"    >
    <ng-template kendoGridCellTemplate let-grupoDeLojas let-rowIndex="rowIndex">
      <span class="text-muted"> <b>{{grupoDeLojas.descricao}}</b> </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column    [width]="60"  >
    <ng-template kendoGridCellTemplate let-grupoDeLojas let-rowIndex="rowIndex">
      <i class="fa fa-edit fa-lg cpointer" (click)="editarGrupoDeLojas(grupoDeLojas)"></i>
    </ng-template>
  </kendo-grid-column>


</kendo-grid>
