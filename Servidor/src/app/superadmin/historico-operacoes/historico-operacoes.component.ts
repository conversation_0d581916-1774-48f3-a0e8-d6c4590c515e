import {Component, Input, OnInit} from '@angular/core';
import {SortDescriptor} from "@progress/kendo-data-query";
import {EmpresasService} from "../services/empresas.service";
import {PageChangeEvent} from "@progress/kendo-angular-pager";
import {DialogRef, DialogService} from "@progress/kendo-angular-dialog";
import {
  PayloadJsonViewComponent
} from "../../pedidos/tela-pedidos-erros-importar/payload-json-view/payload-json-view.component";
import {KendoPopupUtils} from "../../lib/KendoPopupUtils";

@Component({
  selector: 'app-historico-operacoes',
  templateUrl: './historico-operacoes.component.html',
  styleUrls: ['./historico-operacoes.component.scss']
})
export class HistoricoOperacoesComponent implements OnInit {
  @Input()
  public empresa: any;


  historicoResultSet: any[];
  public pageSizes = false;
  public previousNext = true;
  tamanhoPagina = 20;
  total: any;
  loading = true;
  page = 0;
  public buttonCount = 5;
  public info = true;
  public type: 'numeric' | 'input' = 'numeric';
  filtro: any = {};
  erroBloqueio: boolean;
  erroRemover: string;
  sort: SortDescriptor[] = [{
    field: 'nome'
  }];

  private filtroAtual: any;
  operacoes: any = [];
  tiposDeObjeto: any = [
    {
      id: 'Produto',
      nome: 'Produto'
    },
    {
      id: 'Categoria',
      nome: 'Categoria'
    },
    {
      id: 'FormaDeEntregaEmpresa',
      nome: 'Forma de Entrega'
    },
    {
      id: 'Cupom',
      nome: 'Cupom'
    },
    {
      id: 'HorarioFuncionamento',
      nome: 'Horário de Funcionamento'
    },
    {
      id: 'Contrato',
      nome: 'Contrato'
    }
  ];
  private timeoutBusca: any;
  inicio = 0;


  constructor(private empresaService: EmpresasService,
              private dialogService: DialogService) { }

  ngOnInit(): void {
    this.carregueOperacoes();
    this.carregueHistorico({ i: this.inicio, t: this.tamanhoPagina})
  }

  sortChange($event: Array<SortDescriptor>) {

  }

  onFilter(event: Event) {
    if(this.timeoutBusca) clearTimeout(this.timeoutBusca);

    this.timeoutBusca = setTimeout(() => {
      this.filtreHistorico(this.filtro)
    }, 500)
  }

  filtreHistorico(filtro) {
    this.inicio = 0
    filtro.i = 0
    filtro.t = this.tamanhoPagina
    this.carregueHistorico(filtro)
  }

  carregueHistorico(dados) {
    this.empresaService.obtenhaHistorico(this.empresa, dados).then((resposta: any) => {
      let historico = resposta.registros
      let quantidade = resposta.quantidade
      this.historicoResultSet = historico
      this.total = quantidade

      this.loading = false;
    })
  }


  carregueOperacoes(){
    this.empresaService.listeOperacoesHistorico().then((result: any) => {
      this.operacoes = result;
    });
  }

  onPageChange($event: PageChangeEvent) {
    this.inicio = $event.skip;
    if(!this.filtro) this.filtro = {}
    this.filtro.i = this.inicio;
    this.filtro.t = this.tamanhoPagina
    window.scrollTo(0, 0);
    if(this.timeoutBusca) clearTimeout(this.timeoutBusca);
    this.carregueHistorico(this.filtro)
  }


  verJsonHistorico(dataItem: any){

    const windowRef: DialogRef = this.dialogService.open({
      title: null,
      content: PayloadJsonViewComponent,
      minWidth: 250,
      width: window.innerWidth > 600 ? 600 : window.innerWidth,
      maxHeight: window.innerHeight - 100,
      cssClass: 'bsModal'
    });

    KendoPopupUtils.abriuPopupNgBootstrap(windowRef)


    windowRef.content.instance.payload = dataItem.valorNovo;
    windowRef.content.instance.titulo =  dataItem.descricao;

    windowRef.result.subscribe( (result) => {  },
      (a) => { });

    return false;
  }
}
