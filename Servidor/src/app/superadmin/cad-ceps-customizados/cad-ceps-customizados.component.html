<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <div class="page-title-box">
        <h4 class="page-title">
          <i class="fe-map-pin"></i> CEPs Customizados
        </h4>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title"><i class="fe-info mr-1"></i> O que são CEPs customizados?</h5>
          <p class="card-text">
            Os CEPs customizados permitem que você adicione ou ajuste os dados de um CEP específico no sistema, especialmente quando:
          </p>
          <ul>
            <li>Um CEP não é encontrado na api VIA CEP</li>
            <li>Um CEP possui dados incorretos ou desatualizados</li>
          </ul>
          <p class="card-text text-muted">
            <i class="mdi mdi-lightbulb-on-outline mr-1"></i> Os CEPs customizados terão prioridade sobre os CEPs padrão nas buscas do sistema.
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-2">
    <div class="col-md-8">
      <div class="input-group">
        <div class="input-group-prepend">
          <span class="input-group-text" id="basic-addon1"><i class="fe-search"></i></span>
        </div>
        <input
          type="text"
          class="form-control"
          placeholder="Buscar por CEP, logradouro, bairro ou cidade..."
          [(ngModel)]="termoBusca"
          (keyup)="filtrarCeps()"
          aria-label="Buscar CEP"
          aria-describedby="basic-addon1">
        <div class="input-group-append" *ngIf="termoBusca && !buscandoLista">
          <button class="btn btn-light" type="button" (click)="limparBusca()">
            <i class="fe-x"></i>
          </button>
        </div>
        <div class="input-group-append" *ngIf="buscandoLista">
          <span class="input-group-text">
            <i class="k-icon k-i-loading"></i>
          </span>
        </div>
      </div>
    </div>
    <div class="col-md-4 text-right">
      <button class="btn-add-cep" (click)="abrirDrawerNovo()">
        <i class="fe-plus-circle mr-2"></i>
        <span>Adicionar CEP Customizado</span>
      </button>
    </div>
  </div>

  <div class="row mt-3">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <!-- Carregando -->
          <div *ngIf="carregando || buscandoLista" class="text-center my-3">
            <i class="k-icon k-i-loading" style="font-size: 2rem;"></i>
            <p class="mt-2">{{ buscandoLista ? 'Buscando CEPs...' : 'Carregando CEPs...' }}</p>
          </div>

          <!-- Sem CEPs cadastrados -->
          <div *ngIf="!carregando && !buscandoLista && (!ceps || ceps.length === 0) && !termoBusca" class="text-center my-5">
            <i class="k-icon k-i-info" style="font-size: 2rem; color: #888;"></i>
            <p class="mt-2">Não há CEPs customizados cadastrados.</p>
            <button class="btn btn-sm btn-primary mt-3" (click)="abrirDrawerNovo()">
              <i class="fa fa-plus-circle mr-1"></i> Adicionar CEP
            </button>
          </div>

          <!-- Nenhum resultado de busca -->
          <div *ngIf="!carregando && !buscandoLista && termoBusca && (!gridView?.data || gridView?.data?.length === 0)" class="text-center my-5">
            <i class="k-icon k-i-filter" style="font-size: 2rem; color: #888;"></i>
            <p class="mt-2">Nenhum CEP encontrado com os termos da busca.</p>
            <button class="btn btn-sm btn-light mt-3" (click)="limparBusca()">
              <i class="fe-x-circle mr-1"></i> Limpar busca
            </button>
          </div>

          <!-- Grid de resultados -->
          <div *ngIf="!carregando && !buscandoLista && gridView?.data && gridView?.data?.length > 0">
            <kendo-grid
              [data]="gridView"
              [loading]="carregando || buscandoLista"
              [pageSize]="itensPorPagina">
              <kendo-grid-column title="Nº" [width]="60">
                <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                  {{ (paginaAtual - 1) * itensPorPagina + rowIndex + 1 }}
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column title="CEP" [width]="120">
                <ng-template kendoGridCellTemplate let-dataItem>
                  {{ dataItem.cep?.slice(0,2) + '.' + dataItem.cep?.slice(2,5) + '-' + dataItem.cep?.slice(5) }}
                </ng-template>
              </kendo-grid-column>
              <kendo-grid-column field="logradouro" title="Logradouro" [width]="180"></kendo-grid-column>
              <kendo-grid-column field="bairro" title="Bairro" [width]="120"></kendo-grid-column>
              <kendo-grid-column field="cidade" title="Cidade" [width]="120"></kendo-grid-column>
              <kendo-grid-column field="estado" title="UF" [width]="60"></kendo-grid-column>
              <kendo-grid-column field="complemento" title="Compl." [width]="100"></kendo-grid-column>
              <kendo-grid-column title="Ações" [width]="160">
                <ng-template kendoGridCellTemplate let-dataItem>
                  <div class="d-flex justify-content-start align-items-center">
                    <button class="btn-action btn-edit mr-2" (click)="abrirDrawerEditar(dataItem)" title="Editar CEP">
                      <i class="fe-edit-2"></i>
                    </button>
                    <button class="btn-action btn-delete" (click)="removerCep(dataItem)" title="Remover CEP">
                      <i class="fe-trash-2"></i>
                    </button>
                  </div>
                </ng-template>
              </kendo-grid-column>
            </kendo-grid>

            <!-- Paginador -->
            <kendo-datapager
              [style.width.%]="100"
              [pageSize]="itensPorPagina"
              [skip]="(paginaAtual - 1) * itensPorPagina"
              [total]="totalItens"
              (pageChange)="onPageChange($event)"
              *ngIf="totalItens > itensPorPagina">
            </kendo-datapager>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Drawer lateral manual -->
  <div *ngIf="drawerAberto">
    <div class="drawer-backdrop" (click)="fecharDrawer()"></div>
    <div class="drawer-lateral shadow-lg" style="border-radius: 16px 0 0 16px; background-color: #fff;">
      <!-- Cabeçalho estilizado -->
      <div class="drawer-header bg-light py-3 px-4 border-bottom" style="border-radius: 16px 0 0 0;">
        <div class="d-flex justify-content-between align-items-center">
          <h5 class="mb-0 font-weight-bold" style="color: #3e464d;">
            <i class="fe-map-pin mr-2" style="color: #4A6FDC;"></i>
            {{ modoEdicao ? 'Editar CEP' : 'Novo CEP' }}
          </h5>
          <button type="button" class="btn-close" aria-label="Fechar" (click)="fecharDrawer()"
                  style="background: none; border: none; font-size: 20px; color: #6c757d; transition: all 0.2s;">
            <i class="fe-x"></i>
          </button>
        </div>
      </div>

      <!-- Corpo do drawer -->
      <div class="drawer-body px-2 py-2">
        <!-- Alerta de erro -->
        <div *ngIf="mensagemErro" class="alert alert-danger mb-4 d-flex align-items-center"
             style="border-radius: 8px; border-left: 4px solid #dc3545; background-color: rgba(220, 53, 69, 0.05);">
          <i class="fe-alert-circle mr-3" style="font-size: 1.3rem; color: #dc3545;"></i>
          <span style="font-weight: 500;">{{mensagemErro}}</span>
        </div>

        <!-- Formulário modernizado -->
        <form #cepForm="ngForm" (ngSubmit)="salvarCep()" [ngClass]="{'needs-validation': true, 'was-validated': cepForm.submitted}" novalidate>
          <div class="form-card" style="border-radius: 8px;">
            <!-- Campo CEP -->
            <div class="form-group mb-4">
              <label for="cep" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                <i class="fe-hash mr-2" style="color: #4A6FDC;"></i>CEP
              </label>
              <div class="position-relative">
                <kendo-maskedtextbox
                  name="cep"
                  [(ngModel)]="cepSelecionado.cep"
                  mask="00.000-000"
                  required
                  class="form-control"
                  id="cep"
                  (blur)="buscarCep()"
                  [disabled]="buscandoCEP || modoEdicao"
                  style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; transition: all 0.3s;">
                </kendo-maskedtextbox>
                <div *ngIf="buscandoCEP" class="spinner-container" style="position: absolute; right: 15px; top: 50%; transform: translateY(-50%);">
                  <i class="k-icon k-i-loading" style="color: #4A6FDC;"></i>
                </div>
              </div>
              <div class="invalid-feedback">CEP é obrigatório</div>
            </div>

            <!-- Campo Logradouro -->
            <div class="form-group mb-4">
              <label for="logradouro" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                <i class="fe-map mr-2" style="color: #4A6FDC;"></i>Logradouro
              </label>
              <input type="text" name="logradouro" [(ngModel)]="cepSelecionado.logradouro"
                     class="form-control" id="logradouro" required
                     style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; transition: all 0.3s;" />
              <div class="invalid-feedback">Logradouro é obrigatório</div>
            </div>

            <!-- Campo Bairro -->
            <div class="form-group mb-4">
              <label for="bairro" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                <i class="fe-home mr-2" style="color: #4A6FDC;"></i>Bairro
              </label>
              <input type="text" name="bairro" [(ngModel)]="cepSelecionado.bairro"
                    class="form-control" id="bairro" required
                    style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; transition: all 0.3s;" />
              <div class="invalid-feedback">Bairro é obrigatório</div>
            </div>

            <!-- Grid de Estado e Cidade -->
            <div class="row gx-3 mb-4">
              <div class="col-md-4">
                <div class="form-group mb-0">
                  <label for="estado" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                    <i class="fe-compass mr-2" style="color: #4A6FDC;"></i>Estado
                  </label>
                  <input type="text" name="estado" [(ngModel)]="cepSelecionado.estado"
                        class="form-control" id="estado" required maxlength="2"
                        style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; text-transform:uppercase; transition: all 0.3s;" />
                  <div class="invalid-feedback">Estado é obrigatório</div>
                </div>
              </div>
              <div class="col-md-8">
                <div class="form-group mb-0">
                  <label for="cidade" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                    <i class="fe-navigation mr-2" style="color: #4A6FDC;"></i>Cidade
                  </label>
                  <input type="text" name="cidade" [(ngModel)]="cepSelecionado.cidade"
                        class="form-control" id="cidade" required
                        style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; transition: all 0.3s;" />
                  <div class="invalid-feedback">Cidade é obrigatória</div>
                </div>
              </div>
            </div>

            <!-- Campo Complemento -->
            <div class="form-group mb-2">
              <label for="complemento" class="form-label fw-medium mb-2" style="color: #495057; font-weight: 500;">
                <i class="fe-info mr-2" style="color: #4A6FDC;"></i>Complemento
              </label>
              <input type="text" name="complemento" [(ngModel)]="cepSelecionado.complemento"
                    class="form-control" id="complemento" placeholder="Opcional"
                    style="height: 45px; border-radius: 8px; padding-left: 15px; box-shadow: none; transition: all 0.3s;" />
            </div>
          </div>

          <!-- Rodapé com botões -->
          <div class="drawer-footer pt-4 mt-4 border-top d-flex justify-content-end">
            <button type="submit" class="btn btn-primary px-4 mr-3"
                    style="height: 45px; border-radius: 8px; font-weight: 500; background-color: #4A6FDC; border-color: #4A6FDC; transition: all 0.3s;">
              <i class="fe-save mr-2"></i>Salvar
            </button>
            <button type="button" class="btn btn-light px-4" (click)="fecharDrawer()"
                    style="height: 45px; border-radius: 8px; font-weight: 500; transition: all 0.3s;">
              <i class="fe-x mr-2"></i>Cancelar
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
