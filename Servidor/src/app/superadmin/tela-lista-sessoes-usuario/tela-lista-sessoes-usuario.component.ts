import {Component, Input, OnInit} from '@angular/core';
import { SessoesUsuarioService } from '../../services/sessoesusuario.service';

declare var moment;

@Component({
  selector: 'app-tela-lista-sessoes-usuario',
  templateUrl: './tela-lista-sessoes-usuario.component.html',
  styleUrls: ['./tela-lista-sessoes-usuario.component.scss']
})
export class TelaListaSessoesUsuarioComponent implements OnInit {
  @Input() empresa: any;
  @Input() usuarioSelecionado: any;
  registrosDeLogin: any = [];
  pageSize: number = 10;
  skip: number = 0;
  carregou = false;
  isLoadingDeslogar = false;

  constructor(private sessoesUsuarioService: SessoesUsuarioService) { }

  ngOnInit(): void {
    this.carregueListaSessoes();
  }

  carregueListaSessoes(): Promise<any> {
    return new Promise((resolve, reject) => {
      if( !this.usuarioSelecionado ) {
        reject('Usuário não selecionado');
      } else {
        this.sessoesUsuarioService.listeSessoes(this.empresa, this.usuarioSelecionado.id).then(sessoes => {
          this.carregou = true;
          this.registrosDeLogin = sessoes;
          resolve(true);
        }).catch(erro => {
          console.error('Erro ao carregar sessões', erro);
          reject(erro);
        });
      }
    });
  }

  pageChange(event: any): void {
    this.skip = event.skip;
    this.carregueListaSessoes();
  }

  async deslogarSessao(registro: any) {
    registro.processando = true;
    this.sessoesUsuarioService.deslogarSessao(this.empresa, registro.idSessao).then(async () => {
      await this.carregueListaSessoes();
      registro.processando = false;
    }).catch(erro => {
      console.error('Erro ao deslogar sessões', erro);
      registro.processando = false;
    });
  }

  async deslogarTodasSessoes() {
    this.isLoadingDeslogar = true;
    this.sessoesUsuarioService.deslogarTodas(this.empresa, this.usuarioSelecionado.id).then(async () => {
      await this.carregueListaSessoes();
      this.isLoadingDeslogar = false;
    }).catch(erro => {
      console.error('Erro ao deslogar sessões', erro);
      this.isLoadingDeslogar = false;
    });
  }

  buscarInformacoesLocalizacao(registro: any) {
    registro.processando = true;
    this.sessoesUsuarioService.buscarInformacoesLocalizacao(this.empresa, registro.idSessao).then(informacoes => {
      registro.processando = false;

      registro.city = informacoes.city;
      registro.region = informacoes.region;
      registro.country = informacoes.country;
      registro.loc = informacoes.loc;
      registro.org = informacoes.org;
      registro.postal = informacoes.postal;
      registro.timezone = informacoes.timezone;
    }).catch(erro => {
      registro.processando = false;
      console.error('Erro ao buscar informações de localização', erro);
    });
  }
}
