import { Component, OnInit } from '@angular/core';

import {ActivatedRoute} from "@angular/router";
import {EmpresasService} from "../services/empresas.service";
import {TiposDePontuacaoService} from "../services/tipos-de-pontuacao.service";
import {TipoDePontuacaoPorValor} from "../../../../server/domain/TipoDePontuacaoPorValor";
import {TipoDePontuacaoQtdFixa} from "../../../../server/domain/TipoDePontuacaoQtdFixa";
import {ModalKendo} from "../../lib/ModalKendo";

@Component({
  selector: 'app-cad-tipo-de-pontuacao',
  templateUrl: './cad-tipo-de-pontuacao.component.html',
  styleUrls: ['./cad-tipo-de-pontuacao.component.scss']
})
export class CadTipoDePontuacaoComponent extends ModalKendo implements OnInit {
  tipoDePontuacao: any = {};
  tipos: any;
  mensagemSucesso: any = '';
  mensagemErro: any = '';

  idEmpresa: any;
  empresa: any;

  constructor(  private route: ActivatedRoute, private empresaService: EmpresasService,
              private tipoDePontuacaoService: TiposDePontuacaoService) {
    super()
  }


  ngOnInit() {
    this.tipos = [new TipoDePontuacaoPorValor(), new TipoDePontuacaoQtdFixa()];

    if( this.idEmpresa ) {
      this.empresaService.obtenha(this.empresa.id).then((respEmpresa: any) => {

        this.empresa = respEmpresa.data;
      })
    }
  }

  fecheMensagemSucesso() {
    this.mensagemSucesso = '';
  }

  fecheMensagemErro() {
    this.mensagemErro = '';
  }

  onSubmit() {
    this.tipoDePontuacao.empresa = this.empresa;

    this.tipoDePontuacaoService.salve(this.tipoDePontuacao).then( () => {
      this.fecheModal()
    })
  }
}
