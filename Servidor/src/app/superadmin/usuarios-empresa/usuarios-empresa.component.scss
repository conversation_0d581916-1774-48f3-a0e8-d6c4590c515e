::ng-deep  .k-switch-on .k-switch-container  {
  background-color: #568d15 !important;
}

::ng-deep  .k-switch-off .k-switch-container  {
  background-color: #ff6358 !important;
  color: #fff;
}

::ng-deep .meu-drawer {
  max-width: 90vw !important;
}

::ng-deep .meu-drawer .k-drawer-wrapper {
  width: 1000px !important;
}

@media (max-width: 768px) {
  ::ng-deep .meu-drawer .k-drawer-wrapper {
    width: 100% !important;
  }
}

// Enhanced styling for the user search interface
::ng-deep {
  // Grid container and header styling
  .grid-container {
    padding: 16px 0;

    .grid-header {
      padding: 0 16px;

      h3 {
        font-size: 1.5rem;
        font-weight: 600;
        color: #343a40;
      }

      .fa-users {
        font-size: 1.5rem;
      }

      p {
        font-size: 0.9rem;
        margin-bottom: 0;
      }
    }
  }
  // Improved grid appearance
  .k-grid {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .k-grid-header {
      background-color: #f8f9fa;

      th.k-header {
        font-weight: 600;
        color: #495057;
        padding: 12px 16px;
        border-bottom: 2px solid #e9ecef;
      }
    }

    .k-grid-content {
      tr.k-master-row {
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f8f9fa;
        }

        td {
          padding: 12px 16px;
          border-bottom: 1px solid #e9ecef;
          vertical-align: middle;
        }
      }
    }

    .k-pager-wrap {
      background-color: #f8f9fa;
      padding: 8px;
      border-top: 1px solid #e9ecef;
    }
  }

  // Enhanced search input
  .search-container {
    position: relative;
    width: 300px;

    .search-icon {
      position: absolute;
      left: 12px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      z-index: 2;
    }

    .search-input {
      border-radius: 20px;
      padding: 8px 16px 8px 35px;
      border: 1px solid #ced4da;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      transition: all 0.3s ease;
      width: 100%;

      &:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
  }

  // Improved button styling
  .btn {
    border-radius: 4px;
    padding: 8px 16px;
    transition: all 0.2s ease;

    &.btn-warning {
      background-color: #f7c948;
      border-color: #f7c948;
      color: #212529;

      &:hover {
        background-color: #f5bc1d;
        border-color: #f5bc1d;
      }
    }

    &.btn-blue {
      background-color: #3f80ea;
      border-color: #3f80ea;
      color: #fff;

      &:hover {
        background-color: #1e68e6;
        border-color: #1e68e6;
      }
    }
  }

  // Improved toolbar layout
  [kendoGridToolbarTemplate] {
    padding: 16px;
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
  }

  // Status and role badges
  .status-badge, .role-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;

    i {
      margin-right: 4px;
    }
  }

  .status-active {
    background-color: rgba(40, 167, 69, 0.15);
    color: #28a745;
  }

  .status-inactive {
    background-color: rgba(220, 53, 69, 0.15);
    color: #dc3545;
  }

  .role-waiter {
    background-color: rgba(255, 193, 7, 0.15);
    color: #d39e00;
  }

  .role-regular {
    background-color: rgba(108, 117, 125, 0.15);
    color: #6c757d;
  }

  // Cell content styling
  .user-name, .user-email, .user-whatsapp, .user-sessions, .user-login {
    display: flex;
    align-items: center;
  }

  // Action buttons styling
  .action-btn {
    transition: all 0.2s ease;
    border-radius: 4px;
    font-size: 0.8rem;
    padding: 4px 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 80px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border-width: 1px;
    border-style: solid;
    cursor: pointer;

    i {
      font-size: 0.9rem;
    }

    &.btn-outline-primary {
      border-color: #3f80ea;
      color: #3f80ea;

      &:hover {
        background-color: #3f80ea;
        color: #fff;
      }
    }

    &.btn-outline-info {
      border-color: #17a2b8;
      color: #17a2b8;

      &:hover {
        background-color: #17a2b8;
        color: #fff;
        box-shadow: 0 2px 5px rgba(23, 162, 184, 0.3);
      }
    }

    &.btn-outline-danger {
      border-color: #dc3545;
      color: #dc3545;

      &:hover {
        background-color: #dc3545;
        color: #fff;
        box-shadow: 0 2px 5px rgba(220, 53, 69, 0.3);
      }
    }
  }

  // Specific styling for buttons in the grid
  kendo-grid-command-column .action-btn,
  kendo-grid-column[title="Permissões"] .action-btn {
    margin: 0 auto;
    display: flex;
  }

  // Ensure consistent vertical alignment for both button containers
  kendo-grid-command-column .k-grid-cell-inner,
  kendo-grid-column[title="Permissões"] .k-grid-cell-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
}
