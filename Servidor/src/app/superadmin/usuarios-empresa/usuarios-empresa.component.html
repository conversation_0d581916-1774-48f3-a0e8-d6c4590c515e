<kendo-drawer-container>
  <kendo-drawer #drawer class="meu-drawer" [mode]="'overlay'" [mini]="false" [autoCollapse]="true" [position]="'end'">
    <ng-template kendoDrawerTemplate>
      <div class="container mt-5">
        <div class="p-1">
          <div class="titulo-com-icon">
            <h4 class="k-heading "><i class="fa fa-desktop text-info"></i> Lista de Sessões</h4>
          </div>
          <hr>
        </div>

        <app-tela-lista-sessoes-usuario [usuarioSelecionado]="usuarioSelecionado" [empresa]="empresa"></app-tela-lista-sessoes-usuario>
      </div>
    </ng-template>
  </kendo-drawer>
</kendo-drawer-container>

<kendo-drawer-content>

<kendo-grid [pageSize]="paginacao.size"
            [skip]="paginacao.skip"
            [style]=""
            [kendoGridBinding]="usuariosFiltrados"
            [loading]="loading" style="min-height: 300px;"
            [scrollable]="'none'"
            footerStyle="font-size: 11px;"
            [pageable]="{
              buttonCount: paginacao.buttonCount,
              info: paginacao.info,
              type: 'numeric',
              pageSizes: paginacao.pageSizes,
              previousNext: paginacao.previousNext
            }"
            (edit)="editarUsuario($event)"
            (pageChange)="mudouPaginacao($event)">
  <ng-template kendoGridToolbarTemplate [position]="'top'">
    <div class="d-flex justify-content-between align-items-center w-100">
      <div>
        <button class="btn btn-warning mr-2" (click)="abraModalGerenciarPapeis()">
          <i class="fe-lock mr-1"></i>
          Gerenciar Papeis
        </button>

        <button class="btn btn-blue" (click)="novoGarcom()" *ngIf="possuiModuloGarcom">
          <i class="fe-plus mr-1"></i>
          Adicionar garçom
        </button>
      </div>
      <div class="d-flex align-items-center">
        <label class="mr-2">Filtrar:</label>
        <div class="search-container position-relative">
          <i class="fa fa-search search-icon"></i>
          <input class="form-control search-input" type="text" [(ngModel)]="filtroUsuario" (input)="filtrarUsuarios()" placeholder="Buscar por nome, email ou whatsapp">
        </div>
      </div>
    </div>
  </ng-template>
  <kendo-grid-messages
    pagerPage="Página"
    pagerOf="de"
    pagerItems="itens"
    noRecords="nenhum usuário cadastrado"
    loading="Carregando"
    pagerItemsPerPage="ítems por página"
  >
  </kendo-grid-messages>


  <kendo-grid-column field="id" title="ID"   [width]="70">  </kendo-grid-column>

  <kendo-grid-column title="Nome"  >
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-name">
        <i class="fa fa-user-circle text-secondary mr-2"></i>
        <span><b>{{dataItem.nome}}</b></span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Email" >
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-email">
        <i class="fa fa-envelope text-secondary mr-2"></i>
        <span class="text-primary">{{dataItem.email}}</span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Whatsapp"  >
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-whatsapp" *ngIf="dataItem.numeroWhatsapp">
        <i class="fab fa-whatsapp text-success mr-2"></i>
        <span class="text-primary">{{dataItem.numeroWhatsapp.whatsapp | telefone}}</span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Sessões Ativas" [width]="150">
    <ng-template kendoGridCellTemplate let-dataItem>
      <div class="user-sessions">
        <i class="fa fa-desktop text-info mr-2"></i>
        <a href="#" class="text-success" *ngIf="dataItem.qtdeSessoesAtivas" (click)="abrirPopupSessoes(dataItem); $event.preventDefault()">
          {{dataItem.qtdeSessoesAtivas}}
          <span *ngIf="dataItem.qtdeSessoesAtivas === 1"> sessão ativa</span>
          <span *ngIf="dataItem.qtdeSessoesAtivas > 1"> sessões ativas</span>
        </a>
        <span class="text-muted" *ngIf="dataItem.qtdeSessoesAtivas === 0">Nenhuma</span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Último login" >
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-login">
        <i class="fa fa-clock text-secondary mr-2"></i>
        <span class="text-primary">{{dataItem.ultimoLogin | date:'dd/MM/yyyy HH:mm:ss'}}</span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Ativo" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-status text-center">
        <span class="status-badge" [ngClass]="{'status-active': dataItem.ativo, 'status-inactive': !dataItem.ativo}">
          <i class="fa" [ngClass]="{'fa-check': dataItem.ativo, 'fa-times': !dataItem.ativo}"></i>
          {{dataItem.ativo ? 'Ativo' : 'Inativo'}}
        </span>
      </div>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-column title="Garçom" [width]="100">
    <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
      <div class="user-role text-center">
        <span class="role-badge" [ngClass]="{'role-waiter': dataItem.garcom, 'role-regular': !dataItem.garcom}">
          <i class="fa" [ngClass]="{'fa-utensils': dataItem.garcom, 'fa-user': !dataItem.garcom}"></i>
          {{dataItem.garcom ? 'Garçom' : 'Usuário'}}
        </span>
      </div>
    </ng-template>
  </kendo-grid-column>


  <kendo-grid-column title="Permissões"   [width]="130" >
    <ng-template kendoGridCellTemplate let-usuario >
      <button class="action-btn btn-outline-info" (click)="abraModalAssociarPapel(usuario)" *ngIf="usuario.idEmpresa === empresa.id">
        <i class="mdi mdi-account-key mr-1"></i>
        Permissões
      </button>
      <span class="badge badge-info" *ngIf="usuario.admin">
          <i class="fa fa-shield-alt mr-1"></i>
          Admin
        </span>
    </ng-template>
  </kendo-grid-column>

  <kendo-grid-command-column title="" [width]="160">
    <ng-template kendoGridCellTemplate let-isNew="isNew" let-usuario>
      <div class="d-flex">
        <button class="action-btn btn-outline-danger mr-1" (click)="editarUsuario(usuario)" *ngIf="usuario.idEmpresa === empresa.id">
          <i class="fa fa-edit mr-1"></i>
          Editar
        </button>
        <span class="badge badge-success" *ngIf="usuarioLogado && usuarioLogado.id === usuario.id">
          <i class="fa fa-user mr-1"></i>
          Você
        </span>
        <span class="badge badge-warning" *ngIf="usuario.superadmin">
          <i class="fa fa-shield-alt mr-1"></i>
          Superadmin
        </span>
        <button class="btn btn-sm btn-danger" (click)="removerUsuario(usuario)" *ngIf="usuarioLogado && usuarioLogado.id !== usuario.id && !usuario.superadmin && (usuario.admin && usuarioLogado.superadmin)">
          <i class="fa fa-trash"></i>
        </button>
      </div>
    </ng-template>
  </kendo-grid-command-column>
</kendo-grid>

</kendo-drawer-content>
