tr td.esconder, tr th.esconder{
  width: 0px !important;
  display: none;
}

.dropdown{
  max-width: 70%; float: left;
  min-width: 100px;
}

.container-scroll{
  max-height: 500px;
  overflow: scroll;
}

table tr td, table th td{
  border: 1px solid #eee;
  text-align: center;
}

.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;

  .stat-item {
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.success { background-color: rgba(40, 167, 69, 0.1); }
    &.warning { background-color: rgba(255, 193, 7, 0.1); }

    i {
      font-size: 1.5rem;
    }
  }
}

.error-section {
  .error-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 0.5rem;

    .error-item {
      padding: 0.5rem;
      border-bottom: 1px solid #dee2e6;

      &:last-child {
        border-bottom: none;
      }

      .error-line {
        font-weight: bold;
        margin-right: 0.5rem;
      }
    }
  }
}

.import-type-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 1rem;
}

.upload-section {
  max-width: 600px;
  margin: 0 auto;
}

.custom-upload {
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;

  &:hover {
    border-color: #007bff;
  }
}

.preview-section {
  .table {
    th {
      white-space: nowrap;

      kendo-dropdownlist {
        min-width: 100px;

        ::ng-deep .k-input-inner {
          padding: 4px 8px;
        }

        ::ng-deep .k-list-item {
          padding: 8px;

          &:hover {
            background-color: rgba(0, 123, 255, 0.1);
          }
        }
      }
    }

    td {
      vertical-align: middle;
    }
  }
}

// Responsividade
@media (max-width: 768px) {
  .stats-container {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .btn {
      width: 100%;
    }
  }
}

kendo-grid {
  .k-grid-header {
    background-color: #f8f9fa;
  }

  .k-grid-content {
    overflow-y: auto;
  }

  // Estilo para a coluna de número da linha
  .k-grid-index-cell {
    background-color: #f8f9fa;
    font-weight: bold;
  }

  // Ajuste para os dropdowns no header
  .k-grid-header {
    kendo-dropdownlist {
      min-width: 150px;
    }
  }
}
