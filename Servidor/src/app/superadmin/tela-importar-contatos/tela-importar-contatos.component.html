<!-- Resultado da importação -->
<div class="card" *ngIf="resposta.importados >= 0">
  <div class="card-header bg-light">
    <h4 class="mb-0">Resultado da Importação</h4>
  </div>
  <div class="card-body">
    <div class="stats-container">
      <div class="stat-item success">
        <i class="fas fa-user-plus"></i>
        <span>Novos contatos: {{resposta.importados}}</span>
      </div>
      <div class="stat-item warning">
        <i class="fas fa-user-check"></i>
        <span>Existentes: {{resposta.existentes}}</span>
      </div>
      <div class="stat-item warning" *ngIf="resposta.atualizados">
        <i class="fas fa-sync"></i>
        <span>Atualizados: {{resposta.atualizados}}</span>
      </div>
    </div>

    <div class="error-section mt-4" *ngIf="resposta.erros.length">
      <h5 class="text-danger">
        <i class="fas fa-exclamation-triangle"></i>
        Erros encontrados: {{resposta.erros.length}}
      </h5>
      <div class="error-list">
        <div class="error-item" *ngFor="let erroInfo of resposta.erros">
          <span class="error-line">Linha {{erroInfo.linha}}:</span>
          <span class="error-message">{{erroInfo.erro}}</span>
        </div>
    </div>
  </div>

    <div class="action-buttons mt-4">
      <button class="btn btn-primary" (click)="importarNovo()">
        <i class="fas fa-file-import"></i> Nova Importação
      </button>
      <button class="btn btn-danger ml-2" (click)="corrigirErro()">
        <i class="fas fa-wrench"></i> Corrigir Erros
      </button>
    </div>
  </div>
</div>

<!-- Seleção do tipo de importação -->
<div class="card" *ngIf="!resposta.importado">
  <div class="card-body">
    <div *ngIf="!tipoImportar.tipo" class="text-center">
      <h5 class="mb-4"><b>Selecione o tipo de importação</b></h5>
      <div class="import-type-buttons">
        <button *ngFor="let tipoDeImportacao of tiposImportacao"
                (click)="selecioneTipoImportacao(tipoDeImportacao)"
                class="btn btn-info m-2">
          <i class="fas fa-file-alt"></i>
          {{tipoDeImportacao.tipo}}
        </button>
      </div>
  </div>

    <!-- Upload do arquivo -->
    <div *ngIf="tipoImportar.tipo && !contatos?.length" class="upload-section">
      <h6 class="mb-3">Selecione o arquivo para importação</h6>
      <kendo-upload
        id="foto"
        name="foto"
        (select)="selecionouArquivo($event)"
        (success)="successUpload($event)"
                  [(ngModel)]="files"
        [multiple]="false"
        [autoUpload]="true"
        [saveUrl]="uploadUrl"
        [restrictions]="restricoes"
        class="custom-upload">
      <kendo-upload-messages
          select="Selecionar arquivo..."
          uploadSelectedFiles="Enviar arquivo"
        clearSelectedFiles="Limpar">
      </kendo-upload-messages>
    </kendo-upload>

      <div class="alert alert-danger mt-3" *ngIf="erro">
        <i class="fas fa-exclamation-circle"></i>
        {{erro}}
   </div>
  </div>

    <!-- Tabela de preview -->
    <div *ngIf="contatos?.length > 0" class="preview-section mt-4">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h6 class="mb-0">
          <i class="fas fa-table"></i>
          Contatos a importar: <span class="badge badge-info">{{contatos.length}}</span>
        </h6>
        <button class="btn btn-success"
                (click)="importarContatos($event)"
                [disabled]="importando">
          <i class="fas fa-file-import"></i>
          Importar
          <i class="k-icon k-i-loading" *ngIf="importando"></i>
        </button>
      </div>

      <kendo-grid [data]="contatos"
                  [height]="450"
                  [loading]="importando">

        <!-- Coluna de número da linha personalizada -->
        <kendo-grid-column [width]="50"
                          headerClass="text-center"
                          class="text-center"
                          [locked]="true"
                          title="#">
            <ng-template kendoGridCellTemplate let-dataItem let-rowIndex="rowIndex">
                {{rowIndex + 1}}
            </ng-template>
        </kendo-grid-column>

        <!-- Colunas dinâmicas -->
        <ng-container *ngFor="let item of colunasImportar" >
          <kendo-grid-column *ngIf="!item.removida && item.indice > 0"
                             [width]="200">
            <ng-template kendoGridHeaderTemplate>
              <div class="d-flex align-items-center">
                <kendo-dropdownlist
                  [name]="'colunaImportar' + (item.indice+1)"
                  [data]="tipoImportar.colunas"
                  class="flex-grow-1"
                  textField="descricao"
                  [filterable]="true" [kendoDropDownFilter]="filterSettings"
                  [virtual]="false"
                  [defaultItem]="{ descricao: 'Selecione' }"
                  [(ngModel)]="item.coluna">
                </kendo-dropdownlist>
                <button class="btn btn-danger btn-sm ml-2"
                        (click)="removaColuna(item)">
                  <i class="fas fa-times"></i>
          </button>
              </div>
            </ng-template>
            <ng-template kendoGridCellTemplate let-dataItem>
              {{dataItem[item.indice]}}
            </ng-template>
          </kendo-grid-column>
        </ng-container>


        <!-- Coluna de Status/Erro -->
        <kendo-grid-column field="erro"
                          title="Status"
                          [width]="150"
                          class="text-danger">
        </kendo-grid-column>

      </kendo-grid>
    </div>
  </div>
</div>

