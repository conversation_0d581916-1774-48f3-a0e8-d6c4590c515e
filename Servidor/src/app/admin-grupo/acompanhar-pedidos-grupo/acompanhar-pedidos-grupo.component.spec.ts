import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AcompanharPedidosGrupoComponent } from './acompanhar-pedidos-grupo.component';

describe('AcompanharPedidosGrupoComponent', () => {
  let component: AcompanharPedidosGrupoComponent;
  let fixture: ComponentFixture<AcompanharPedidosGrupoComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AcompanharPedidosGrupoComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AcompanharPedidosGrupoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
