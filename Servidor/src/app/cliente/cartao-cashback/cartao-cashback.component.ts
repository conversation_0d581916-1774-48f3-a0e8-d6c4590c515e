import {Component, Input, OnInit} from '@angular/core';
import {Router} from "@angular/router";

@Component({
  selector: 'app-cartao-cashback',
  templateUrl: './cartao-cashback.component.html',
  styleUrls: ['./cartao-cashback.component.scss']
})
export class CartaoCashbackComponent implements OnInit {

  @Input() cartao: any = { pontos: 0  }
  constructor(private router: Router) { }
  ngOnInit() {}
}
