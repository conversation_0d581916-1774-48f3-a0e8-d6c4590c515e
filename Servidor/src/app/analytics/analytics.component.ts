import { Component, OnInit } from '@angular/core';
import {ConstantsService} from "../fidelidade/ConstantsService";
import {DomSanitizer} from "@angular/platform-browser";

@Component({
  selector: 'app-analytics',
  templateUrl: './analytics.component.html',
  styleUrls: ['./analytics.component.scss']
})
export class AnalyticsComponent implements OnInit {
  empresa: any = {

  };
  url: any = '';

  constructor(private constantsService: ConstantsService, private domSanitizer: DomSanitizer) { }

  ngOnInit(): void {
    this.constantsService.empresa$.subscribe( (empresa: any) => {
      if( !empresa) {
        return;
      }

      this.empresa = empresa;

      const linkLoja = empresa.linkLoja.replace('https://', '');

      const strUrl = `https://datastudio.google.com/embed/reporting/6c116ab4-941b-46b9-b57d-07cff55a1aa6/page/tWDGB?params=%7B%22ds48.teste%22:%22${linkLoja}%22%7D`;

      this.url = this.domSanitizer.bypassSecurityTrustResourceUrl(strUrl);
    });
  }

}
