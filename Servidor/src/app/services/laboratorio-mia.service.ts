import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from './ServerService';

@Injectable({
  providedIn: 'root'
})
export class LaboratorioMiaService extends ServerService {
  
  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  /**
   * Testa uma mensagem com um modelo específico de IA
   * @param mensagem O texto da mensagem a ser testada
   * @param modelo O ID do modelo a ser usado
   * @param config Configurações adicionais para o teste
   */
  testeMensagem(mensagem: string, modelo: string, config: any = {}) {
    return this.facaPost('/chatbot/laboratorio-mia/teste', {
      mensagem,
      modelo,
      temperatura: config.temperatura || 0.7,
      maxTokens: config.maxTokens || 500,
      ferramentasAtivas: config.ferramentasAtivas || {
        buscarProdutos: true,
        verificarEstoque: false,
        verificarPromocoes: false,
        verificarHorarios: true
      }
    });
  }

  /**
   * Salva uma configuração de modelo como padrão
   * @param config A configuração a ser salva
   */
  salveConfiguracao(config: any) {
    return this.facaPost('/chatbot/laboratorio-mia/configuracao', config);
  }

  /**
   * Obtém o histórico de testes realizados
   * @param filtros Filtros opcionais para o histórico
   */
  obtenhaHistorico(filtros: any = {}) {
    return this.facaPost('/chatbot/laboratorio-mia/historico', filtros);
  }

  /**
   * Salva uma avaliação de teste
   * @param idTeste ID do teste avaliado
   * @param avaliacao Tipo de avaliação ('positivo', 'negativo', etc)
   */
  salveAvaliacao(idTeste: number, avaliacao: string) {
    return this.facaPost('/chatbot/laboratorio-mia/avaliacao', {
      idTeste,
      avaliacao
    });
  }

  /**
   * Obtém os modelos de IA disponíveis
   */
  obtenhaModelosDisponiveis() {
    return this.obtenha('/chatbot/laboratorio-mia/modelos', {});
  }
} 