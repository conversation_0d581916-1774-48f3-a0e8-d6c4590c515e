.chat-window {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin: 2rem auto;
  width: 300px;
}

.chat-header {
  padding: 0.5rem 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 0.5rem;
}

.chat-footer {
  padding: 0.8rem 2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #c0e5ff;
  margin-top: 0.5rem;
  color: #00a5f4;
}

.chat-header {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}
.chat-body {
  padding: 1rem;
  overflow-y: auto;
  background: #f0f0f0;
}
.chat-message {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}
.chat-message:last-child {
  margin-bottom: 0;
}
.chat-icon {
  color: #007bff;
  margin-right: 0.5rem;
}

.chat-text {
  flex-grow: 1;
  padding: 8px;
  padding-bottom: 0px;
  border-radius: 15px;
  background: #fff;
}
