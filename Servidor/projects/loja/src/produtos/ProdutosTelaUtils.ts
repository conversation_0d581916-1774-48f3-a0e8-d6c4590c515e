export class ProdutosTelaUtils{
  constructor( ) { }

  static insiraCategoriaFixa(categoria: any , produtos: any, tela: any){
    if(!tela.categorias.find(item =>  item.id === categoria.id ))
      tela.categorias.splice(0, 0, categoria);

    if( tela.produtosPorCategoria)
      tela.produtosPorCategoria[categoria.nome] = produtos;
  }

  static definaCategoriasFixas(tela: any) {
    let pizzasMontar: any =   tela.produtos.filter( produto => produto.template != null && produto.template.montarPizza);

    if(pizzasMontar.length > 0){
      tela.categoriaMonteSuaPizza.nome = pizzasMontar[0].template.nomeCategoriaMontar;
      ProdutosTelaUtils.insiraCategoriaFixa(tela.categoriaMonteSuaPizza, [], tela);

      pizzasMontar.forEach( (produtoPizza: any) => {
        produtoPizza.tamanhos.forEach( (tamanhoPizza: any) => {

          if(tamanhoPizza.qtdeSabores >= 1){

            let nome = String(`${produtoPizza.template.nome} ${tamanhoPizza.descricao} `),
              descricao = String(`Monte ${nome}`),
              preco = tamanhoPizza.novoPreco ? tamanhoPizza.novoPreco : tamanhoPizza.preco,
              valorMinimo =  tamanhoPizza.valorMinimo;

            if(tamanhoPizza.qtdeSabores > 1){
              nome = String(`${nome} com até  ${tamanhoPizza.qtdeSabores} sabores`)
              descricao = String(`${descricao} com até  ${tamanhoPizza.qtdeSabores} sabores`)
            }

            let produtoCategoria =
              tela.produtosPorCategoria[tela.categoriaMonteSuaPizza.nome].find(
                produtoCategogia => produtoCategogia.id === tamanhoPizza.template.id);

            if(!produtoCategoria){
              produtoCategoria =  {
                id: tamanhoPizza.template.id, nome: nome,    descricao: descricao, montar: true,
                template: produtoPizza.template, tamanho: tamanhoPizza, valorMinimo: valorMinimo,
                sabores: [], camposAdicionais: produtoPizza.camposAdicionais, tamanhos: [],
              }
              tela.produtosPorCategoria[tela.categoriaMonteSuaPizza.nome].push( produtoCategoria);
            }

            produtoCategoria.sabores.push(produtoPizza);

            if(valorMinimo < produtoCategoria.valorMinimo)
              produtoCategoria.valorMinimo = valorMinimo
          }


        })
      })

    }

    tela.destaques = [];

    tela.produtos.forEach( (produto: any) => {
      if(produto.destaque) tela.destaques.push(Object.assign({}, produto))

      if(produto.tamanhos && produto.tamanhos.length){
        if(produto.tamanhos.find( tamanho => tamanho.destaque) != null)
          tela.destaques.push(Object.assign({}, produto))
      }
    })

    if( tela.destaques && tela.destaques.length){
      tela.destaques.forEach( produto => produto.destacado = true);
      ProdutosTelaUtils.insiraCategoriaFixa( tela.categoriaDestaque,  tela.destaques, tela)
    }

    if(tela.vitrines){
      for(let i = tela.vitrines.length - 1; i >= 0 ; i--){
        let vitrine: any =  tela.vitrines[i];
        let categoriaVitrine: any =  { nome: vitrine.nome, id: vitrine.id, vitrine: true };

        let produtosVitrine = vitrine.produtosNaVitrine.map((pnv: any) => pnv.produto);
        produtosVitrine.forEach( produto => produto.destacado = true);

        ProdutosTelaUtils.insiraCategoriaFixa(categoriaVitrine, produtosVitrine, tela)
      }
    }

  }
}
