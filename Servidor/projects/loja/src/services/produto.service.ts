import { Injectable } from '@angular/core';
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {ServerService} from "./ServerService";
import { Observable } from 'rxjs';
import {catchError, map, publishReplay, refCount} from "rxjs/operators";


@Injectable({
  providedIn: 'root'
})
export class ProdutoService extends ServerService {
  cacheProdutosPorEmpresa = {};
  INICIAL = 'INICIAL';
  constructor(private httpCliente: HttpClient) {
    super(httpCliente);
  }

  listeDaCategoria(idCategoria: number, inicio: number, total: number, filtro: string)
  {
    let params = String(`idcat=${idCategoria}&indice=${inicio}&limite=${total}`)

    if(filtro) params = params + "&termo=" + filtro

    return this.obtenha(String(`/api/produtos/venda/delivery?${params}`), {})
  }

  listeDaVitrine(inicio: number, total: number, filtro: string)
  {
    let params = String(`vit=true&indice=${inicio}&limite=${total}`)

    if(filtro) params = params + "&termo=" + filtro

    return this.obtenha(String(`/api/produtos/venda/delivery?${params}`), {})
  }

  listeAhVendaGrupo(tipoCardapio  = 'DELIVERY', layoutHorinzontal = false, categoria: string = null,
                    quantidadePorPagina = null, indice = null, termo = ''): Observable<any> {
    let link = '/api/grupo/produtos/venda'

    return this.httpCliente.get(link).pipe(
      map( this.processeResposta),
      publishReplay(1),
      refCount(),
      catchError(this.handleError)
    );
  }

  removaDaCache(empresa: any, tipoCardapio){
    delete this.cacheProdutosPorEmpresa[empresa.id];
  }

  listeAhVenda(empresa: any, tipoCardapio  = 'DELIVERY', layoutHorizontal = false, categoria: any = null,
               quantidadePorPagina = null, indice = null, termo = ''): Observable<any> {
    let produtos;
    let nomeCategoria;

    let cacheProdutosEmpresa = this.cacheProdutosPorEmpresa[empresa.id];

    console.log('cache: ', this.cacheProdutosPorEmpresa);

    if(!this.cacheProdutosPorEmpresa[empresa.id] )
      this.cacheProdutosPorEmpresa[empresa.id] = cacheProdutosEmpresa = new CacheDeProdutos();

    const chavePorTipo = (tipoCardapio + termo) + (categoria || '');
    const chaveMercado = (categoria || this.INICIAL) + termo;

    if(!layoutHorizontal)
      produtos = cacheProdutosEmpresa.mapProdutosPorTipoCardapio[chavePorTipo]
    else {
      if(!cacheProdutosEmpresa.mapProdutosMercado[chaveMercado])
        cacheProdutosEmpresa.mapProdutosMercado[chaveMercado] = {}

        produtos = cacheProdutosEmpresa.mapProdutosMercado[chaveMercado][indice]
    }

    let headers = new HttpHeaders();
    headers = headers.set('nomeempresa', empresa.dominio);
    if (!produtos) {
      let link =  String(`/api/produtos/venda/${tipoCardapio}?`) ;

        if(quantidadePorPagina)
          link += '&limite=' + quantidadePorPagina + '&indice=' + indice

        if(termo)
          link += '&termo=' + termo

        if(layoutHorizontal){
          link += '&mc=1'
          if(!categoria) categoria = 'INICIAL'
          else link += categoria > 0 ?  '&idcat=' + categoria : '&cat=' + categoria;

          cacheProdutosEmpresa.mapProdutosMercado[chaveMercado][indice] = this.httpCliente.get(link, {
            headers: headers
          }).pipe(
                map( this.processeResposta),
                  publishReplay(1),
                  refCount(),
                  catchError(this.handleError)
                );
        }  else {
          if( categoria ) link += categoria > 0 ?  '&idcat=' + categoria : '&cat=' + categoria;

          console.log('header: ' + headers.get('nomeempresa'));

          cacheProdutosEmpresa.mapProdutosPorTipoCardapio[chavePorTipo] = this.httpCliente.get(link, {
            headers: headers
          }).pipe(
            map(this.processeResposta),
            publishReplay(1),
            refCount(),
            catchError(this.handleError)
          );
      }
      //.toPromise().then(this.retorno).catch(this.handleError);
    }

    if(layoutHorizontal)
      return cacheProdutosEmpresa.mapProdutosMercado[chaveMercado][indice];
    else
      return cacheProdutosEmpresa.mapProdutosPorTipoCardapio[chavePorTipo];
  }

  processeResposta(response: any): Promise<any> {
    if ( response.sucesso ) {
      return response.data;
    } else {
      console.log( 'Houve um erro ao enviar');
      return response.erro;
    }
  }

  listeProdutosVitrine() {
    return this.obtenha('/api/produtos/vitrines', {})
  }

  listeCategoriasMegamenu(){
    return this.obtenha('/api/categorias/megamenu', {});
  }

  obtenhaProduto(idEmpresa: number, idProduto: number) {
      const link =  '/api/empresa/' + idEmpresa + "/produtos/" + idProduto;

    return this.httpCliente.get(link)
      .toPromise().then(this.retorno).catch(this.handleError);
  }

  apagueCache() {
  }
}

class CacheDeProdutos {
  empresa: any;
  mapProdutosPorTipoCardapio = {};
  mapProdutosMercado = {};
}
