import { Injectable } from '@angular/core';
import {HttpClient} from "@angular/common/http";
import {BehaviorSubject, Observable} from "rxjs";
import {ClienteService} from "./cliente.service";
import {ActivatedRoute} from "@angular/router";
import {ProdutoService} from "./produto.service";

@Injectable()
export class ConstantsService{
  private moduloPedido = new BehaviorSubject<boolean>(null);
  private moduloFidelidade = new BehaviorSubject<boolean>(null);
  private moduloApp = new BehaviorSubject<boolean>(false);
  private empresa = new BehaviorSubject<any>(null);
  private franquia = new BehaviorSubject<any>(null);
  private campoCpf = new BehaviorSubject<any>(null);
  private campoDataNascimento = new BehaviorSubject<any>(null);
  private cardapioLoja = new BehaviorSubject<any>(null);
  private tagsProdutos = new BehaviorSubject<any>(null);
  diasDaSemana = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];
  moduloPedido$ = this.moduloPedido.asObservable();
  moduloFidelidade$ = this.moduloFidelidade.asObservable();
  moduloApp$ = this.moduloApp.asObservable();
  empresa$ = this.empresa.asObservable();
  franquia$ = this.franquia.asObservable();
  tagsProdutos$ = this.tagsProdutos.asObservable();
  campoCpf$ = this.campoCpf.asObservable();
  campoDataNascimento$ = this.campoDataNascimento.asObservable();
  produtosLoja: any = [];
  brindesDaLoja: any = [];

  cardapioLoja$: any  = this.cardapioLoja.asObservable();
  categoriasDaLoja: any = [];
  carregou  = false;
  carregandoCardapio = false;
  eventSource: any
  readonly usuarioLogado: any;

  constructor(private httpClient: HttpClient,  private clienteService: ClienteService,
              private produtoService: ProdutoService,
              private activatedRoute: ActivatedRoute) {

  }

  recarregueEmpresa(mesa: boolean = false) {
    let idOperador = null;

    if( !idOperador )
      idOperador = localStorage.getItem('operador');

    this.empresa.next(null);
    this.clienteService.obtenhaEmpresa(idOperador, mesa).then((dados) => {
      this.carregou = true;
      this.carregueInformacoesDeHorarioDeFuncionamento(dados.empresa);
      dados.empresa.tagsProdutos = dados.tagsProdutos || [];
      this.empresa.next ( dados.empresa );
      this.moduloPedido.next( this.possuiModulo(dados.empresa, 'pedidos')  )
      this.moduloFidelidade.next( this.possuiModulo(dados.empresa, 'fidelidade')  )
      this.moduloApp.next( this.possuiModulo(dados.empresa, 'app')  )
      this.campoCpf.next( this.obtenhaCampoExtra(dados.empresa, 'cpf'))
      this.campoDataNascimento.next( this.obtenhaCampoExtra(dados.empresa, 'datanascimento'))

    });
  }

  obtenhaCarpadioCompleto(tipo: string, empresa: any){
    if(!this.categoriasDaLoja.length)
      this.carregueCardapio(tipo, empresa);
    else {
      setTimeout(() => {
        let dados: any = { categorias: this.categoriasDaLoja}

        if(!this.carregandoCardapio){
          dados.produtos = this.produtosLoja;
          dados.brindes = this.brindesDaLoja;
        }

        this.cardapioLoja.next(dados) ;
      })
    }

    return this.cardapioLoja$;
  }

  carregueCardapio(tipoCardapio: string, empresa: any){
    if(this.carregandoCardapio) return;

    this.carregandoCardapio = true;

    console.log('chamou carregue')
    const eventSourceConfig: any = {
      headers:  { 'nomeempresa':  empresa.dominio}
    };

    let url =  String(`/api/produtos/venda/${tipoCardapio}/async?`) ;

    this.eventSource = new EventSource(url, eventSourceConfig);

    this.eventSource.addEventListener('categorias', (event: any) => {
      console.log('chamou event listener categorias')
      const data = JSON.parse(event.data);
      this.categoriasDaLoja = data.categorias;
      this.cardapioLoja.next({ categorias: this.categoriasDaLoja})
    });

    this.eventSource.addEventListener('produtos', (event: any) => {
      const data = JSON.parse(event.data);
      this.produtosLoja = data.produtos;
      this.brindesDaLoja = data.brindes;
      this.cardapioLoja.next({ produtos: this.produtosLoja, brindes: this.brindesDaLoja})
      this.carregandoCardapio = false;
      this.eventSource.close();
    });
  }

  possuiCampoExtra(empresa: any, nomeCampo){
    if(!empresa || !empresa.camposExtras) return false;

    return empresa.camposExtras.some(campoExtra =>   campoExtra.nome === nomeCampo);
  }

  obtenhaCampoExtra(empresa: any, nomeCampo){
    if(!empresa || !empresa.camposExtras) return null;

    return  empresa.camposExtras.find(campoExtra =>   campoExtra.nome === nomeCampo)
  }

  possuiModulo(empresa: any, nomeModulo: string) {
    if(!empresa || !empresa.modulos) return false;

    return empresa.modulos.some(modulo =>   modulo.nome === nomeModulo);
  }

  private carregueInformacoesDeHorarioDeFuncionamento(empresa: any) {

    if(empresa.estaAberta) {
      empresa.formasDeEntrega.forEach( (forma: any) => {
         if(forma.tempoMinimo && forma.tempoMaximo)
           empresa.descricaoTempoEntrega =  String(`${forma.tempoMinimo} ~ ${forma.tempoMaximo} min`);
         if(forma.tempoMinimoRetirada && forma.tempoMaximoRetirada)
           empresa.descricaoTempoRetirada = String(`${forma.tempoMinimoRetirada} ~ ${forma.tempoMaximoRetirada} min`);
      });
    }
  }
}
