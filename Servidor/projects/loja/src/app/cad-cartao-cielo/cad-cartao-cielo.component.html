
<div class="alert alert-danger" *ngIf="erroAutenticacao3ds">
  {{erroAutenticacao3ds}}
</div>


<form id="frm" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm">
  <div class="bloqueio" *ngIf="carregandoScript || !this.inicializouMdi3ds" >
    <div class="k-icon k-i-loading mt-5 centralizado fa-2x" [hidden]="erroAutenticacao3ds"></div>
  </div>

  <div>
    <div class="row">

      <input type="hidden" id="carregou3ds"  (change)="onCarregou3ds($event)"  value="false" />
      <input type="hidden" id="autenticou3ds"  (change)="onAuntenticou3ds($event)"  value="false" />
      <input type="hidden" id="erro3ds" (change)="onFalhouAutenticou3ds($event)" value="false" />


      <input type="hidden"   class="bpmpi_auth" value="true" />

     <!-- <input type="hidden"   class="bpmpi_auth_notifyonly" value="true" *ngIf="cartao && cartao.bandeira === 'Mastercard'" /> -->

      <input type="hidden"   class="bpmpi_accesstoken" value="{{token}}" />
      <input type="hidden"   class="bpmpi_transaction_mode" value="S" />
      <input type="hidden"   class="bpmpi_order_recurrence" value="false" />

      <input type="hidden"  class="bpmpi_ordernumber"   value="{{pedido.guid}}" />
      <input type="hidden"  class="bpmpi_currency" value="986" />
      <input type="hidden"  class="bpmpi_totalamount" value="{{convertaCentavos(pedido.total)}}" />
      <input type="hidden"  class="bpmpi_installments" value="1" />
      <input type="hidden"  class="bpmpi_paymentmethod"   value="Credit" />
      <input type="hidden"  class="bpmpi_order_productcode"   value="PHY" />

      <input type="hidden" class="bpmpi_cardnumber" value="{{cartao.numero}}" />
      <input type="hidden" class="bpmpi_cardexpirationmonth" value="{{cartao.mes}}" />
      <input type="hidden" class="bpmpi_cardexpirationyear" value="{{cartao.ano}}" />

      <input type="hidden"  class="bpmpi_merchant_url"   value="{{host}}" />

      <input type="hidden"   class="bpmpi_billto_contactname"   value="{{contato.nome}}" />
      <input type="hidden"   class="bpmpi_billto_phonenumber"   value="{{contato.telefone}}" />
      <input type="hidden"   class="bpmpi_billto_email"   value="{{cartao.email}}" />
      <input type="hidden"   class="bpmpi_billto_customerid"   value="{{cartao.cpf}}" />

      <ng-container *ngFor="let item of pedido.itens">
        <input type="hidden" class="bpmpi_cart_1_name"   value="{{item.produto.nome}}" />
        <input type="hidden" class="bpmpi_cart_1_description"   value="{{item.produto.descricao}}" />
        <input type="hidden" class="bpmpi_cart_1_quantity"   value="{{item.qtde}}" />
        <input type="hidden" class="bpmpi_cart_1_unitprice" value="{{obtenhaValorUnitario(item)}}" />
      </ng-container>

      <ng-container *ngIf="enderecoCobranca">
        <input type="hidden" class="bpmpi_billto_street1"   value="{{obtenhaEndereco1(enderecoCobranca)}}" />
        <input type="hidden" class="bpmpi_billto_street2"   value="{{obtenhaEndereco2(enderecoCobranca)}}" />
        <input type="hidden" class="bpmpi_billto_city" value="{{enderecoCobranca.cidade.nome}}" />
        <input type="hidden" class="bpmpi_billto_state" value="{{enderecoCobranca.cidade.estado.sigla}}" />
        <input type="hidden" class="bpmpi_billto_zipcode"   value="{{enderecoCobranca.cep}}" />
        <input type="hidden" class="bpmpi_billto_country"   value="BR" />
      </ng-container>

      <ng-container *ngIf="enderecoEntrega">
        <input type="hidden" class="bpmpi_shipto_street1"   value="{{obtenhaEndereco1(enderecoEntrega)}}" />
        <input type="hidden" class="bpmpi_shipto_street2"   value="{{obtenhaEndereco2(enderecoEntrega)}}" />
        <input type="hidden" class="bpmpi_shipto_city" value="{{enderecoEntrega.cidade.nome}}" />
        <input type="hidden" class="bpmpi_shipto_state" value="{{enderecoEntrega.cidade.estado.sigla}}" />
        <input type="hidden" class="bpmpi_shipto_zipcode"   value="{{enderecoEntrega.cep}}" />
        <input type="hidden" class="bpmpi_shipto_country"   value="BR" />
      </ng-container>

      <div class="col-12 col-sm-8">
        <div class="form-group ">
          <label for="numero">Número do cartão</label>

          <input type="text" class="form-control input-background " autocomplete="off"  [mask]="'0000-0000-0000-0009'" style="font-family: monospace;"
                 id="numero" name="numero" [(ngModel)]="cartao.numero" #numero="ngModel" #numeroElem appAutoFocus
                 required (ngModelChange)="alterouNumeroCartao()"  >


          <i class="fa fa-credit-card fa-2x" *ngIf="!paymentMethod"
             [ngClass]="{ 'text-muted': !cartao.bandeira,  ' text-success': cartao.bandeira && cartao.numeroValido}" > </i>


          <div class="invalid-feedback">
            <p *ngIf="numero.errors?.required">Obrigatório</p>
            <p *ngIf="numero.errors?.mask">Número do cartão de crédito inválido.</p>
          </div>
        </div>
      </div>

      <div class="col-12 col-sm-4">
        <div class="form-group ">
          <label for="validade">Data validade</label>
          <kendo-dateinput name="validade" id="validade" format="MM/yyyy" class="form-control"  required #validade="ngModel"
                           [(ngModel)]="cartao.validade">

          </kendo-dateinput>


          <div class="invalid-feedback">
            <p *ngIf="validade.errors?.required">Obrigatório</p>
          </div>
        </div>
      </div>

      <div class="col-12 col-sm-8">
        <div class="form-group ">
          <label for="nome">Nome do titular (como está gravado no cartão)</label>
          <input type="text" class="form-control" autocomplete="off" required
                 id="nome" name="nome" [(ngModel)]="cartao.nome" #nome="ngModel"
                 placeholder="Nome impresso" value="" >
          <div class="invalid-feedback">
            <p *ngIf="nome.errors?.required">Obrigatório</p>
          </div>
        </div>
      </div>

      <div class="col-12 col-sm-4">
        <div class="form-group ">
          <label for="cvv">Código de segurança</label>
          <input type="text" class="form-control" autocomplete="off" [mask]="'0009'"
                 id="cvv" name="cvv" [(ngModel)]="cartao.cvv" #cvv="ngModel"
                 required    >
          <div class="invalid-feedback">
            <p *ngIf="cvv.errors?.required">Obrigatório</p>
          </div>
        </div>
      </div>

      <div class="col-12 col-sm-5">
        <div class="form-group">
          <label for="nome">CPF</label>
          <input type="text" class="form-control" autocomplete="off" required
                 id="cpf" name="cpf" [(ngModel)]="cartao.cpf" #cpf="ngModel"
                 mask="000.000.000-00" cpfValido placeholder="___.___.___-__" value="">
          <div class="invalid-feedback">
            <p *ngIf="cpf.errors?.required">Obrigatório</p>
            <p *ngIf="cpf.errors?.cpfInvalido">CPF é invalido</p>
          </div>
        </div>
      </div>

      <div class="form-group  col-12 col-sm-7">
        <label for="nome">Email</label>
        <input kendoTextBox id="email" name="email" placeholder="Informe seu email"
               class="form-control"   #email="ngModel"
               [(ngModel)]="cartao.email" required/>
        <div class="invalid-feedback">
          <p *ngIf="email.errors?.required">Email é obrigatório</p>
        </div>
      </div>
    </div>

    <div class="row" *ngIf="parcelamento && parcelamento.parcelas.length">
      <div  class="col-12 mt-2 form-group">
        <label for="parcelamento">Número de parcelas</label>

        <kendo-combobox id="parcelamento"  name="parcelamento" [(ngModel)]="cartao.parcelamento" [data]="parcelamento.parcelas " required
                        textField = 'descricao'  #parcelamentoModel="ngModel"
                        placeholder="Selecione a quantidade de parcelas" class="form-control" >
        </kendo-combobox>

        <div class="invalid-feedback">
          <p *ngIf="parcelamentoModel.errors?.required">Obrigatório</p>
        </div>
      </div>
    </div>


    <div *ngIf="mensagemSucesso" class="alert alert-success alert-dismissible fade show mt-2" role="alert">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemSucesso}}
    </div>

    <div *ngIf="mensagemErro" class="alert alert-danger alert-dismissible fade show mt-2" role="alert">
      <i class="mdi mdi-check-all mr-2"></i> {{mensagemErro}}
    </div>
  </div>
</form>
