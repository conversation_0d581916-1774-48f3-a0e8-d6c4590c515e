<div class="tab-bar" [hidden]="!exibirMenu">

  <a class="tab-item" (click)="clicouExibirMenuCategorias()" [ngClass]="{'ativo': estaNaBuscaPorCategoria}" [hidden]="!exibirMenuCategorias">
    <div class="tab-icon">   <i class="fe-filter"></i>    </div>
    <div class="tab-title">      Categorias  </div>
  </a>

  <a class="tab-item" (click)="naveguePara('/')"
     [ngClass]="{'ativo': estaNoHome}" >
    <div class="tab-icon">  <i class="fe-home"></i></div>
    <div class="tab-title"> Home</div>
  </a>

  <a class="tab-item" [hidden]="empresa?.exibirBrindes" (click)="busqueProdutos()" [ngClass]="{'ativo': url.startsWith('busca')}">
    <div class="tab-icon">      <i class="fe-search"></i>     </div>
    <div class="tab-title">    Busca </div>
  </a>

  <a class="tab-item" [hidden]="!empresa?.exibirBrindes" (click)="exibirTelaBrindes()" [ngClass]="{'ativo': url.startsWith('brindes')}">
    <div class="tab-icon">  <i class="fe-award"></i>     </div>
    <div class="tab-title">    Brindes </div>
  </a>


  <a class="tab-item" (click)="naveguePara('meusPedidos')"   [ngClass]="{'ativo': url==='meusPedidos'}">
    <div class="tab-icon">
      <i class="fe-shopping-bag"></i>
    </div>
    <div class="tab-title">
      Pedidos
    </div>
  </a>

  <a class="tab-item " (click)="naveguePara('perfil')"
     [ngClass]="{'ativo': url==='perfil' || url.indexOf('cadastro') >=0 || url==='login'}" *ngIf="empresa && empresa.tema !== 'quiosque'">
    <div class="tab-icon">
      <i class="fe-user"></i>
    </div>
    <div class="tab-title" *ngIf="usuario?.telefone">
      Perfil
    </div>
    <div class="tab-title" *ngIf="!usuario || !usuario.telefone">
      Entrar
    </div>
  </a>
  <a class="tab-item " href="https://meucardapio.ai" target="_blank" *ngIf="exibirPoweredBy">
    <div class="tab-icon">
      <img src="https://fibo.promokit.com.br/lp/cardapio/images/logo-meucardapio.png" style="width: 45px;"/>
    </div>
    <div class="tab-title" style="font-size: 11px;">
      Powered By
    </div>
  </a>
</div>
