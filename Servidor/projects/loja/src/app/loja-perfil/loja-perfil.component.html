<app-header-loja [titulo]="'Minha Área'"></app-header-loja>

<h4 class="mt-3" *ngIf="usuario.nome">Ol<PERSON>, {{usuario?.nome?.split(' ')[0]}}</h4>

<div *ngIf="usuario.saldoFidelidade >=0"  class="saldo-info" [ngClass]="{'gendai': empresa.idRede === 2}">
  <a  (click)="verDetalhesSaldo(usuario)">
    <h5 class="">

      <ng-container *ngIf="!usuario.atualizarCadastro &&  !usuario.fazerOptinUsarSaldo && !usuario.fazerOptin">
        {{usuario.idCartao ? 'Saldo' : 'Cashback'}}
        <span class="font-18"><b> {{usuario.saldoDescricao}}</b></span>

        <span class="ml-1" style="    top: -3px; position: relative">
               <i class="k-icon k-i-reload   cpointer"  kendoTooltip title="atualizar saldo" *ngIf="!usuario.erroFidelidade"
                  (click)="recarregueSaldo(usuario, $event)" [hidden]="carregandoSaldo"></i>

            <i class="k-icon k-i-loading" *ngIf="carregandoSaldo"></i>
           <i  class="fas fa-exclamation-triangle text-warning"
               *ngIf="usuario.erroFidelidade" kendoTooltip title="{{usuario.erroFidelidade}}" > </i>
       </span>

      </ng-container>

      <span class="text-primary"  *ngIf="usuario.atualizarCadastro"><b>Atualize seu cadastro</b></span>
      <span class="text-primary"  *ngIf="usuario.fazerOptinUsarSaldo || usuario.fazerOptin"><b>Quero ganhar cashback</b></span>

      <button class="btn btn-blue btn-sm btn-small ml-1" *ngIf="usuario.idCartao">extrato</button>

      <small class="text-muted mt-1 d-block"> {{usuario.descricaoProgramaFidelidade}}</small>

      <img src="{{empresa.integracaoFidelidade?.logo}}" class="ml-2"
             *ngIf="empresa.integracaoFidelidade?.logo">

    </h5>
  </a>
</div>

<nav class="menu-itens">
  <div class="item-menu">
    <a class="" routerLink="/loja/meusPedidos">
      <span class="icon">
         <i class="fe-shopping-bag"></i>
      </span>
      <span>
         Pedidos
      </span>
      <span class="arrow">
        <i class="fe-chevron-right"></i>
      </span>
    </a>
  </div>
  <div class="item-menu">
    <a class="" routerLink="/loja/meus-dados">
      <span class="icon">
         <i class="fe-edit"></i>
      </span>
      <span>
         Meus dados
      </span>
      <span class="arrow">
       <i class="fe-chevron-right"></i>
      </span>
    </a>
  </div>
  <div class="item-menu">
    <a class="" routerLink="/loja/configuracoes">
      <span class="icon">
         <i class="fas fa-wrench"></i>
      </span>
      <span>
         Configurações
      </span>
      <span class="arrow">
       <i class="fe-chevron-right"></i>
      </span>
    </a>
  </div>
  <div class="item-menu">
    <a class="" href="#" (click)="facaLogout($event)">
      <span class="icon">
         <i class="fe-log-out"></i>
      </span>
      <span>
         Sair
      </span>
      <span class="arrow">
          <i class="fe-chevron-right"></i>
      </span>
    </a>
  </div>
</nav>






