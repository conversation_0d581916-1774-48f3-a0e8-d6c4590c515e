import {Component, EventEmitter, Input, OnInit, Output, ViewChild} from '@angular/core';
import {FormEnderecoComponent} from "../../form-endereco/form-endereco.component";
import {AutorizacaoLojaService} from "../../../services/autorizacao-loja.service";
import {EnderecoService} from "../../../services/endereco.service";
import {Endereco} from "../../../objeto/Endereco";
import {TelaListaLojasComponent} from "../tela-lista-lojas/tela-lista-lojas.component";

@Component({
  selector: 'app-tela-endereco-redirecionar',
  templateUrl: './tela-endereco-redirecionar.component.html',
  styleUrls: ['./tela-endereco-redirecionar.component.scss']
})
export class TelaEnderecoRedirecionarComponent implements OnInit {
  @ViewChild('telaEndereco', {static: false}) telaEndereco:  FormEnderecoComponent;
  @ViewChild('telaListaLojas', {static: false}) telaListaLojas: TelaListaLojasComponent;

  pedido: any;
  window: any;
  carregando = false;
  carregou = false;
  enderecos = [];
  enderecoEscolhido: any;
  endereco: any;
  calculandoTaxa = false;
  msgErro = '';
  grupoDeLojas = '';
  objGrupoDeLojas: any = {};
  escolherLojaRetirada = false;
  urlInteligente = '';

  @Output() telaCarregou = new EventEmitter();
  @Input() labelSalvarEndereco = 'Salvar Endereço';

  constructor(private autorizacao: AutorizacaoLojaService,
              private enderecoService: EnderecoService) {

  }

  ngOnInit() {
    let endereco = this.pedido && this.pedido.entrega ?  this.pedido.entrega.endereco : {}

    this.enderecoEscolhido =  this.endereco != null;

    this.autorizacao.obtenhaEnderecos().then( (enderecos) => {
      this.carregando = false;
      this.carregou = true;

      if( endereco && !endereco.id) {
        enderecos.unshift(endereco);
      }

      this.setEnderecos(enderecos)
    }).catch( (erro) => {
      this.carregou = true;
    });
  }

  salveEndereco(endereco) {
    this.enderecoService.encontreLoja(this.grupoDeLojas, endereco).then( (lojaEncontrada) => {
      if( !lojaEncontrada.encontrou ) {
        this.telaEndereco.calculandoTaxa = false;
        this.escolherLojaRetirada = true;
        return;
      }

      this.window.close(endereco);

      let linkLoja = lojaEncontrada.link + this.urlInteligente;
      if( linkLoja.indexOf('api.whatsapp.com') !== -1 ) {
        linkLoja = lojaEncontrada.link;
      }

      window.location.href = linkLoja;
    }).catch( (erro) => {
      this.telaEndereco.calculandoTaxa = false;
      this.escolherLojaRetirada = true;
      setTimeout( () => {
        this.telaListaLojas.carregueLojas();
      });
    });
  }

  setEnderecos(enderecos: any = []){
    this.enderecos = []
    enderecos.forEach( (dadosEndereco: any) => {
      let endereco = Endereco.novo();

      Object.assign(endereco, dadosEndereco)

      if(dadosEndereco.cidade){
        endereco.cidade = dadosEndereco.cidade;
        endereco.estado = dadosEndereco.cidade.estado;
        endereco.estado = dadosEndereco.cidade.estado;
      }

      if( !endereco.descricaoCompleta ) {
        endereco.descricaoCompleta = endereco.obtenhaEnderecoCompleto();
      }
      this.enderecos.push(endereco)
    });

    if(this.enderecos.length && !this.enderecoEscolhido)
      this.endereco =  this.enderecos[0]
    else if (!this.enderecos.length &&    !this.enderecoEscolhido){
      this.enderecoEscolhido = true;
    }
  }

  busqueCEP(cep: string) {
    this.telaEndereco.busqueCEP(cep);
  }

  fnTelaCarregou($event: any) {
    this.telaCarregou.emit($event);
  }
}
