import {IUITipoDePontuacaoLoja} from "./IUITipoDePontuacaoLoja";
import {ProdutoCashbackUtilsLoja} from "./ProdutoCashbackUtilsLoja";

export class IUITipoDePontuacaoPorCashback implements IUITipoDePontuacaoLoja {
  constructor(private empresa: any, private tipoDePontuacao: any, private tipoDeAcumulo: string, private atividade: any) {
  }

  calculePontos(valorVenda: number, itens: any) {
    return ProdutoCashbackUtilsLoja.calculeCashbakDosProdutos(valorVenda, itens, this.atividade)
  }

  obtenhaPontosFormatado(valorVenda: number, itens: any) {
    let cashback = this.calculePontos(valorVenda, itens);

    if(!cashback) return null;

    return String(`R$ ${cashback.toFixed(2).replace('.', ',')}`)
  }
}
