import {ItemPedido} from "./ItemPedido";
import {Entrega} from "./Entrega";
import {Pagamento} from "./Pagamento";
import {CupomPedido} from "./CupomPedido";
import {IUITipoPontuacaoFactory} from "./IUITipoPontuacaoFactory";
import {ObjetoComAdicionais} from "./ObjetoComAdicionais";
import {TaxaCobranca} from "./TaxaCobranca";
import {DescontoPromocional} from "./DescontoPromocional";
import {FormaDeEntrega} from "./FormaDeEntrega";
import {DataHoraUtils} from "../../../../src/app/lib/DataHoraUtils";

export class PedidoLoja extends ObjetoComAdicionais {
  itens = [];
  total = 0.0;
  totalResgatado = 0;
  subTotal = 0.0;
  qtde = 0;
  entrega: Entrega;
  troco: number;
  novosEnderecos = [];
  pagamento: Pagamento;
  contato: any;
  codigo: string;
  guid: string;
  mesa: any;
  pago: boolean;
  dataEntrega: Date
  horarioEntrega: String
  gerarLinkPagamento: boolean
  linkPagamento: string
  cupom: CupomPedido;
  promocoesAplicadas: DescontoPromocional[] = []
  desconto: number;
  descontoFormaDePagamento: number;
  taxaFormaDePagamento: number;
  timestamp: any
  codigoCupom: string;
  pontosReceber: number;
  cashback;
  resgate: any;
  adicionais: any;
  camposAdicionais: any;
  disparouEventoIniciarCheckout = false;
  disparouEventoAddedToCart = false;
  itensPorEmpresa: any = {};
  fidelidadeGcom: any;
  acumulo: string;
  idComanda: number;
  constructor(public origem: string  = null, contato: any = null, mesa: any = null) {
    super();
    this.entrega = new Entrega();
    this.pagamento = new Pagamento();
    this.mesa = mesa;
    this.setContato( contato || {})
  }

  setContato(contato: any){
    this.contato = contato;
    if(this.contato.dataNascimento)
      this.contato.dataNascimento = DataHoraUtils.obtenhaDataDaString(this.contato.dataNascimento);
  }

  edite(indiceItem: any, produto: any, qtde: number, observacao: string, adicionais: any, tamanho: any , sabores: any) {
    let itemExistente: ItemPedido = this.itens[indiceItem]

    itemExistente.atualize(qtde, observacao, adicionais, tamanho , sabores);
    this.limpeTroco();
    this.calculeTotal()

    return itemExistente;
  }

  limpeTroco(){
    if(this.pagamento) this.pagamento.trocoPara = 0;
  }



  expirou(){
    if(!this.timestamp) return true;
    let tempoMinutos =  (new Date().getTime() - this.timestamp) / 1000 / 60;

    return tempoMinutos >  240 ; //4h
  }

  adicione(produto: any, qtde: number, observacao: string, adicionais: any, tamanho: any , sabores: any) {
    const itemPedido = new ItemPedido(produto, qtde, observacao);

    itemPedido.adicionais = adicionais
    itemPedido.produtoTamanho = tamanho
    itemPedido.sabores = sabores || []
    itemPedido.atualizeTotal()
    this.itens.push(itemPedido);

    this.limpeTroco();

    this.calculeTotal();

    this.itensPorEmpresa = Object.values(this.calculeItensPorEmpresa());

    if(produto.brinde && produto.cupom){
      this.cupom = produto.cupom;
      this.cupom.aplicarAuto = this.cupom.brindeResgate = true;
    }


    return itemPedido;
  }

  calculeItensPorEmpresa() {
    let mapGrupos = {};

    for( let itemProduto of this.itens ) {
      const empresa = itemProduto.produto.empresa;

      if( !mapGrupos[empresa.id] ) {
        mapGrupos[empresa.id] = {
          empresa: empresa,
          itens: []
        };
      }

      mapGrupos[empresa.id].itens.push(itemProduto);
    }

    return mapGrupos
  }

  obtenhaTotalPontos(){
    let  totalEmPontos = 0;
    this.itens.forEach( ( itemPedido: any) => {
      if(itemPedido.brinde){
        totalEmPontos += itemPedido.valorResgatado;
        this.acumulo = itemPedido.produto.acumulo;
      }

    });

    return  Number(totalEmPontos.toFixed(2));
  }
  obtenhaSubTotal() {
    let subTotal = 0.0;

    this.itens.forEach( ( itemPedido: any) => {
      if(!itemPedido.brinde)
        subTotal += itemPedido.obtenhaValor();

    });

    return  Number(subTotal.toFixed(2));
  }

  obtenhaValorAhPagar() {
    let valor = this.total;

    if(this.cashback)
      valor -= this.cashback.valor;

    return Number(valor.toFixed(2));
  }


  obtenhaTotalSemTaxaEntrega(){
    let subtotal = this.obtenhaSubTotal();

    if(this.desconto)
      if(!this.cupom || !this.cupom.aplicarNaTaxaDeEntrega)
        subtotal -= this.desconto;

    if(this.descontoFormaDePagamento)
      subtotal -= this.descontoFormaDePagamento

    return subtotal;

  }

  private obtenhaValorPagoQuePontua() {
    if(this.cupom && this.cupom.naoPontuarFidelidade) return 0;

    let subtotal = this.obtenhaTotalSemTaxaEntrega();

    if(this.cashback && this.cashback.valor)
      subtotal = subtotal - this.cashback.valor;

    if(subtotal < 0)
      subtotal = 0

    return subtotal;
  }

  atualizeTotal() {
    this.configureAdicionais();

    this.calculeTotal();

    this.removaPagamentoSelecionado();
  }

  removaPagamentoSelecionado(){
    if(this.pagamento) this.pagamento.limpePagamento();
  }

  calculeTotal(){
    this.qtde = this.itens.length;
    this.subTotal = this.obtenhaSubTotal();
    this.total = this.subTotal;

    this.total +=  this.obtenhaValorAdicionais();
    this.totalResgatado  = this.obtenhaTotalPontos();

    if(this.desconto)
      this.total -= this.desconto;

    this.descontoFormaDePagamento = null;

    if(this.pagamento.formaDePagamento){

      if(this.pagamento.formaDePagamento.possuiDesconto) {
        let total = this.total;

        if(this.cashback && this.cashback.podeUsarNoPedido)
          total = this.total - this.cashback.valor

        this.descontoFormaDePagamento = Number((total * this.pagamento.formaDePagamento.desconto / 100).toFixed(2));
        this.total -= this.descontoFormaDePagamento;
      }
    }

    if( this.entrega && this.entrega.taxaDeEntrega && this.entrega.taxaDeEntrega > 0 )
      this.total += this.obtenhaValorTaxaEntrega()


    if(this.pagamento.formaDePagamento && this.pagamento.formaDePagamento.taxaCobranca){
      this.taxaFormaDePagamento = TaxaCobranca.calcule(this.pagamento.formaDePagamento.taxaCobranca, this.obtenhaValorAhPagar())
      this.total +=    this.taxaFormaDePagamento;
    } else {
      this.taxaFormaDePagamento  = 0;
    }

    this.calculeTroco();
  }

  obtenhaValorTaxaEntrega(){
    if(!this.entrega) return 0;

    if(this.entrega.valorComRetorno)
      if(!this.pagamento  ||  !this.pagamento.formaDePagamento  || this.pagamento.formaDePagamento.cobrarTaxaRetorno)
        return this.entrega.valorComRetorno

    return this.entrega.taxaDeEntrega
  }

  removaItem(item: any) {
    let index = this.itens.indexOf(item);

    this.itens.splice(index, 1);

    if(item.produto && item.produto.cupom)
      if(this.cupom && this.cupom.id === item.produto.cupom)
        this.removaCupom();

    this.limpeTroco();
    this.calculeTotal();

    this.itensPorEmpresa = Object.values(this.calculeItensPorEmpresa())
  }

  obtenhaDescricaoPromocoes() {
    if(!this.promocoesAplicadas) return null

    return this.promocoesAplicadas.map((promocaoAplicada) => promocaoAplicada.descricao ).join(",")
  }

  getItem(produto: any): ItemPedido{
    return this.itens.find( item => item.produto.id === produto.id)
  }

  calculeTroco() {
    this.troco = 0;

    if (this.pagamento && this.pagamento.trocoPara)
      this.troco = this.pagamento.trocoPara - this.total

    if (this.troco < 0)
      this.troco = 0;

  }


  calculePontosAhReceber(empresa: any, integracao: any) {
    if (integracao.pontuando) {

      if(!this.mesa || integracao.pontuarMesas){
        if(this.cupom && this.cupom.cashback)
          integracao.atividade.cashback = this.cupom.cashback

        this.pontosReceber =
          IUITipoPontuacaoFactory.crie(empresa, integracao).obtenhaPontosFormatado(this.obtenhaValorPagoQuePontua(), this.itens);
      }
    }

  }

  obtenhaDadosEnvio(empresa: any) {
    let horarioEntrega = this.obtenhaHorarioEntregaAgendada(empresa)
    let dados: any = {
      contato: this.contato,
      itens: this.itens,
      mesa: this.mesa,
      guid: this.guid,
      cupom: this.cupom,
      promocoesAplicadas: this.promocoesAplicadas,
      codigo: this.codigo,
      pagamentos: [],
      horarioEntregaAgendada: horarioEntrega,
      textoHorarioEntregaAgendada: horarioEntrega ? horarioEntrega.toLocaleString("pt-BR") : null,
      formaDeEntrega: this.entrega.obtenhaForma(),
      taxaEntrega: this.obtenhaValorTaxaEntrega(),
      taxaCalculadaId: this.entrega.taxaCalculadaId,
      hash: this.entrega.hash,
      adicionais: this.adicionais,
      valoresAdicionais: this.valoresAdicionais,
      total: this.total,
      origem: this.origem,
      op: localStorage.getItem('operador')
    }

    if(!this.entrega.ehRetirada())
      dados.endereco =  this.entrega.endereco;

    let valorPagar =  this.obtenhaValorAhPagar();

    if(valorPagar > 0){
      if(this.pagamento && this.pagamento.formaDePagamento){
        dados.pagamentos.push(Object.assign({}, this.pagamento))
        dados.pagamentos[0].valor = valorPagar;
      }
    }

    if(this.cashback && this.cashback.valor){
      let dadosPagamento: any = {
        valor: this.cashback.valor,
        cartao: this.cashback.cartao,
        formaDePagamento:  empresa.formasDePagamento.find( forma => forma.nome === 'cashback' )
      }

      if(this.cashback.cashbackExterno)
        dadosPagamento.cashbackExterno = this.cashback.cashbackExterno;

      dados.pagamentos.push(dadosPagamento);
    }

    if(this.resgate && this.resgate.valor){
      let dadosPagamento: any = {
        valor: this.resgate.valor,
        formaDePagamento:  empresa.formasDePagamento.find( forma => forma.nome === 'resgate' )
      }

      for (let i = 0; i < this.itens.length; i++){
        let item: any = this.itens[i];

        if(item.brinde){
          item.cartao = { id: this.resgate.cartaoid}
          item.brinde = { id: item.produto.id};
        }
      }

      dados.pagamentos.push(dadosPagamento);
    }

    if(this.fidelidadeGcom){
      this.contato.adicionarFidelidadeGcom = true;
      this.contato.idCliente = this.fidelidadeGcom.id_cliente;
    }

    if(this.idComanda) dados.idComanda = this.idComanda;

    return dados;
  }

  informouFormaDePagamento(): boolean {
    return (this.pagamento && this.pagamento.formaDePagamento != null) || (this.cashback && this.cashback.valor === this.total) ||
      (this.resgate && this.total === 0 &&  this.totalResgatado === this.resgate.valor);
  }

  informouFormaDeEntrega(): boolean{
    return this.entrega && this.entrega.formaDeEntrega != null;
  }

  setNovoPagamento(){
    this.pagamento = new Pagamento();
  }

  temValorMinimoDaFormaEscolhida(formasDeEntrega: any){
    if(! this.entrega || !this.entrega.formaDeEntrega) return true;

    return this.temValorMinimo(this.entrega.formaDeEntrega, formasDeEntrega)
  }

  obtenhaValorMinimoDaFormaEscolhida(  formasDeEntrega: any){
    if(! this.entrega || !this.entrega.formaDeEntrega) return true;

    return this.obtenhaValorMinimoParaEntrega(this.entrega.formaDeEntrega, formasDeEntrega)
  }


  obtenhaFormaEscolhida( formasDeEntrega: any, nomeForma: string = this.entrega.formaDeEntrega){
    if(!formasDeEntrega) return null;

    //todo: :( gambi, hoje salva string a forma, retirar quando mudar para salvar obj
    if(nomeForma === FormaDeEntrega.COMERNOLOCAL)
      nomeForma = FormaDeEntrega.RETIRAR;

    return formasDeEntrega.find( (forma: any) => forma.nome ===  nomeForma );

  }

  obtenhaLabelFormaSelecionada(){
    if(!this.entrega.formaDeEntrega) return '';

    if(this.entrega.formaDeEntrega === FormaDeEntrega.RECEBER_EM_CASA)
      return 'entrega'

    return 'retirada';
  }

  temValorMinimo(nomeForma: any, formasDeEntrega: any) {
    let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);

    if(formaDeEntrega && formaDeEntrega.valorMinimoPedido){
      return this.obtenhaSubTotal() >= formaDeEntrega.valorMinimoPedido;
    } else {
      return true;
    }
  }


  obtenhaValorMinimoParaEntrega(nomeForma: any, formasDeEntrega: any){
    let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);

    if(formaDeEntrega && formaDeEntrega.valorMinimoPedido){
      return  formaDeEntrega.valorMinimoPedido;
    } else {
      return  0;
    }
  }

  ultrapassouValorMaximo(nomeForma: any, formasDeEntrega: any){
    let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);

    if(formaDeEntrega && formaDeEntrega.valorMaximoPedido){
      return this.obtenhaTotalSemTaxaEntrega() > formaDeEntrega.valorMaximoPedido;
    } else {
      return false;
    }
  }

  ultrapassouValorMaximoDaFormaEscolhida(formasDeEntrega: any){
    if(!this.entrega || !this.entrega.formaDeEntrega) return false;

    return this.ultrapassouValorMaximo(this.entrega.formaDeEntrega, formasDeEntrega)
  }


  obtenhaValorMaximoParaEntrega(nomeForma: any, formasDeEntrega: any){
    let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);

    if(formaDeEntrega && formaDeEntrega.valorMaximoPedido){
      return  formaDeEntrega.valorMaximoPedido;
    } else {
      return  0;
    }
  }

  obtenhaValorMaximoDaFormaEscolhida( formasDeEntrega: any){
    if(! this.entrega || !this.entrega.formaDeEntrega) return 0;

    return this.obtenhaValorMaximoParaEntrega(this.entrega.formaDeEntrega, formasDeEntrega)
  }


  permiteAgendamento(nomeForma: any, formasDeEntrega: any) {
   let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);

    return formaDeEntrega ? formaDeEntrega.permiteAgendamento : false;
  }

  obtenhaIntervaloAgendamento(nomeForma: any, formasDeEntrega: any) {
    let formaDeEntrega = this.obtenhaFormaEscolhida(formasDeEntrega, nomeForma);
    return formaDeEntrega ? formaDeEntrega.intervaloAgendamento : false
  }

  naoInformarHorarioNoAgendamento(nomeForma: any, formasDeEntrega: any) {
    if(formasDeEntrega) {
      let formaDeEntrega = formasDeEntrega.find( (forma: any) => forma.nome ===  nomeForma );

      if(!formaDeEntrega) return false;

      return formaDeEntrega.naoPerguntarHorario;
    }
  }

  private obtenhaHorarioEntregaAgendada(empresa: any): Date {
    if(!this.dataEntrega) return null;

    const naoInformarHorario = this.naoInformarHorarioNoAgendamento(this.entrega.formaDeEntrega, empresa.formasDeEntrega);
    if(!naoInformarHorario && !this.horarioEntrega) return null;

    let horarioAgendamento = new Date(this.dataEntrega.toDateString())

    if( this.horarioEntrega ) {
      horarioAgendamento.setHours(parseInt(this.horarioEntrega.split(':')[0], 10))
      horarioAgendamento.setMinutes(parseInt(this.horarioEntrega.split(':')[1], 10))
    }

    return horarioAgendamento

  }

  apliqueCupom(dados: any) {
    if(dados && dados.desconto >= 0) {
      this.cupom = new CupomPedido(dados.id, dados.desconto, dados.codigo, dados.aplicarNaTaxaDeEntrega,
                                   dados.naoPontuarFidelidade, dados.aplicarAuto, dados.cashback, dados.brindeResgate);

      if(dados.erro)
        this.cupom.erro = dados.erro;

      this.desconto += this.cupom.desconto;
      this.limpeTroco();
      this.calculeTotal();
    }
  }


  apliqueDescontoPromocional(respostaDescontos: any) {
    this.promocoesAplicadas = []
    if(respostaDescontos && respostaDescontos.desconto >= 0) {
      for(let promocaoAplicada of respostaDescontos.promocoesAplicadas) {
        let descontoPromocional = new DescontoPromocional(promocaoAplicada.id, promocaoAplicada.empresa, promocaoAplicada.descricao,
          promocaoAplicada.desconto, promocaoAplicada.cumulativa, promocaoAplicada.produtosAdicionados)

        if(promocaoAplicada.produtosRemovidos) {
          for(let produtoRemovido of promocaoAplicada.produtosRemovidos)
            this.removaItem(this.getItem(produtoRemovido))
        }


        this.promocoesAplicadas.push(descontoPromocional)
        console.log(descontoPromocional)
        this.desconto += descontoPromocional.desconto

        if(descontoPromocional.produtosAdicionados)
          for(let produtoAdicionado of descontoPromocional.produtosAdicionados)
            this.adicione(produtoAdicionado.produto, 1, produtoAdicionado.observacao, null, null, null)



      }
      this.calculeTotal();
    }

  }


  removaCupom() {
    delete this.codigoCupom
    delete this.cupom;
    this.desconto = 0;
    this.calculeTotal();
  }

  obtenhaDescricaoPagamento() {
    if(!this.pagamento || !this.pagamento.formaDePagamento ) {
      if(this.usandoCashbackNoPagamento())  return 'Fidelidade Cashback'

      if(this.resgatandoBrinde()) return 'Pontos Fidelidade'

      return ''
    }

    let descricao =   this.pagamento.formaDePagamento.descricao

    if(this.pagamento.dadosCartao && this.pagamento.dadosCartao.descricaoPagamento)
      descricao = this.pagamento.dadosCartao.descricaoPagamento
    else if(this.pagamento.formaDePagamento.carteiraDigital)
      descricao = this.pagamento.formaDePagamento.carteiraDigital

    if(this.usandoCashbackNoPagamento())  descricao = String(`Cashback + ${descricao}`)
    else if(this.resgatandoBrinde())
       descricao = String(`Pontos + ${descricao}`)

    return descricao;
  }

  ehDeMultiloja() {
    return this.origem === 'multiloja';
  }

  obtenhaQtdeItens(itemPedido: any){
    let qtde = 0

    for(let item of this.itens)
      if(itemPedido && itemPedido.guid !== item.guid)
         qtde += item.qtde;

    return qtde;
  }

  usandoCashbackNoPagamento() {
    return this.cashback && this.cashback.valor;
  }

  resgatandoBrinde(){
    return this.resgate && this.resgate.valor
  }

  resgatandoBrindeOuUsandoCashback(){
    return this.usandoCashbackNoPagamento() || this.resgatandoBrinde();
  }

  maisDeUmaFormaPagamento(){
    return this.pagamento && this.pagamento.formaDePagamento && this.resgatandoBrindeOuUsandoCashback();
  }

  teveAutenticacaoPagamento() {
    if(!this.pagamento) return false;

    return this.pagamento.dadosCartao && this.pagamento.dadosCartao.autenticacao3ds;
  }

  validarValorPedido(){
    return this.teveAutenticacaoPagamento() && this.pagamento.dadosCartao.valorPagamento;
  }


}
