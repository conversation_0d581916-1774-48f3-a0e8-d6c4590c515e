import {
  Component,
  ElementRef,
  Input,
  OnInit,
  ViewChild,
  Renderer2,
  ChangeDetectionStrategy,
  SimpleChanges, OnChanges, ApplicationRef, ChangeDetectorRef
} from '@angular/core';
import {DomSanitizer, SafeUrl} from "@angular/platform-browser";

@Component({
  selector: 'app-loja-bandeira-logo',
  templateUrl: './bandeira-logo-loja.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BandeiraLogoLojaComponent implements OnChanges {
  @Input() bandeira: any;
  @Input() descricao: string;
  textoString: string;
  @ViewChild('svg', { static: true }) svg: ElementRef;
  @ViewChild('img', { static: true }) img: ElementRef;
  constructor(private el: ElementRef,  private app: ApplicationRef,
              private cd: ChangeDetectorRef, private sanitizer: DomSanitizer) {}

  ngOnChanges(changes: SimpleChanges) {
    if (this.bandeira) {
      this.textoString = this.descricao ? this.descricao : this.bandeira.nome;

      if (this.bandeira.imagem && this.bandeira.imagem.indexOf('svg') >= 0) {
        this.svg.nativeElement.innerHTML = this.bandeira.imagem;

        const svgElement = this.el.nativeElement.querySelector('svg');

        if (svgElement) {
          svgElement.style.maxWidth = '30px';
          svgElement.style.maxHeight = '20px';
        }
      } else {
        this.bandeira.imagemUrl = this.bandeira.imagem;
      }

    }
  }
}
