<app-header-tela titulo="Pagamento" xmlns="http://www.w3.org/1999/html"></app-header-tela>

<form id="form" [ngClass]="{'needs-validation': !frm.submitted, 'was-validated': frm.submitted}"
      novalidate #frm="ngForm" (ngSubmit)="onSubmit()" *ngIf="pedido">
  <div  >
    <div class="mt-3"></div>

    <div class="alert alert-danger mt-2" role="alert" *ngIf="msgErro">
      {{msgErro}}
    </div>

    <ng-container *ngIf="pedido.taxaFormaDePagamento > 0">
      <div class=" pt-0  ">
        <div class="media mt-2">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Sub-total</span></h5>
          </div>
          <h4 class="mt-0  ">{{(pedido.total - pedido.taxaFormaDePagamento ) | currency: 'BRL'}}</h4>
        </div>
      </div>

      <div class=" pt-0 ">
        <div class="media borda-pontilhada">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>Taxa forma de pagamento</span></h5>
          </div>
          <h4 class="mt-0  ">{{pedido.taxaFormaDePagamento | currency: 'BRL'}}</h4>
        </div>
      </div>

    </ng-container>

    <ng-container *ngIf="pedido.totalResgatado">
      <div class="produto pt-0  mb-0 mt-2"  >
        <div class="media  ">
          <div class="media-body">
            <h5 class="mt-0 mb-1"><span>{{pedido.acumulo}} consumidos</span></h5>
          </div>
          <h4 class="mt-0 resgate-preco">
            -{{pedido.totalResgatado}} {{pedido.acumulo}}
          </h4>

        </div>
      </div>
      <div *ngIf="resgate && resgate.podeUsarNaLoja" class="box-resgate">
        <h5>Usar meus <b>{{pedido.acumulo}}</b> no resgate </h5>
        <div class="form-group mb-2 mt-2" [hidden]="resgate.atualizarCadastro">
          <input id="resgatar" name="resgatar" type="checkbox"
                 [(ngModel)]="resgate.usar"      class="k-checkbox" kendoCheckBox  readonly/>
          <label for="resgatar" class="ml-1"  >
              <span class="text-primary"><b>{{resgate.saldo}} {{pedido.acumulo}}</b> disponíveis</span>
          </label>
        </div>

        <a  (click)="irParaAtualizarCadastro()" *ngIf="resgate.atualizarCadastro" href="">
          <b>Atualizar meu cadastro </b>
          <br>
          <small class="text-muted">Atualize seu cadastro para utilizar seus pontos</small>
        </a>


      </div>
    </ng-container>

    <div class="produto mt-3">
      <div class="media ">
        <div class="media-body">
          <h4 class="mt-0 mb-1"><span>Total</span></h4>
        </div>
        <h4 class="mt-0 preco">{{pedido.total | currency: 'BRL'}}</h4>
      </div>
    </div>

    <ng-container *ngIf="cashback && cashback.podeUsarNaLoja">
      <h5>Usar meu <b>saldo de Cashback</b> para pagar </h5>

      <div class="k-display-inline-flex">

        <div class="flex-row">
          <a  (click)="irParaAtualizarCadastro()" *ngIf="cashback.atualizarCadastro" href="">
            <b>Atualizar meu cadastro </b>
            <br>
            <small class="text-muted">Atualize seu cadastro para utilizar seu cashback</small>
          </a>

          <ng-container *ngIf="cashback.fazerLogin">
            <a  (click)="irParaLogin()"  href="">
              <b>Identifique-se</b>
              <br>
              <small class="text-muted">Faça login para utilizar seu cashback</small>
            </a>

            <br>
            <button (click)="irParaVerSaldo()" class="btn btn-blue btn-xs mt-1">Ver meu saldo</button>
          </ng-container>


          <a  (click)="irparaOptin()" *ngIf="cashback.fazerOptin ||  cashback.fazerOptinUsarSaldo" href="">
            <b>Quero ganhar cashback</b>
            <br>
            <small class="text-muted">Participe do plano fidelidade para ganhar até 20% de cashback</small>
          </a>


          <div class="form-group mb-2 mt-2" [hidden]="cashback.atualizarCadastro || cashback.fazerLogin || cashback.fazerOptin || cashback.fazerOptinUsarSaldo">
            <input id="usarSaldo" name="usarSaldo" type="checkbox" (change)="marcouUsarCashback()"
                   [(ngModel)]="cashback.usar"      class="k-checkbox" kendoCheckBox [disabled]="cashbackDesabilitado()"/>
            <label for="usarSaldo" class="ml-1"  >
              Cashback disponível <span class="preco"><b>{{ obtenhaSaldo()}}</b></span>
            </label>

            <p class="mt-1" *ngIf="!cashback.podeUsar && cashback.minimo" >
              <i class="fa fa-exclamation-circle mr-1 text-warning "></i>
              Acumule <span class=" text-blue"><b>{{ cashback.minimo | currency:"BRL"}}</b></span> de cashback
              e utilize como forma de pagamento.</p>

            <p class="mt-1" *ngIf="cashback.podeUsar && !cashback.podeUsarNoPedido" >
              <i class="fa fa-exclamation-circle mr-1 text-warning "></i>
              Faça um pedido a partir de <span class=" text-blue"><b>{{ cashback.minimoPedido | currency:"BRL"}}</b></span>
              e utilize seu saldo como forma de pagamento.</p>

          </div>
        </div>

        <div class="flex-row">
          <img src="{{empresa.integracaoFidelidade?.logo}}" class="ml-2 logo-plano" [ngClass]="{'gendai': empresa.idRede === 2}"
               *ngIf="empresa.integracaoFidelidade?.logo">
        </div>
      </div>

      <p class="text-danger  " style="    font-size: 11px;font-weight: bold;}" *ngIf="this.cashback.erro">
        {{this.cashback.erro}}</p>

      <span [hidden]="obtenhaTotalPagar() === 0">
          <hr class="linha">

          <div class="produto pt-0 pb-2">
            <div class="media mt-2">
              <div class="media-body">
                <h5 class="mt-0 mb-1"><span>Total restante a pagar</span></h5>
              </div>
              <h4 class="mt-0 preco">{{obtenhaTotalPagar() | currency: 'BRL'}}</h4>
            </div>
          </div>
      </span>


    </ng-container>

    <div class="mt-3" *ngIf="carregando">
      <i class="k-i-loading k-icon"></i>
    </div>

    <span *ngIf="obtenhaTotalPagar() > 0" [hidden]="carregando">
      <h4 class="mb-2">Formas de pagamento</h4>

      <app-loja-formaspagamento-mobile #formasPagamentoAntigas *ngIf="!empresa.exibirBandeiras">
      </app-loja-formaspagamento-mobile>

      <app-loja-formaspagamento-mobile-nova #formasPagamentoNova  *ngIf="empresa.exibirBandeiras">
      </app-loja-formaspagamento-mobile-nova>


    </span>

    <div class="alert alert-danger mt-2" role="alert" *ngIf="msgErro">
      {{msgErro}}
    </div>

    <div style="height: 40px"></div>

    <footer class="footer"  class="{{empresa.tema}}">
      <div>
        <div class="row" style="padding: 15px;">
          <div class="col">
            <button class="btn btn-primary btn-block">Salvar Forma de Pagamento</button>
          </div>
        </div>
      </div>
    </footer>
  </div>
</form>
