import {AfterViewInit, ElementRef, OnInit, QueryList, ViewChild, ViewChildren, Directive} from '@angular/core';
import {ActivatedRoute, Router} from "@angular/router";
import {ProdutoService} from "../services/produto.service";
import {Location} from "@angular/common";
import {ClienteService} from "../services/cliente.service";
import {CarrinhoService} from "../services/carrinho.service";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {ItemPedido} from "../objeto/ItemPedido";
import {ITela} from "../objeto/ITela";
import {DominiosService} from "../services/dominios.service";
import {ConstantsService} from "../services/ConstantsService";
import {DialogService} from "@progress/kendo-angular-dialog";
import {SiteCampoAdicionalComponent} from "../app/site-campo-adicional/site-campo-adicional.component";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {AdicionaisCustomizadosComponent} from "../app/adicionais-customizados/adicionais-customizados.component";
import {PopupUtils} from "../objeto/PopupUtils";
import {AdicionalUtils} from "../objeto/AdicionalUtils";
import {SiteAdicionarProdutoComponent} from "../site-adicionar-produto/site-adicionar-produto.component";
import {NgImageSliderComponent} from "ng-image-slider";
import {DialogRef} from "@progress/kendo-angular-dialog";
import {SiteProdutoComponent} from "./site-produto.component";

declare var gtag;

@Directive()
export class SiteProdutoBase implements OnInit, ITela, AfterViewInit {
  @ViewChild('siteAdicionarProduto' , { static: false }) siteAdicionarProduto: SiteAdicionarProdutoComponent;
  @ViewChildren('adicionalComponent') ctrlAdicionais: QueryList<SiteCampoAdicionalComponent>;
  @ViewChild('adicionaisCustomizados', { static: false }) adicionaisCustomizados: AdicionaisCustomizadosComponent;
  @ViewChild('inputNovoPeso', { static: false }) inputNovoPeso: ElementRef;
  @ViewChild('inputNovaQtde', { static: false }) inputNovaQtde: ElementRef;
  @ViewChild('nav', { static: false }) nav: NgImageSliderComponent;

  produto: any;
  idProduto;
  itemPedido: ItemPedido = null;
  pedido: PedidoLoja;
  nomePagina: string;
  target: string;
  horarioAbertura: any;
  indiceItem: any;
  window: any;
  alterarPeso;
  novoPeso: number;
  topWindow: any;
  leftWindow: any;
  erro: string;
  carregando = true;
  selecionada = 0;
  imagensSlider: any  = [];
  msgErroAlterarPeso: string;
  imagens: any;
  alterarQtde = false;
  novaQtde: number;
  msgErroAlterarQtde: string;
  exibirComboSelecao = false;

  constructor(
    protected router: Router,
    protected produtoService: ProdutoService,
    protected deviceService: MyDetectorDevice,
    protected el: ElementRef,
    protected activatedRoute: ActivatedRoute,
    protected _location: Location,
    protected clienteService: ClienteService,
    protected carrinhoService: CarrinhoService,
    protected dominiosService: DominiosService,
    protected constantsService: ConstantsService
  ) {
    this.produto = window.history.state.produto;
    this.idProduto = + this.activatedRoute.snapshot.params['id'];
    this.indiceItem = this.activatedRoute.snapshot.params['posicao'];
    this.nomePagina = dominiosService.obtenhaRaizCardapio();
  }

  static abraComoPopup(router: Router, location: Location, activatedRoute: ActivatedRoute, dialogService: DialogService,
                     isMobile: boolean, produto: any, indiceItem: string = null,
                     itemPedido: any = null, onClose = null): DialogRef {

    let dimensao = PopupUtils.calculeAlturaLargura(isMobile)

    const windowRef: DialogRef = dialogService.open({
      title: null,
      content: SiteProdutoComponent,
      minWidth: 250,
      width: dimensao.largura,
      height: dimensao.altura
    });

    let telaProduto: SiteProdutoBase = windowRef.content.instance;

    telaProduto.produto = produto;

    let params: any = {
      c: produto.id
    };

    if( indiceItem ) {
      telaProduto.indiceItem = indiceItem;
      params.e = indiceItem;
    }

    if(itemPedido){
      telaProduto.itemPedido = itemPedido;
    }

    telaProduto.window = windowRef;

    PopupUtils.abraJanela(router, location, activatedRoute, windowRef, params, onClose);

    return windowRef;
  }

  ngOnInit() {
    this.pedido = this.carrinhoService.obtenhaPedido();
    this.target = this.activatedRoute.snapshot.queryParams.target;

    if (!this.produto && this.idProduto)
      this.produto = this.constantsService.produtosLoja.find(produtoLoja => produtoLoja.id === this.idProduto)

    //substitui \n por <br>
    if(this.produto.descricao)
      this.produto.descricao = this.produto.descricao.replace(/(?:\r\n|\r|\n)/g, '<br>');

    if(this.produto.tipoDeVenda === "Unidade")
      this.exibirComboSelecao = this.produto.incremento > 1

    if(this.indiceItem){
      this.editarItemPedido(this.indiceItem);
    } else if(this.itemPedido){
      this.prepareItemPedido(this.itemPedido);
    } else {
      this.crieItemPedido();
    }

    this.imagens = this.produto.imagens ? this.produto.imagens.map((imagem) => {
      return {
        linkImagem: imagem.linkImagem,
        root: 'empresa'
      }
    }) : null

    if(this.produto.imagemCodigoDeBarras && this.produto.imagemCodigoDeBarras.linkImagem) {
      if(!this.imagens) this.imagens = []

      this.imagens.push({
        linkImagem: this.produto.imagemCodigoDeBarras.linkImagem,
        root: 'produtos'
      })
    }

    if(this.imagens) {
      this.imagens.forEach((img) => {
        this.imagensSlider.push(
          {
            image: '/images/' + img.root + '/' + img.linkImagem,
            thumbImage: '/images/' + img.root + '/' + img.linkImagem
          })
      })
    }

    this.carregou();
  }

  carregou(){
    this.carregando = false;
    setTimeout(() => {
      if( this.siteAdicionarProduto ) {
        this.siteAdicionarProduto.setControlesAdicionais(this.ctrlAdicionais, this.adicionaisCustomizados);
      }
    }, 0)
  }

  voltar() {
    this._location.back();
  }

  private editarItemPedido(indiceItem: any) {
    let itemNoPedido = this.pedido.itens[indiceItem]
    this.prepareItemPedido(itemNoPedido)
  }

  private prepareItemPedido(itemNoPedido: any){
    let qtde = itemNoPedido ? itemNoPedido.qtde : (this.produto.valorInicial || 1);

    this.itemPedido = new ItemPedido(this.produto, qtde, itemNoPedido.observacao);

    this.itemPedido.guid = itemNoPedido.guid;

    if(itemNoPedido && itemNoPedido.adicionais)
      this.itemPedido.adicionais = itemNoPedido.adicionais

    if(itemNoPedido && itemNoPedido.produtoTamanho)
      this.itemPedido.produtoTamanho = this.produto.tamanhos.find(tamanho => tamanho.id === itemNoPedido.produtoTamanho.id);

    this.itemPedido.sabores = itemNoPedido.sabores;

    AdicionalUtils.prepareItemdoPedido(this.itemPedido);
  }

  private crieItemPedido() {
    let qtde = this.produto.valorInicial || this.produto.qtdeMinima;

    this.itemPedido = new ItemPedido(this.produto, qtde, null);

    AdicionalUtils.prepareItemdoPedido(this.itemPedido)
  }

  deveExibirTopo() {
    return false;
  }

  deveExibirMenu(){
    return false;
  }

  fecheTela() {
    this.window.close();
  }

  informarPeso() {
    if(!this.exibirUnidade()) { //produto vendido por unidade
      this.informarNovaQtde();
      return;
    }
    this.alterarPeso = true;
    this.msgErroAlterarPeso = null;
    this.novoPeso = this.itemPedido.qtde;
    if(!this.deviceService.isMobile()){
      this.topWindow = 250;
      this.leftWindow = 150;
    } else {
      this.topWindow = 150;
    }
    setTimeout(() => { this.inputNovoPeso.nativeElement.focus()}, 200);
  }

  obtenhaFormato() {
    if(!this.produto.unidadeMedida)
      return '#####'

    return String(`##### ${this.produto.unidadeMedida.sigla}`);
  }

  onAlterouTamanho(produtoTamanho: any) {
    this.ctrlAdicionais.forEach((ctlAdicioanais) => {
      ctlAdicioanais.alterouTamanho(produtoTamanho);
    })
  }

  escolheuNovaOpcao(opcao: any){
    if(this.ctrlAdicionais){
      this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
        ctlAdicioanal.exibaOuEscondaOpcaoQueDepende(opcao)
      });
    }

    if(opcao)
      this.scrollToNext(opcao.elementoHTML)
  }

  desmarcouNovaOpcao(opcao: any){
    this.ctrlAdicionais.forEach((ctlAdicioanal: SiteCampoAdicionalComponent) => {
      ctlAdicioanal.desmarcouOpcaoQueDepende(opcao)
    });
  }

  scrollToNext(adicionalComponent: any) {
    if(!adicionalComponent) return;

    if(adicionalComponent.parentNode && adicionalComponent.parentNode.nextElementSibling){
      const proximaDiv = adicionalComponent.parentNode.nextElementSibling.childNodes[0];
      if(proximaDiv)
        proximaDiv.scrollIntoView({ behavior: 'smooth', block: 'start', inline: 'nearest' });
    }
  }

  salveNovoPeso() {
    this.msgErroAlterarPeso = null;

    if(this.produto.pesoMinimo != null && this.novoPeso < this.produto.pesoMinimo) {
      this.msgErroAlterarPeso = 'Peso mínimo para esse produto é ' + this.produto.pesoMinimo + ' ' +
        this.produto.unidadeMedida.sigla;
      return;
    }

    if(this.produto.pesoMaximo != null && this.novoPeso > this.produto.pesoMaximo) {
      this.msgErroAlterarPeso = 'Peso máximo para esse produto é ' + this.produto.pesoMaximo + ' ' +
        this.produto.unidadeMedida.sigla;
      return;
    }

    if(this.novoPeso > 0){
      this.itemPedido.qtde = this.novoPeso;
      this.itemPedido.atualizeTotal();
    }

    this.fecheModal(true);
  }

  fecheModal(b: boolean) {
    this.alterarPeso = false;
  }

  obtenhaIncremento() {
    return this.produto.incremento || 1;
  }

  exibirUnidade() {
    return this.itemPedido.produto.tipoDeVenda &&
            this.itemPedido.produto.tipoDeVenda === 'Peso';
  }

  selecionou(i: number) {
    this.selecionada = i;
  }

  exibaFullScreen() {
    this.nav.imageOnClick(this.selecionada)
  }

  abriuImagem($event: number) {
    // Implementação vazia
  }

  onEsc($event: any) {
    $event.stopPropagation();
    $event.preventDefault();
  }

  salveAltereQtde() {
    this.msgErroAlterarQtde = null;

    if(this.produto.qtdeMinima != null && this.novaQtde < this.produto.qtdeMinima) {
      this.msgErroAlterarQtde = `Você deve escolher pelo menos ${this.produto.qtdeMinima} UN. desse produto.`
      return;
    }

    if(this.produto.qtdMaxima != null && this.novaQtde > this.produto.qtdMaxima) {
      this.msgErroAlterarQtde = `Você deve escolher até ${this.produto.qtdMaxima} UN. desse produto.`
      return;
    }

    if(this.novaQtde > 0){
      this.itemPedido.qtde = this.novaQtde;
      this.itemPedido.atualizeTotal();
    }

    this.alterarQtde = false;
  }

  informarNovaQtde() {
    //todo: fazer combo seleção posteriormente, por hora nao exibir campo
    if(this.exibirComboSelecao) return;

    this.alterarQtde = true;
    this.msgErroAlterarQtde = null;
    this.novaQtde = this.itemPedido.qtde;
    if(!this.deviceService.isMobile()){
      this.topWindow = 250;
      this.leftWindow = 150;
    } else {
      this.topWindow = 150;
    }
    setTimeout(() => { this.inputNovaQtde.nativeElement.focus()}, 200);
  }

  fecheModalQtde() {
    this.alterarQtde = false;
  }

  deveTerBordas() {
    return true;
  }

  deveExibirBannerTema() {
    return false;
  }

  ngAfterViewInit(): void {
    if(typeof gtag !== 'undefined') {
      try {
        gtag('event', 'view_item', {
          currency: 'BRL',
          value: this.produto.valorMinimo,
          items: [
            {
              id: this.produto.id,
              name: this.produto.nome,
              price: this.produto.valorMinimo,
              item_category: this.produto.categoria.nome,
              quantity: 1
            }
          ]
        });
      } catch (error) {
        console.log(error);
      }
    }
  }
}
