import {AfterViewInit, Component, Input, OnInit, ViewChild} from '@angular/core';
import {SafeResourceUrl} from "@angular/platform-browser";
import {ProdutoService} from "../services/produto.service";
import {MyDetectorDevice} from "../app/shared/MyDetectorDevice";
import {SiteProdutosComponent} from "../produtos/site-produtos.component";
import {ActivatedRoute, Router} from "@angular/router";
import {DialogService} from "@progress/kendo-angular-dialog";
import {Location} from "@angular/common";
import {CarrinhoService} from "../services/carrinho.service";
import {PedidoLoja} from "../objeto/PedidoLoja";
import {ConstantsService} from "../services/ConstantsService";
import {AutorizacaoLojaService} from "../services/autorizacao-loja.service";
import {PainelHorariosComponent} from "../app/painel-horarios/painel-horarios.component";

@Component({
  selector: 'app-topo',
  templateUrl: './topo.component.html',
  styleUrls: ['./topo.component.scss']
})
export class TopoComponent implements OnInit, AfterViewInit {
  @Input() empresa: any = {};
  @Input() exibirTopo = true;
  @Input() exibirBannerTema = false;
  @Input() exibirTitulo = true;
  carregou = false;
  @Input() topoReduzido = false;
  @ViewChild('slideshow') slideshow: any;
  mensagemErro: string;
  isDesktop: boolean;
  pedido: PedidoLoja;
  formaReceberEmCasa: any = {};
  usuario: any;
  constructor(private produtoService: ProdutoService, private deviceDetectorModule: MyDetectorDevice,
              private router: Router, private location: Location, private activatedRoute: ActivatedRoute,
              private dialogService: DialogService, private carrinhoService: CarrinhoService,
              private constantsService: ConstantsService, private autorizacao: AutorizacaoLojaService) {
    this.isDesktop = this.deviceDetectorModule.isDesktop();
    this.pedido = this.carrinhoService.obtenhaPedido();
    this.usuario = autorizacao.getUsuario() || {}
  }

  ngOnInit() {
    this.autorizacao.usuario$.subscribe( (usuario) => {
      if(!usuario) return;
      this.usuario = usuario;
    });
  }

  ngAfterViewInit(): void {
    this.constantsService.empresa$.subscribe( (data) => {
      if( !data ) return;

      this.carregou = true;
      this.empresa  = data;

      this.formaReceberEmCasa = this.obtenhaFormaDeEntregaReceberEmCasa(this.empresa);
    });
  }

  carregueDados() {
    this.carregou = true;
  }

  exibirFormaDeEntrega() {
    return this.formaReceberEmCasa && this.formaReceberEmCasa.perguntarEnderecoInicio;
  }

  mudarFormaDeEntrega() {
    SiteProdutosComponent.abraPedindoEndereco(null, this.router, this.location, this.activatedRoute,
      this.dialogService, true);
  }

  obtenhaFormaDeEntregaReceberEmCasa(empresa: any) {
    return empresa.formasDeEntrega.find( (forma) => {
      return forma.formaDeEntrega.nome === 'Receber em casa'
    });
  }

  verHorarios(horarioServico: any){
    PainelHorariosComponent.abraComoPopup(this.router, this.location, this.activatedRoute,
      this.dialogService, this.empresa, horarioServico)
  }
}
