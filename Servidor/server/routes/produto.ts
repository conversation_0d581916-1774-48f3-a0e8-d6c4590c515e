import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {Produto} from "../domain/Produto";
import {MapeadorDeProduto} from "../mapeadores/MapeadorDeProduto";
import {MapeadorDeAdicionalDeProduto} from "../mapeadores/MapeadorDeAdicionalDeProduto";
import {ProdutoService} from "../service/ProdutoService";
import {AdicionalDeProduto} from "../domain/delivery/AdicionalDeProduto";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeUnidadeMedida} from "../mapeadores/MapeadorDeUnidadeMedida";
import {EnumDisponibilidadeProduto} from "../lib/emun/EnumDisponibilidadeProduto";
import {CardapioPDFService} from "../service/CardapioPDFService";
import {Empresa} from "../domain/Empresa";
import {MapeadorDeOpcaoDeAdicionalDeProduto} from "../mapeadores/MapeadorDeOpcaoDeAdicionalDeProduto";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {MapeadorDeCategoria} from "../mapeadores/MapeadorDeCategoria";
import {Categoria} from "../domain/delivery/Categoria";
import {MapeadorDeImagemDoProduto} from "../mapeadores/MapeadorDeImagemDoProduto";
import {RegistroDeOperacaoService} from "../service/RegistroDeOperacaoService";
import {Ambiente} from "../service/Ambiente";
import {MapeadorDeRegistroDeOperacao} from "../mapeadores/MapeadorDeRegistroDeOperacao";
import {DTOResumoProdutosContato} from "../domain/relatorios/DTOResumoProdutosContato";
import {ImportadorProduto} from "../lib/integracao/ImportadorProduto";
import {EnumTipoDeOrigem} from "../lib/emun/EnumTipoDeOrigem";
// @ts-ignore
import moment = require("moment");
import {Catalogo} from "../domain/catalogo/Catalogo";
import {MapeadorDeCatalogo} from "../mapeadores/MapeadorDeCatalogo";
import {MapeadorDeProdutoNaEmpresa} from "../mapeadores/MapeadorDeProdutoNaEmpresa";
import {RotaGuard} from "../lib/permissao/RotaGuard";
import {MapeadorDeOpcaoNaEmpresa} from "../mapeadores/MapeadorDeOpcaoNaEmpresa";
import {MapeadorDeCategoriaNaEmpresa} from "../mapeadores/MapeadorDeCategoriaNaEmpresa";
import {CategoriaNaEmpresa} from "../domain/catalogo/CategoriaNaEmpresa";
import {MapeadorDeIntegracaoDelivery} from "../mapeadores/MapeadorDeIntegracaoDelivery";
import * as _ from "underscore";
import {MapeadorDeTag} from "../mapeadores/MapeadorDeTag";
import {MepeadorDeTagProduto} from "../mapeadores/MepeadorDeTagProduto";
import {TagProdutoTipoEnum} from "../domain/TagProduto";
import {MapeadorDeInsumo} from "../mapeadores/MapeadorDeInsumo";
import {MapeadorDeEstoque} from "../mapeadores/MapeadorDeEstoque";
import {Estoque} from "../domain/estoque/Estoque";
import {EstoqueService} from "../service/EstoqueService";
import {ProdutoBrindeFidelidade} from "../domain/ProdutoBrindeFidelidade";
import {ProdutoFactory} from "../lib/ProdutoFactory";

const router: Router = Router();

function usuarioAdminOuEmpresaDoUsuario(req: any, res: any, next: any){
  let idEmpresa =  req.empresa ? req.empresa.id : null;

  if(!idEmpresa)
    return res.json(Resposta.erro('Empresa não informada'))

  if(req.user.admin || req.empresa.id === Number(idEmpresa))
    return  next();

  res.json(Resposta.erro('Operação não permitida'))
}

router.get('/autocomplete/', usuarioAdminOuEmpresaDoUsuario, async (req: any, res: any) => {
  let termo: any;

  if(req.query.q)
    termo = req.query.q

  let idCatalogo = Number(req.query.cid)

  let mapeadorCatalogo = new MapeadorDeCatalogo()

  mapeadorCatalogo.selecioneSync({id: idCatalogo}).then(async (catalogo: Catalogo) => {

  let query: any = { idCatalogo: catalogo.id, ahVenda: true, termo: termo, inicio: 0, total: 20}

    let produtos = await new MapeadorDeProduto(catalogo).listeAsync(query);

    res.json(Resposta.sucesso(produtos))

  })

})

router.get('/adicionais-pedido', async(req: any, res) => {
  const empresa = req.empresa;

  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto();

  const dados = {
    catalogo: {
      id: empresa.catalogo.id
    },
    objeto: {
      id: empresa.catalogo.id
    }
  };

  mapeadorDeAtributo.listeAsync(dados).then( (adicionais: Array<AdicionalDeProduto>) => {
    adicionais.forEach((adicional: any) => {
      if(!adicional.opcoesDisponiveis) adicional.opcoesDisponiveis = []
      if(adicional.campoOrdenar )
        adicional.opcoesDisponiveis = _.sortBy( adicional.opcoesDisponiveis, (opcao: any) => opcao[adicional.campoOrdenar])
    })

    res.json(Resposta.sucesso(adicionais));
  });
});

async function obtenhaObjetoProduto(req: any): Promise<Produto> {
  return new Promise(async(resolve: any, reject: any) => {
    const idProduto: any = req.params.id;
    const idCatalogo: any = req.query.cid;
    const idEmpresa: any = req.query.eid;
    let inicio = new Date().getTime();
    console.log('buscar produto so agora: ' + req.params.id)

    let catalogo: Catalogo = req.empresa ? req.empresa.catalogo : null

    if (idCatalogo) catalogo = await new MapeadorDeCatalogo().selecioneSync({id: idCatalogo})

    let mapeador = new MapeadorDeProduto(catalogo)

    mapeador.selecioneSync({id: idProduto, idEmpresa: idEmpresa}).then((produto: any) => {
      console.log('tempo buscar produto: ' + (new Date().getTime() - inicio) / 1000)

      produto.camposAdicionais.forEach((adicional: any) => {
        if (!adicional.opcoesDisponiveis) adicional.opcoesDisponiveis = []
      })

      if (catalogo.precoPorEmpresa) {
        produto.precoNaEmpresa = produto.produtoNaEmpresa && produto.produtoNaEmpresa.preco ? produto.produtoNaEmpresa.preco : produto.preco
        produto.destaqueNaEmpresa = produto.produtoNaEmpresa && produto.precoNaEmpresa.destaque !== undefined
          ? produto.precoNaEmpresa.destaque : produto.destaque

        produto.novoPrecoNaEmpresa = produto.produtoNaEmpresa && produto.produtoNaEmpresa.novoPreco ?
          produto.produtoNaEmpresa.novoPreco : produto.novoPreco


        produto.percentualDescontoNaEmpresa = produto.produtoNaEmpresa && produto.produtoNaEmpresa.preco && produto.novoPrecoNaEmpresa ?
          Number((  100 - ( produto.novoPrecoNaEmpresa / produto.precoNaEmpresa ) * 100 ).toFixed(2)) : produto.percentualDesconto

        produto.camposAdicionais.forEach((adicional: AdicionalDeProduto) => {
          adicional.opcoesDisponiveis.forEach((opcao: any) => {
            opcao.valorNaEmpresa = opcao.opcaoNaEmpresa && opcao.opcaoNaEmpresa.valor ? opcao.opcaoNaEmpresa.valor : opcao.valor
          })
        })
      }

      if (catalogo.disponibilidadePorEmpresa) {
        produto.disponibilidadeNaEmpresa = produto.produtoNaEmpresa && produto.produtoNaEmpresa.disponibilidade >= 0 ?
          produto.produtoNaEmpresa.disponibilidade : produto.disponibilidade

        produto.camposAdicionais.forEach((adicional: AdicionalDeProduto) => {
          adicional.opcoesDisponiveis.forEach((opcao: any) => {
            opcao.disponibilidadeNaEmpresa = opcao.opcaoNaEmpresa &&
            (opcao.opcaoNaEmpresa.disponivel !== undefined && opcao.opcaoNaEmpresa.disponivel !== null &&
              opcao.opcaoNaEmpresa.id !== undefined) ?
              opcao.opcaoNaEmpresa.disponivel : opcao.disponivel
          })
        })
      }

      resolve(produto);
    });
  });
}

router.get('/tela/:id', async (req: any, res) => {
  const produto: Produto = await obtenhaObjetoProduto(req);

  await produto.processeParaLoja(req.empresa ? req.empresa.getCashback() : null);

  let imagemProduto = '';

  if( produto.imagens.length > 0 )
    imagemProduto = produto.imagens[0].linkImagem;

  res.render('loja/produto.ejs', {produto: produto, imagemProduto: imagemProduto});
});

router.get('/:id', async (req: any, res) => {
  obtenhaObjetoProduto(req).then( (produto) => {
    res.json({
      sucesso: true,
      data: produto
    });
  });
});

router.post('/', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const dados: any = req.body;

  let produto: Produto  = ProdutoFactory.crie(dados, empresa);

  new ProdutoService().salveProduto(produto).then( ( erro => {
    if(!erro){
      res.json(Resposta.sucesso(produto))
    }else{
      res.json(Resposta.erro(erro))
    }
  }))

});

router.put('/', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const empresa: Empresa = req.empresa;
  const dados: any = req.body;

  let produto: Produto  = ProdutoFactory.crie(dados, empresa)


  const inicio = new Date();
  new ProdutoService().salveProduto(produto).then( ( erro => {
    console.log('salvando produto: ' + (new Date().getTime() - inicio.getTime()));
    if(!erro){
      res.json(Resposta.sucesso( ))
    }else{
      res.json(Resposta.erro(erro))
    }

    new CardapioPDFService().gerePDF(empresa).then( () => {

    }).catch( (erroPDF: any) => {
      console.log('erro ao gerar cardapio pdf');
      console.log(erroPDF)

    })
  }));
});

router.put('/sincronize/disponibilidade', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body;

  new ProdutoService().sincronizeDisponibilidade(req.empresa, dados).then( ()   => {
    res.json(Resposta.sucesso())
  });

})

router.put('/sincronize', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {

  const produtos: any = req.body.produtos;
  const empresa: Empresa = req.empresa
  new ImportadorProduto().sincronizeProdutos(empresa.catalogo, produtos).then( async()   => {
    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso())
  }).catch( (erro) => {
    res.json(Resposta.erro(erro.message ? erro.message : erro))
  })
});

router.put('/remova/catalogo', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {

  const produtos: any = req.body.produtos;
  const empresa: Empresa = req.empresa

  new ImportadorProduto().removaProdutos(empresa.catalogo, produtos).then( async()   => {
    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso())
  }).catch( (erro) => {
    res.json(Resposta.erro(erro.message ? erro.message : erro))
  })
});

router.post('/pausarOpcional', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const idOpcao = req.body.idOpcao;
  const disponivel = req.body.disponivel;

  const mapeadorDeOpcao = new MapeadorDeOpcaoDeAdicionalDeProduto();
  const empresa: Empresa = req.empresa

  let catalogo = empresa.catalogo

  mapeadorDeOpcao.selecioneSync({id: idOpcao}).then( (opcao: OpcaoDeAdicionalDeProduto) => {
    if( !opcao) {
      return res.json(Resposta.erro('Parâmetros inválidos'));
    }

    if(catalogo.disponibilidadePorEmpresa) {
      const mapeadorDeOpcaoNaEmpresa = new MapeadorDeOpcaoNaEmpresa()

      if(!opcao.opcaoNaEmpresa || !opcao.opcaoNaEmpresa.id) {
        opcao.opcaoNaEmpresa = {}
        opcao.opcaoNaEmpresa.opcao = opcao
      }

      opcao.opcaoNaEmpresa.disponivel = disponivel
      opcao.opcaoNaEmpresa.empresa =  { id: req.empresa.id}

      if(!opcao.opcaoNaEmpresa.valor)
        opcao.opcaoNaEmpresa.valor = opcao.valor

      mapeadorDeOpcaoNaEmpresa.insiraSync(opcao.opcaoNaEmpresa).then(async () => {
        await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
        res.json(Resposta.sucesso('Disponibilidade atualizada'));

      })

    } else {
      opcao.disponivel = disponivel;

      mapeadorDeOpcao.atualizeDisponibilidade(opcao).then( async () => {
        await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
        res.json(Resposta.sucesso('Disponibilidade atualizada'));
      });
    }



  });
});

router.post('/pausarCategoria', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const idCategoria = req.body.idCategoria;
  const disponivel = req.body.disponivel;
  const empresa: Empresa = req.empresa;

  const mapeadorDeCategoria = new MapeadorDeCategoria(empresa.catalogo);
  const mapeadorDeCategoriaNaEmpresa = new MapeadorDeCategoriaNaEmpresa();
  mapeadorDeCategoria.selecioneSync({idEmpresa: empresa.id, id: idCategoria}).then( (categoria: Categoria) => {
    if( !categoria) {
      return res.json(Resposta.erro('Parâmetros inválidos'));
    }



    let promise = null;

    if(empresa.catalogo.disponibilidadePorEmpresa) {
      if(!categoria.categoriaNaEmpresa || !categoria.categoriaNaEmpresa.id)
        categoria.categoriaNaEmpresa = new CategoriaNaEmpresa(empresa, categoria)

      categoria.categoriaNaEmpresa.disponivel = disponivel;
      categoria.categoriaNaEmpresa.empresa = empresa
      promise = mapeadorDeCategoriaNaEmpresa.atualizeDisponibilidadeNaEmpresa(categoria.categoriaNaEmpresa)
    } else {
      categoria.disponivel = disponivel;
      promise = mapeadorDeCategoria.atualizeDisponibilidade(categoria)
    }

    promise.then( async() => {
        let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

        await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
        await new MapeadorDeEmpresa().removaListaDeCategoriasDaCache(empresa);
        registroDeOperacao.atualizouDisponibilidadeCategoria(categoria).then(() => {
          res.json(Resposta.sucesso('Disponibilidade atualizada'));
        })

        new CardapioPDFService().gerePDF(empresa).then( () => {

        }).catch( (erroPDF: any) => {
          console.log('erro ao gerar cardapio pdf', erroPDF);
        })
    });
  })
})

router.get('/:idCatalogo/:id/clone', RotaGuard.alterarCadastrarProdutos, async(req: any, res: any) => {
  let idCatalogo = req.params.idCatalogo

  let catalogo = await (new MapeadorDeCatalogo().selecioneSync({id: idCatalogo}))

  let mapeador = new MapeadorDeProduto(catalogo)

  mapeador.selecioneSync({id: req.params.id}).then((produto: Produto) => {
    if(!produto)
      return res.json(Resposta.erro("Não foi possível encontrar um produto com o id " + req.params.id))

    let produtoClonado = produto.clone()

    produtoClonado.nome = produtoClonado.nome + " (Cópia)"

    let produtoService = new ProdutoService()
    produtoService.salveProduto(produtoClonado).then(async() => {

      let promisesAdicionais = []
      if (produtoClonado.camposAdicionais && produtoClonado.camposAdicionais.length > 0)
        for (let adicional of produtoClonado.camposAdicionais) {
          promisesAdicionais.push(new Promise((resolveAdicional: any) => {
            produtoService.insiraAdicionalProduto(produtoClonado.catalogo, produtoClonado, adicional).then(() => {
              resolveAdicional();
            })
          }))
        }

      await new MapeadorDeProduto(catalogo).removaCacheProdutos();
      Promise.all(promisesAdicionais).then(() => {
        return res.json(Resposta.sucesso(produtoClonado))
      })
    })
  })
})

router.post('/adicional/reordenar', RotaGuard.alterarCadastrarProdutos, async(req: any, res: any) => {
  const dados: any = req.body


    if(!dados.cid)
       return res.json(Resposta.erro("É necessário informar o id do catálogo"))

   const idCatalogo = Number(dados.cid);


  let catalogo = await new MapeadorDeCatalogo().selecioneSync({id: idCatalogo})

  let mapeador = new MapeadorDeAdicionalDeProduto()

  //garantir que nao salve repetiddo
  for(let i = 0; i <  dados.adicionais.length; i++){
    let adicional: any =  dados.adicionais[i];
    adicional.catalogo = catalogo;
    if(adicional.ordem !== i){
      console.log(`adicional ${adicional.id}-${adicional.nome} com ordem repetidada/errada corrigindo: ${adicional.ordem} -> ${i}`)
      dados.adicionais[i].ordem = i;
    }
  }

  mapeador.reordeneAdicionais(dados.adicionais).then(async() => {
    let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

    await new MapeadorDeProduto(catalogo).removaCacheProdutos();
    registroDeOperacao.reordenouAdicionaisDoProduto({id: dados.pid, adicionais: dados.adicionais}).then(() => {
      res.json(Resposta.sucesso())
    }).catch((erro: any) => {
      res.json(Resposta.erro(erro))
    })


  }).catch((erro: any) => {
    res.json(Resposta.erro(erro))
  })
})

router.post('/adicional/opcao',  RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body;
  const idAdicional = dados.aid;
  const opcao = dados.opcao;
  const empresa: Empresa = req.empresa;
  let produto: any = {id: Number(dados.pid)};

  const adicional = await   new MapeadorDeAdicionalDeProduto().selecioneSync(idAdicional);
  let  catalogo = await new MapeadorDeCatalogo().selecioneSync({id: dados.cid});


  produto.catalogo = catalogo;

  await new ProdutoService().salveOpcaoAdicional(produto, adicional, opcao,
    dados.rede ? false : catalogo.precoPorEmpresa, dados.rede ?
      false : catalogo.disponibilidadePorEmpresa, empresa );


  res.json(Resposta.sucesso(opcao));
})

router.post('/adicional', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body;

  let adicional = dados.adicional;
  let produto: any = {};

  const empresa: Empresa = req.empresa;

  let catalogo = empresa.catalogo

  adicional.catalogo = catalogo;

  adicional.classe = adicional.tipo + '-' + adicional.entidade


  if(dados.cid)
    catalogo = await new MapeadorDeCatalogo().selecioneSync({id: dados.cid})


  let mapeadorDeProduto = new MapeadorDeProduto(catalogo)

  if(dados.pid) {
    produto = await mapeadorDeProduto.selecioneSync({id: dados.pid})
    adicional.ordem = produto.camposAdicionais ? produto.camposAdicionais.length : 1
  }


  if( adicional.entidade === 'pedido' ) {
    const adicionaisPedidos = await new MapeadorDeAdicionalDeProduto().listeAsync({
      objeto: catalogo.id,
      entidade: 'pedido'});
    adicional.ordem = adicionaisPedidos.length;
    produto = empresa.catalogo;
  }


  let adicionalNovo: any = await new ProdutoService().insiraAdicionalProduto(catalogo, produto, adicional)
    .catch((erroCriar) =>
    res.json(Resposta.erro(erroCriar))
  );
  //com com adicinal e opçoes com todos ids inseridos;

  if(adicionalNovo){
    let mapeador = new MapeadorDeProduto(empresa.catalogo)

    let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto();


    const adicionalAtualizado = await mapeadorDeAtributo.selecioneSync({id: adicional.id});

    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

    res.json(Resposta.sucesso({id: adicional.id, opcoesDisponiveis: adicionalAtualizado.opcoesDisponiveis }));

  }
})

router.put('/adicional', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body;

  let empresa: Empresa = req.empresa
  let catalogo = empresa.catalogo
  let adicional = dados.adicional;
  let produto: any = {id: Number(dados.pid)};

  if(!adicional.compartilhado) adicional.compartilhado = false;

  adicional.classe = adicional.tipo + '-' + adicional.entidade

  if(dados.cid) {
    catalogo = await new MapeadorDeCatalogo().selecioneSync({id: dados.cid})
  }

  produto.catalogo = catalogo


  await new ProdutoService().atualizeAdicionalProduto(produto, adicional,
    dados.rede ? false : catalogo.precoPorEmpresa, dados.rede ?
      false : catalogo.disponibilidadePorEmpresa, empresa );

  let mapeadorDeAtributo = new MapeadorDeAdicionalDeProduto();

  const adicionalAtualizado = await mapeadorDeAtributo.selecioneSync({id: adicional.id});

  if(catalogo.precoPorEmpresa)
    adicionalAtualizado.opcoesDisponiveis.forEach((opcao: any) => {
      opcao.valorNaEmpresa = opcao.opcaoNaEmpresa && opcao.opcaoNaEmpresa.valor ? opcao.opcaoNaEmpresa.valor : opcao.valor
    })

  if(catalogo.disponibilidadePorEmpresa)
    adicionalAtualizado.opcoesDisponiveis.forEach((opcao: any) => {
      opcao.disponibilidadeNaEmpresa = opcao.opcaoNaEmpresa && opcao.opcaoNaEmpresa.disponivel !== undefined ?
        opcao.opcaoNaEmpresa.disponivel : opcao.disponivel
    })


  await new MapeadorDeProduto(catalogo).removaCacheProdutos();

  if(empresa.integracaoDelivery) {
    empresa.integracaoDelivery.ultimaSincronizacaoDisponivel = null
    let mapeador = new MapeadorDeIntegracaoDelivery()
    await mapeador.atualizeUltimaSincronizacao(empresa.integracaoDelivery)
    await (new MapeadorDeEmpresa()).removaDasCaches(empresa)
  }


  res.json(Resposta.sucesso({id: adicional.id, opcoesDisponiveis: adicionalAtualizado.opcoesDisponiveis }));
});

router.put('/adicional/delete', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body;
  const empresa: any = req.empresa;
  let adicional = dados.adicional;
  let produto: any = {id: Number(dados.pid)};



  let removeu = await new ProdutoService().removaOuDesvincule( adicional, produto, empresa.catalogo).catch((erro) => {
    res.json(Resposta.erro(erro ));
  })

  if(removeu){
    let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

    await registroDeOperacao.removeuAdicionalDeProduto(produto, adicional)

    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

    res.json(Resposta.sucesso( ));
  }
});

router.post('/adicional/copiar', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body,
    idCatalogo = Number(dados.cid),
    dadosCopiar = dados.dadosCopiar;
  const clonar = dados.clonar;

  let mapeadorCatalogo = new MapeadorDeCatalogo();

  mapeadorCatalogo.selecioneSync({id: idCatalogo}).then(async (catalogo: Catalogo) => {
    let mapeador = new MapeadorDeProduto(catalogo)

    let produto =  await mapeador.selecioneSync({id: Number(dados.pid), idCatalogo: idCatalogo});
    let produtoCopiar: Produto = await mapeador.selecioneSync({id: Number(dadosCopiar.pid), idCatalogo: idCatalogo});
    let adicionais = produtoCopiar.camposAdicionais.filter( opcao => dadosCopiar.ids.indexOf(opcao.id) >= 0 );

    let novosAdicionais: any = await new ProdutoService().copieAdicionais(catalogo, produto, produtoCopiar, adicionais, clonar)
      .catch((err) => {
          res.json(Resposta.erro(err))
      });

    if(novosAdicionais){
      //com com adicinal e opçoes com todos ids inseridos
      let produtoAtualizado: Produto = await mapeador.selecioneSync({ id: produto.id, idCatalogo: idCatalogo});

      let adicionaisNovosComIds: any =
        produtoAtualizado.camposAdicionais.filter(
          adicionalProduto => novosAdicionais.map( (adicinalnovo: any) => adicinalnovo.id).indexOf(adicionalProduto.id) >= 0  );

      await new MapeadorDeProduto(catalogo).removaCacheProdutos();

      res.json(Resposta.sucesso( adicionaisNovosComIds));
    }
  })
})

router.put('/imagens', RotaGuard.alterarCadastrarProdutos, async (req: any, res: any) => {
  const dados: any = req.body
  const empresa: Empresa = req.empresa
  let mapeador = new MapeadorDeImagemDoProduto()

    await mapeador.atualizeImagens(dados);

    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

    res.json(Resposta.sucesso( ))

  })

router.put('/imagem', RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const dados: any = req.body,
    idEmpresa = Number(dados.eid);

    let mapeadorDeEmpresa = new MapeadorDeEmpresa().selecioneSync({id: idEmpresa}).then(async (empresa: Empresa) => {
      let mapeador = new MapeadorDeProduto(empresa.catalogo)

      dados.empresa = empresa;

      await mapeador.atualizeFoto(dados);

      await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

      res.json(Resposta.sucesso( ))
    })



  })

router.put('/pontosfidelidade', RotaGuard.alterarCadastrarProdutos, async (req: any, res: any) => {
    const dados: any = req.body;

    let produto: any = {id: dados.id , catalogo: req.empresa.catalogo  };

    if(dados.cashback != null){
      produto.cashback = dados.cashback
      produto.pontosGanhos = null
    } else if(dados.pontosGanhos != null){
      produto.pontosGanhos = dados.pontosGanhos;
      produto.cashback = null;
    }

    let mapeador = new MapeadorDeProduto(req.empresa.catalogo)
    mapeador.transacao( async (conexao: any, commit: any) => {

      if( dados.tamanhos && dados.tamanhos.length){
        for(let i = 0; i < dados.tamanhos.length; i++){
          let produtoTamanho: any =  dados.tamanhos[i];

          if(produtoTamanho.cashback != null)
            produtoTamanho.pontosGanhos = null

          if(produtoTamanho.pontosGanhos != null)
            produtoTamanho.cashback = null

          await mapeador.atualizePontosFidelidadeTamanho(produtoTamanho);
        }
      }

      await mapeador.atualizePontosFidelidade(produto);
      await mapeador.removaCacheProdutos();

      commit(() => {
        res.json(Resposta.sucesso(  {  } ))
      });
    });

  })

router.put('/preco', RotaGuard.alterarCadastrarProdutos, async (req: any, res: any) => {
  const dados: any = req.body,
    empresa: Empresa = req.empresa;

  if(!dados.cid)
    return res.json(Resposta.erro('É necessário informar um id de catálogo para essa operação'))

  const idCatalogo = Number(dados.cid);

  new MapeadorDeCatalogo().selecioneSync({id: idCatalogo}).then((catalogo: Catalogo) => {
    let mapeador = new MapeadorDeProduto(catalogo)


    mapeador.transacao( async (conexao: any, commit: any) => {
      dados.catalogo = {id: idCatalogo};

      if(dados.precoNaEmpresa)
        await new MapeadorDeProdutoNaEmpresa().atualizePrecoNaEmpresa(
          {preco: dados.precoNaEmpresa, empresa: empresa, produto: {id: dados.id}})
      else
        await  mapeador.atualizePreco(dados);

      let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

      await registroDeOperacao.alterouPrecoProduto(dados)

      await new MapeadorDeProduto(catalogo).removaCacheProdutos();
      conexao.commit(() => {
        res.json(Resposta.sucesso(  {   } ))
      });
    })
  });

})

router.put('/disponibilidade', RotaGuard.alterarCadastrarProdutos, async (req: any, res: any) => {
  const dados: any = req.body,
    empresa: Empresa = req.empresa;

    if(!dados.cid)
      return res.json(Resposta.erro('É necessário informar um id de catálogo para essa operação'))

    const idCatalogo = Number(dados.cid);

    new MapeadorDeCatalogo().selecioneSync({id: idCatalogo}).then((catalogo: Catalogo) => {
      let mapeador = new MapeadorDeProduto(catalogo)

      dados.catalogo = {id: idCatalogo};
      let salvarHorarios = dados.disponibilidade === EnumDisponibilidadeProduto.DiaDaSemana;

      if(salvarHorarios){
        if(!dados.disponibilidades || !dados.disponibilidades.length)
          return res.json(Resposta.erro('Informe pelo menos uma disponibilidade que produto estará disponivel'))
      }


      mapeador.transacao( async (conexao: any, commit: any) => {
        let porEmpresa: boolean = catalogo.disponibilidadePorEmpresa && empresa.catalogo.id === idCatalogo &&
          Number.isInteger(dados.disponibilidadeNaEmpresa);

        if(porEmpresa) {
          dados.produtoNaEmpresa.disponibilidade = dados.disponibilidadeNaEmpresa
          dados.produtoNaEmpresa.empresa =  { id: empresa.id}
          dados.produtoNaEmpresa.produto = {id: dados.id }
          await (new MapeadorDeProdutoNaEmpresa()).atualizeDisponibilidadeNaEmpresa(dados.produtoNaEmpresa)
        }
        else {
          await  mapeador.atualizeDisponibilidade(dados);


          if(salvarHorarios)
            await new ProdutoService().salveDisponibilidades(dados, catalogo);
          else if(dados.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel)
          {
            dados.disponibilidades = []
            await new ProdutoService().salveDisponibilidades(dados, catalogo);
          }
        }

        if(dados.insumoRemover)
          await new MapeadorDeInsumo().removaNaListaProdutos(dados.insumoRemover, dados);

        if(dados.insumo && dados.insumo.vincular){
          await new EstoqueService().registreVinculoInsumoAosProdutos(dados.insumo, [dados]);
        }

        try{
          if(dados.estoque && dados.controlarEstoque)
            await new EstoqueService().salveEstoqueDoProduto(dados, dados.estoque, req.user)


          let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

          if(porEmpresa){
            await registroDeOperacao.alterouDisponibilidadeDoProdutoDaEmpresa(dados)
          } else {
            await registroDeOperacao.alterouDisponibilidadeDoProduto(dados)
          }

          await new MapeadorDeProduto(catalogo).removaCacheProdutos();
          conexao.commit(() => {
            res.json(Resposta.sucesso(  { disponibilidades: dados.disponibilidades } ))
          });

        }catch (err){
          console.log(err)
          conexao.rollback(() => {
            res.json(Resposta.erro( err.message || err ))
          });
        }
    });
    })
});

router.put('/:id/tags', async (req: any, res: any) => {
  let produto: any = { id: Number(req.params.id), catalogo: req.empresa.catalogo};

  let tags: any = req.body;

  await new MepeadorDeTagProduto().salveTags(produto, tags);

  res.json(Resposta.sucesso())
})

router.put('/ordens' , RotaGuard.alterarCadastrarProdutos, async (req, res) => {
    const produtos: any = req.body.produtos,
      idEmpresa = Number(req.body.eid);

    new MapeadorDeEmpresa().selecioneSync({id: idEmpresa}).then((empresa: Empresa) => {
      let mapeador = new MapeadorDeProduto(empresa.catalogo)

      mapeador.transacao( async (conexao: any, commit: any) => {

        for(let i =  0; i < produtos.length; i++){
          let dados = produtos[i];

          dados.empresa = {id: idEmpresa};

          await mapeador.atualizeOrdem(dados)
        }

        commit( () => {
          res.json(Resposta.sucesso( ))
        })
      })
    })


  });

function obtenhaProduto(catalogo: Catalogo, produto: Produto, comando: string) {
    return new Promise( (resolve, reject) => {
      let mapeador = new MapeadorDeProduto(catalogo)

      if( comando === 'SUBIR' ) {
        mapeador.obtenhaProdutoAcima(produto).then( (produtoAcima: Produto) => {
          resolve(produtoAcima);
        });
      } else {
        mapeador.obtenhaProdutoAbaixo(produto).then( (produtoAbaixo: Produto) => {
          resolve(produtoAbaixo);
        });
      }
    });
  }

router.put('/subir1Posicao' , RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const idProduto: any = req.body.idProduto;
  const ordem: any = req.body.ordem;
  const empresa: Empresa = req.empresa;
  const idCatalogo = req.body.idCatalogo
  const comando = req.body.comando;

  let catalogo = idCatalogo ? await new MapeadorDeCatalogo().selecioneSync({id: idCatalogo}) : empresa.catalogo

  let mapeador = new MapeadorDeProduto(catalogo)

  mapeador.selecioneSync({id: idProduto}).then( (produto: Produto) => {
    if( !produto ) {
      return res.json(Resposta.erro('Produto não encontrado (subir1Posicao): ' + idProduto));
    }

    obtenhaProduto(catalogo, produto, comando).then( (produtoAcima: Produto) => {
      const aux = produto.ordem;

      if( produtoAcima === null || produtoAcima.id === null ) {
        res.json({
          sucesso: false,
          erro: "Houve um erro ao mudar as ordens: Produto já está no topo da categoria."
        });
        return;
      }

      produto.ordem = produtoAcima.ordem;
      produtoAcima.ordem = aux;

      mapeador.transacao( async (conexao: any, commit: any) => {
        const promiseAtualizacaoProduto = mapeador.atualizeOrdem(produto);
        const promiseAtualizacaoProdutoAcima = mapeador.atualizeOrdem(produtoAcima);

        Promise.all([promiseAtualizacaoProduto, promiseAtualizacaoProdutoAcima]).then( (resposta) => {
          commit( () => {
            res.json(Resposta.sucesso({
              ordem: produtoAcima.ordem,
              novaOrdem: produto.ordem
            }));
          });
        }).catch( (erro) => {
          conexao.rollback(() => {
            res.json({
              sucesso: false,
              erro: "Houve um erro ao mudar as ordens: " + erro
            })
          })
        });
      });
    });
  });
});

router.put('/subirParaTopo' , RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const idProduto: any = req.body.idProduto;
  const ordem: any = req.body.ordem;
  const empresa: Empresa = req.empresa;

  let mapeador = new MapeadorDeProduto(empresa.catalogo)

  mapeador.selecioneSync({id: idProduto}).then( (produto) => {
    if( !produto ) {
      return res.json(Resposta.erro('Produto não encontrado (subirParaTopo): ' + idProduto));
    }

    mapeador.selecioneMenorOrdemDaCategoria(produto.categoria).then( (ordemPrimeiro: number) => {
      mapeador.transacao( async (conexao: any, commit: any) => {
        produto.ordem = ordemPrimeiro;
        const dados = {
          idCategoria: produto.categoria.id,
          idCatalogo: empresa.catalogo.id,
          ordemProduto: ordem,
          idProduto: produto.id,
          ordemTopo: ordemPrimeiro
        };

        mapeador.atualizeSubirProdutoTopo(dados).then( () => {
          mapeador.removaCacheProdutos().then(() => {
            commit( () => {
              res.json(Resposta.sucesso({
                ordem: produto.ordem
              }))
            })
          })
        }).catch( (erro) => {
          res.json(Resposta.erro(erro));
        });
      });
    });
  });
});

router.put('/descerParaFinal' , RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  const idProduto: any = req.body.idProduto;
  const ordem: any = req.body.ordem;
  const empresa: Empresa = req.empresa;

  let mapeador = new MapeadorDeProduto(empresa.catalogo)

  mapeador.selecioneSync({id: idProduto}).then( (produto) => {
    if( !produto ) {
      return res.json(Resposta.erro('Produto não encontrado (descerParaFinal): ' + idProduto));
    }

    mapeador.selecioneMaiorOrdemDaCategoria(produto.categoria).then( (ordemFinal: number) => {
      mapeador.transacao( async (conexao: any, commit: any) => {
        produto.ordem = ordemFinal;

        const dados = {
          idCategoria: produto.categoria.id,
          idCatalogo: empresa.catalogo.id,
          ordemProduto: ordem,
          idProduto: produto.id,
          ordemTopo: ordemFinal
        };

        mapeador.atualizeDescerProdutoFinal(dados).then( () => {
          mapeador.removaCacheProdutos().then(() => {
            commit( () => {
              res.json(Resposta.sucesso({
                ordem: produto.ordem
              }))
            })
          });
        }).catch( (erro) => {
          res.json(Resposta.erro(erro));
        });
      });
    });
  });
});

router.delete('/:idCatalogo/:id', RotaGuard.alterarCadastrarProdutos, async(req: any, res) => {
  let idCatalogo = req.params.idCatalogo
  let id = req.params.id;

    let catalogo = await new MapeadorDeCatalogo().selecioneSync({id: idCatalogo})

    if(!id)
      return res.json(Resposta.erro('É necessário informar o id do produto a ser removido'));

    const mapeadorDeProduto = new MapeadorDeProduto(catalogo);
    let produto = {id: id}

    mapeadorDeProduto.removaProduto(produto ).then(() => {
      let registroDeOperacao = new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip())

      registroDeOperacao.removeuProduto(produto).then(() => res.json(Resposta.sucesso()))

    }).catch((reason) => {
      let mensagem = 'Não foi possível remover o produto.'

      if(reason)
        mensagem += ' ' + reason

      return res.json(Resposta.erro(mensagem));
    });
  });

router.get('/vendas/resumo/:idContato', async (req: any, res: any) => {
  let idContato = Number(req.params.idContato),
    inicio = req.query.inicio, fim = req.query.fim;

  if(Number.isNaN(idContato))
    return res.json(Resposta.erro("Não foi informado o id do contato"))

  let horarioInicio = inicio ? moment(inicio, 'DD/MM/YYYY').format('YYYY-MM-DD') : null
  let horarioFim = fim ? moment(fim, 'DD/MM/YYYY').format('YYYY-MM-DD') : null

let query = {id: idContato, horarioInicio: horarioInicio, horarioFim: horarioFim};
let empresa: Empresa = req.empresa
let resumo: DTOResumoProdutosContato[] =
  await new MapeadorDeProduto(empresa.catalogo).obtenhaResumoProdutosContato(query,  req.empresa);

  res.json(Resposta.sucesso(resumo));
})

router.get('/ordens/recalcule',  RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  let empresa: Empresa =  req.empresa;

  if(empresa){
    await new MapeadorDeProduto(empresa.catalogo).recalculeOrdens(empresa.catalogo);

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).reordenouProdutosEmpresa(empresa  );

    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

    res.json(Resposta.sucesso('ordens recalculadas'))
  } else {
    res.json(Resposta.erro('Empresa não encontrada'))
  }

})

router.get('/ordens/recalcule/pornome',  RotaGuard.alterarCadastrarProdutos, async (req: any, res) => {
  let empresa: Empresa =  req.empresa;

  if(empresa){
    await new MapeadorDeProduto(empresa.catalogo).recalculeOrdens(empresa.catalogo, true);

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(),
      Ambiente.Instance.ip()).reordenouProdutosEmpresa(empresa, true);

    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
    res.json(Resposta.sucesso('ordens recalculadas (por nome)'))
  } else {
    res.json(Resposta.erro('Empresa não encontrada'))
  }

})

router.get('/unidades/liste', async (req, res) => {
  let unidades = await new MapeadorDeUnidadeMedida().listeAsync({ produto: true});

  res.json(Resposta.sucesso(unidades))
})

router.get('/unidades/liste', async (req, res) => {
  let unidades = await new MapeadorDeUnidadeMedida().listeAsync({});

  res.json(Resposta.sucesso(unidades))
})



router.get('/historico/:id', async (req, res) => {
  let idProduto = req.params.id
  let filtroTexto =  req.query.texto ? '%' + req.query.texto + '%' : null ;
  let filtroOperacao = req.query.operacao ?  req.query.operacao : null;

  let dados: any = {
    idProduto: idProduto,
    texto: filtroTexto,
    operacao: filtroOperacao

  }

  if(req.query.i) {
    dados.inicio = Number(req.query.i)
    dados.total = Number(req.query.t)
  }


  let mapeador = new MapeadorDeRegistroDeOperacao()
  let historico = await mapeador.listeAsync(dados)
  let quantidade = await mapeador.obtenhaQuantidade(dados)

  res.json(Resposta.sucesso({
    registros: historico,
    quantidade: quantidade
  }))
})

router.post('/sincronizou/precos', async (req: any, res: any) => {
  let empresa = req.empresa;

  if(empresa.integracaoDelivery){
    await empresa.integracaoDelivery.atualizeUltimaSincronizacaoPrecos();
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();
  }

  res.json(Resposta.sucesso(empresa.integracaoDelivery.ultimaSincronizacaoPrecos ))
})

router.post('/sincronizou/estoque', async (req: any, res: any) => {
  let empresa = req.empresa;

  await empresa.integracaoDelivery.atualizeUltimaSincronizacaoEstoque();

  await new MapeadorDeEmpresa().removaDasCaches(empresa);
  await new MapeadorDeProduto(empresa.catalogo).removaCacheProdutos();

  res.json(Resposta.sucesso(empresa.integracaoDelivery.ultimaSincronizacaoEstoque ));
})


router.get('/tags/alimentar',  async (req: any, res: any) => {
  let lista =  await new MepeadorDeTagProduto().listeAsync({ grupo: TagProdutoTipoEnum.Alimentar})

  res.json(Resposta.sucesso(lista))
})



export const ProdutosController: Router = router;
