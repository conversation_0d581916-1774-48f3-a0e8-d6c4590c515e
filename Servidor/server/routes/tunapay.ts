import {Router} from "express";
import {Resposta} from "../utils/Resposta";
import {DTOMerchantTuna} from "../lib/tunapay/DTOMerchantTuna";
import {TunaPayApi} from "../lib/TunaPayApi";
import {EnumStatusAtivacaoMeucardapioPay, MeucardapioPay} from "../domain/integracoes/MeucardapioPay";
import {MapeadorContratoMeucardapioPay} from "../mapeadores/MapeadorContratoMeucardapioPay";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {Ambiente} from "../service/Ambiente";
import {ExecutorAsync} from "../utils/ExecutorAsync";
import {CacheService} from "../service/CacheService";

const router: Router = Router();

enum TunaServiceFluxo {
  TunaSplitparcartoV2 = "63",
  TunaSplitparaPixV4 = "71",
  TunaDummyTeste = "7",
  RegistroEmpresa = "0"
}




//url configurar tuna: https://promokit.meucardapio.ai/tunapay/merchant/hook
router.post('/merchant/hook', async (req: any, res: any) => {
  let dados: any = req.body;
  console.log('notificou tunapay atualizaçao status loja', JSON.stringify(dados))

  let authorization = req.headers['authorization'];
  console.log(authorization)
  let tokenValidao = authorization ?  authorization.replace('Bearer', '').trim() === TunaPayApi.TOKENWEBWOOK : false;

  if(!tokenValidao)
    return  res.status(401).json({mensagem: 'not authorization'})

  if(!dados.externalId ||  dados.serviceId  == null)
      return res.status(400).json({mensagem: 'Nenhum id de merchant informado'});

  let idServico = dados.serviceId.toString();

  //serviço teste, ignorar
  if(idServico !== TunaServiceFluxo.TunaDummyTeste){
    ///pode star vinculado a mais de um empresa
    let empresas: Array<any> = await new MapeadorDeEmpresa().listeAsync({idLojaTuna: dados.externalId})
    let contexto = require('domain').active.contexto;

    for(let i = 0; i < empresas.length; i++){
      //pegar empresa com formas de pagamento
      let empresa = await new MapeadorDeEmpresa().selecioneSync( empresas[i].id);

      contexto.empresa = empresa
      contexto.idEmpresa =   empresa.id;
      console.log('Processar hook tuna para empresa: ' + empresa.dominio);
      if(empresa.meucardapioPay && empresa.meucardapioPay.idLoja ===  dados.externalId){
        if(idServico ===  TunaServiceFluxo.RegistroEmpresa){
          let merchant: any = await new TunaPayApi().obtenhaLoja(empresa.meucardapioPay.idLoja);

          await empresa.meucardapioPay.recebeuRetornoTuna(merchant, empresa);
        } else  {
          //serviço hook
          let aprovado = dados.message && dados.message.code === 'SRVMerchantRegisterOk'
          if(aprovado){
            if(idServico === TunaServiceFluxo.TunaSplitparaPixV4){
              await empresa.meucardapioPay.atualizePixLiberado(empresa);
            } else if(idServico === TunaServiceFluxo.TunaSplitparcartoV2 ){
              await empresa.meucardapioPay.atualizeCartaoLiberado(empresa);
            } else {
              console.warn('Serviço desconhecido: ' + idServico)
            }
          } else {
            console.warn('Serviço não provado', dados.message)
          }
        }
      } else {
         console.error('Empresa nao possui configuraçao válida: ' + empresa.dominio)
      }
    }
  } else {
    console.log('Ignorar notificação do serviço teste dummy: ' + dados.serviceId )
  }
  res.json(Resposta.sucesso('webhook processado com sucesso'))
});

router.post('/empresas/:id/contrato', async (req: any, res: any) => {
  let contrato: any  = req.body;

  let empresa = await new MapeadorDeEmpresa().selecioneSync(req.params.id)

  if(empresa){
    if(!empresa.meucardapioPay){
      let integracao = new MeucardapioPay(empresa, contrato);

      await integracao.salve(true);

      delete integracao.empresa;

      await new MapeadorDeEmpresa().removaDasCaches(empresa);

      res.json(Resposta.sucesso(integracao))
    } else {
      res.json(Resposta.erro('Empresa já tem contrato configurado: ' + req.params.id ))
    }
  } else {
    res.json(Resposta.erro('Empresa nao encontrada: ' + req.params.id ))
  }

})

router.post('/me/merchant/sincronize', async (req: any, res: any) => {
  let empresa: any = req.empresa;

  if(empresa.meucardapioPay) {
    let merchant: any = await new TunaPayApi().obtenhaLoja(empresa.meucardapioPay.idLoja).catch((err) => {
      res.json(Resposta.erro(err))
    });

    if(merchant){
      await empresa.meucardapioPay.recebeuRetornoTuna(merchant, empresa);

      res.json(Resposta.sucesso(empresa.meucardapioPay));
    }
  } else {
    res.json(Resposta.erro('Nenhum contrato configurado'))
  }


})

router.post('/empresa/registre', async (req: any, res: any) => {
  let empresa: any = req.empresa;
  let merchant: any = req.body;
  let banco: any = merchant.bankAccounts[0];

  merchant.pixKeyType = merchant.pixKeyType.value;

  banco.accountType =  banco.accountType.value;
  banco.documentType =  merchant.documentType;
  banco.document =  merchant.document;

  if(empresa.meucardapioPay){
    let resposta: any = await new TunaPayApi().registreLoja(merchant).catch(async (err) => {
      await empresa.meucardapioPay.registreRetornoErro(err);
      res.json(Resposta.erro(err))
    });

    if(resposta){
      await empresa.meucardapioPay.atualizeCriouEmpresa(resposta, true);
      await new MapeadorDeEmpresa().removaDasCaches(empresa);
      res.json(Resposta.sucesso(empresa.meucardapioPay));
    }
  } else {
    res.json(Resposta.erro('Nenhum contrato configurado'))
  }
})

router.get('/me/registre/auto', async (req: any, res: any) => {
  let empresa: any = req.empresa;

  let contratoPadrao: any =
    await new MapeadorContratoMeucardapioPay().selecioneSync({ padrao: true, producao: Ambiente.Instance.producao});

  if(contratoPadrao){
    await registreEmpresaAutomatico(empresa, contratoPadrao);
    res.json(Resposta.sucesso(empresa.meucardapioPay))
  } else {
    res.json(Resposta.erro("Nenhum contrato padrao encontrado"))
  }

})




router.get('/empresas/registre/auto', async (req: any, res: any) => {
  let contratoPadrao: any =
    await new MapeadorContratoMeucardapioPay().selecioneSync({ padrao: true, producao: Ambiente.Instance.producao});

  if(contratoPadrao){
    let empresasAtivas = await new MapeadorDeEmpresa().listeAsync({ tunaCriarAuto: true});

    let tokenSessao  =  `tunaauto:${new Date().getTime()}`;


    let resposta: any =  { processadas: [], erros: [], restante: empresasAtivas.length,
      link: `/tunapay/empresas/registre/auto/status/${tokenSessao}`};

    CacheService.insiraJson(tokenSessao, resposta)

    ExecutorAsync.execute(async (cbAsync: any) => {
      let contexto: any = require('domain').active.contexto;

      for(let i = 0; i < empresasAtivas.length; i++){
        let empresa: any = empresasAtivas[i];

        contexto.empresa = empresa
        contexto.idEmpresa =   empresa.id;
        console.log('registrar auto empresa: ' + empresa.dominio)

        await registreEmpresaAutomatico(empresa, contratoPadrao);

        resposta.processadas.push(empresa.nome)
        resposta.restante--;
        CacheService.insiraJson(tokenSessao, resposta)
      }

      console.log('fim processar registre/auto')
      cbAsync();

    }, 0)

    res.json(resposta);
  } else {
    res.json(Resposta.erro("Nenhum contrato padrao encontrado"))
  }

})

router.get('/empresas/registre/auto/status/:token', async (req: any, res: any) => {
  let resposta =  await  CacheService.getJson(req.params.token)

  res.json(resposta || {})
})

router.get('/me/instalacao/execute', async (req: any, res: any) => {
  let empresa: any = req.empresa, err: any;
  let idLoja =  empresa.meucardapioPay ? empresa.meucardapioPay.idLoja  : empresa.dominio;

  let merchant: any = await new TunaPayApi().obtenhaLoja(idLoja).catch((erroConsultar) => {
    err = erroConsultar;
  });

  if(merchant){
    if(empresa.meucardapioPay){
      if(Number(merchant.condition ) === empresa.meucardapioPay.contratoMeucardapioPay.idExterno){
        await empresa.meucardapioPay.recebeuRetornoTuna(merchant, empresa);

      } else {
        err = 'Nenhum contrato encontrado para a condition: ' + merchant.condition
      }
    }
  } else {
    if(!err) err = 'Nenhuma loja cadastrada no Tuna com esse id: ' + empresa.dominio
  }

  if(!err){
    delete empresa.meucardapioPay.empresa;

    res.json(empresa.meucardapioPay)
  } else {
    res.json(Resposta.erro(err));
  }
})

router.get('/contratos', async (req: any, res: any) => {
  let contratos  = await new MapeadorContratoMeucardapioPay().listeAsync({});

  res.json(Resposta.sucesso(contratos));

})

router.get('/me/merchant', async (req: any, res: any) => {
  let empresa: any = req.empresa, err: any;

  let idLoja =  empresa.meucardapioPay ? empresa.meucardapioPay.idLoja  : empresa.dominio;

  let merchant: any = await new TunaPayApi().obtenhaLoja(idLoja).catch((erroConsultar) => {
    res.json(Resposta.erro(erroConsultar));
  });

  if(merchant) res.json(merchant)
})

router.get('/meucardapiopay/contrato/padrao', async (req: any, res: any) => {
  let contratoPadrao: any =
    await new MapeadorContratoMeucardapioPay().selecioneSync({ padrao: true, producao: Ambiente.Instance.producao});

  res.json(Resposta.sucesso(contratoPadrao))
})


router.get('/meucardapiopay/padrao', async (req: any, res: any) => {
  let empresa: any = req.empresa;
  let meucardapioPay: any;

  let contratoPadrao: any =
    await new MapeadorContratoMeucardapioPay().selecioneSync({ padrao: true, producao: Ambiente.Instance.producao});

  if(contratoPadrao){
    meucardapioPay = new MeucardapioPay(empresa, contratoPadrao )
    await meucardapioPay.salve(true);
    delete meucardapioPay.empresa;
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
    res.json(Resposta.sucesso(meucardapioPay))
  }  else {
    res.json(Resposta.sucesso())
  }
})

async function registreEmpresaAutomatico(empresa: any, contratoPadrao: any) {

  if(!empresa.meucardapioPay){
    empresa.meucardapioPay  = new MeucardapioPay(empresa, contratoPadrao );

    await empresa.meucardapioPay.salve(true);
  }

  if(empresa.meucardapioPay.jaVinculou()) return;

  let condition: any = empresa.meucardapioPay.contratoMeucardapioPay.idExterno;
  let merchant: any = DTOMerchantTuna.crieDaEmresa(empresa, condition);

  let resposta: any = await new TunaPayApi().registreLoja(merchant).catch( async (err) => {
       await empresa.meucardapioPay.registreRetornoErro(err);
  });

  if(resposta)
    await empresa.meucardapioPay.atualizeCriouEmpresa(resposta, false);


  await new MapeadorDeEmpresa().removaDasCaches(empresa);
}

router.get('/me/merchant/dto', async (req: any, res: any) => {
  let resposta: any = {};
  let empresa: any = req.empresa;

  if(!empresa.meucardapioPay){
    let contratoPadrao: any =
      await new MapeadorContratoMeucardapioPay().selecioneSync({ padrao: true, producao: Ambiente.Instance.producao});

    if(contratoPadrao)
      empresa.meucardapioPay = new MeucardapioPay(empresa, contratoPadrao )

  }

  if(empresa.meucardapioPay){
    let condition: any = empresa.meucardapioPay.contratoMeucardapioPay.idExterno;

    let merchant: any = await new TunaPayApi().obtenhaLoja(empresa.meucardapioPay.idLoja);

    if(!merchant){
      resposta.merchant =  DTOMerchantTuna.crieDaEmresa(empresa, condition);
    } else {
      resposta.merchant = DTOMerchantTuna.fromMerchant(merchant);
      resposta.merchant.precadastro  = true;
    }

    resposta.tiposChavesPix = DTOMerchantTuna.tiposChavesPix;
  }

  res.json(Resposta.sucesso(resposta))
})


router.get('/me/sincronize/pagamentos', async (req: any, res: any) => {
  let resposta: any = {};
  let empresa: any = req.empresa;

  if(empresa.meucardapioPay){
    await empresa.meucardapioPay.sincronizeFormasPagamentosAtivas(empresa);
    await new MapeadorDeEmpresa().removaDasCaches(empresa);
  }

  res.json(Resposta.sucesso())

})


router.post('/cartao/nova-sessao/:guid', async (req: any, res: any) => {

  let sessao: any = await new TunaPayApi().crieSessao(req.params.guid ).catch((err) => {
    res.json(Resposta.erro(err))
  });

  if(sessao)
    res.json(Resposta.sucesso(sessao))

})




export const TunaPayController: Router = router;
