import {Router} from 'express';
import {MapeadorDeClassificacaoContato} from '../mapeadores/MapeadorDeClassificacaoContato';
import {Resposta} from "../utils/Resposta";
import {Empresa} from "../domain/Empresa";
import moment = require("moment");

const router = Router();

// Rota para atualizar as classificações dos contatos
router.post('/atualizar/:idEmpresa', async (req: any, res: any) => {
  const idEmpresa = Number(req.params.idEmpresa);
  const parametros = req.body;

  try {
    let empresa: Empresa = req.empresa;

    if (!empresa) {
      return res.json(Resposta.erro('Empresa não encontrada'));
    }

    // Configuração das datas
    const dataInicial = moment().subtract(30, 'days').startOf('day');
    const dataFim = moment().endOf('day');

    parametros.dataInicio = dataInicial.toDate();
    parametros.dataFim = dataFim.toDate();

    const parametrosCompletos = {
      ...parametros,
      qtdeVendasRecorrente: empresa.qtdeVisitasRecorrente,
      qtdeDiasEmRisco: empresa.qtdeDiasEmRisco,
      qtdeDiasPerdido: empresa.qtdeDiasPerdido,
      qtdeVendasVIP: empresa.qtdeComprasVIP,
      ticketMedioVIP: empresa.ticketMedioVIP,
      qtdeDiasPeriodo: empresa.qtdeDiasPeriodo
    };

    const mapeador = new MapeadorDeClassificacaoContato();
    await mapeador.atualizarClassificacoes(idEmpresa, parametrosCompletos);

    res.json(Resposta.sucesso({
      mensagem: 'Classificações atualizadas com sucesso'
    }));
  } catch (erro) {
    res.json(Resposta.erro('Erro ao atualizar classificações: ' + erro.message));
  }
});

// Rota para obter contatos por classificação
router.get('/:idEmpresa/:classificacao', async (req: any, res: any) => {
  const idEmpresa = Number(req.params.idEmpresa);
  const classificacao = req.params.classificacao;
  const skip = Number(req.query.skip) || 0;
  const take = Number(req.query.take) || 10;

  try {
    const mapeador = new MapeadorDeClassificacaoContato();
    const contatos = await mapeador.selecionePorClassificacao(idEmpresa, classificacao, skip, take);
    const total = await mapeador.contarContatosPorClassificacao(idEmpresa, classificacao);

    res.json(Resposta.sucesso({
      contatos: contatos,
      total: total
    }));
  } catch (erro) {
    res.json(Resposta.erro('Erro ao buscar contatos: ' + erro.message));
  }
});

export const ClassificacaoContatoController: Router = router;
