import { MapeadorDeTaxaDeEntregaCalculada } from './../mapeadores/MapeadorDeTaxaDeEntregaCalculada';
import { Resposta } from './../utils/Resposta';
import { Router } from "express";

const moment = require('moment');
const router: Router = Router();

router.get('/liste', async(req, res) => {
  const dados: any = {};
  dados.inicio = Number(req.query.i || 0)
  dados.total = Number(req.query.t || 5)
  dados.apenasEntregas = req.query.apenasEntregas === 'true'
  dados.apenasFora = req.query.apenasFora === 'true'
  dados.busca = req.query.busca || ''
  
  // Processando dataInicial e dataFinal com moment
  if (req.query.dataInicial) {
    // Define início do dia (00:00:00)
    dados.dataInicial = moment(req.query.dataInicial).startOf('day').format('YYYY-MM-DD HH:mm:ss');
  } else {
    dados.dataInicial = null;
  }
  
  if (req.query.dataFinal) {
    // Define fim do dia (23:59:59)
    dados.dataFinal = moment(req.query.dataFinal).endOf('day').format('YYYY-MM-DD HH:mm:ss');
  } else {
    dados.dataFinal = null;
  }
 
  console.log(dados);
  
  const mapeadorDeTaxaDeEntregaCalculada = new MapeadorDeTaxaDeEntregaCalculada();
  const taxas = await mapeadorDeTaxaDeEntregaCalculada.listeAsync(dados);
  const qtdeTaxas = await mapeadorDeTaxaDeEntregaCalculada.selecioneTotal(dados);

  res.json(Resposta.sucesso({
    taxas: taxas,
    total: qtdeTaxas
  }));
});

export const TaxaDeEntregaCalculadaController = router;
