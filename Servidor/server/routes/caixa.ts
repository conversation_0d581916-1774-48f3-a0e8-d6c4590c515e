//router para a classe Caixa

import { Router } from 'express';
import {Resposta} from "../utils/Resposta";
//importa a class RotaGuard
import {RotaGuard} from "../lib/permissao/RotaGuard";
import {CaixaService} from "../service/pdv/CaixaService";
import {Ambiente} from "../service/Ambiente";


const router: Router = Router();

//obtem o caixa do operador logado na empresa
router.get('/', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const online = req.query.online === "true";
  const operador = online ? null : req.user;

  try {
    const caixa = await caixaService.obtenhaCaixaAtual(empresa, operador, online);

    if(!caixa) {
      return res.json(Resposta.sucesso({caixa: null, resumo: null}))
    }

    res.json(Resposta.sucesso(
    {
      caixa: caixa.obtenhaDTO(),
      resumo: caixa.obtenhaResumo()
    }));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
})

router.get('/historico', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const item = req.query.item;
  const tamanho = req.query.tamanho;

  try {
    const historico = await caixaService.obtenhaHistorico(empresa, item, tamanho);

    let resposta = Resposta.sucesso(historico);

    res.json(resposta);
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
})

//obtem historico de sangrias
router.get('/sangrias', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const item = req.query.item;
  const tamanho = req.query.tamanho;

  try {
    const historico = await caixaService.obtenhaHistoricoSangrias(empresa, item, tamanho);

    let resposta = Resposta.sucesso(historico);

    res.json(resposta);
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
})



//abre o caixa se o usuário corrente tiver permissão e não existir um aberto
router.post('/abra', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const saldoInicial = req.body.saldoInicial;
  const operador = req.user;

  let existeCaixaAberto = await caixaService.existeCaixaAberto(empresa, operador);
  //se existir um caixa aberto, responda com erro
  if (existeCaixaAberto) {
    return res.json(Resposta.erro('Já existe um caixa aberto.'));
  }



  try {
    const caixa = await caixaService.abra(empresa, operador, saldoInicial);
    res.json(Resposta.sucesso(
    {
      caixa: caixa.obtenhaDTO(),
      resumo: caixa.obtenhaResumo()
    }));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
});

//valida se o caixa pode ser fechado e retorna ok, advertência ou erro
router.get('/pode-fechar', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const operador = req.user;

  let existeCaixaAberto = await caixaService.existeCaixaAberto(empresa, operador);
  //se não existir um caixa aberto, responda com erro
  if (!existeCaixaAberto) {
    return res.json(Resposta.erro('Não existe um caixa aberto.'));
  }

  //se existem pedidos em aberto, alerta o usuário
  if (caixaService.existePedidoEmAberto(empresa)) {
    return res.json(Resposta.sucesso({
      temErro: false,
      temAdvertencia: true,
      mensagem: 'Existem pedidos em aberto. Deseja continuar?'
    }));
  }

  //tudo certo
  res.json(Resposta.sucesso({
    temErro: false,
    temAdvertencia: false,
    mensagem: 'Caixa pode ser fechado.'
  }));

})

router.post('/adicione-dinheiro', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const operador = req.user;
  const valor = req.body.valor;

  if(!valor) {
    return res.json(Resposta.erro('É necessário informar o valor a ser adicionado.'));
  }

  //se não existir um caixa aberto, responda com erro
  let existeCaixaAberto = await caixaService.existeCaixaAberto(empresa, operador);
  if (!existeCaixaAberto) {
    return res.json(Resposta.erro('Não existe um caixa aberto.'));
  }

  try {
    const caixa = await caixaService.adicioneDinheiro(empresa, operador, valor);
    res.json(Resposta.sucesso(
    {
      caixa: caixa.obtenhaDTO(),
      resumo: caixa.obtenhaResumo()
    }));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
})


router.post('/sangre', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const operador = req.user;
  const valorSangria = req.body.valorSangria;
  const justificativa = req.body.justificativa;

  if(!valorSangria) {
    return res.json(Resposta.erro('É necessário informar o valor da sangria.'));
  }

  if(!justificativa) {
    return res.json(Resposta.erro('É necessário informar a justificativa da sangria.'));
  }

  let existeCaixaAberto = await caixaService.existeCaixaAberto(empresa, operador);

  if (!existeCaixaAberto) {
    return res.json(Resposta.erro('Não existe um caixa aberto.'));
  }

  try {
    const caixa = await caixaService.sangre(empresa, operador, valorSangria, justificativa);
    res.json(Resposta.sucesso(
    {
      caixa: caixa.obtenhaDTO(),
      resumo: caixa.obtenhaResumo()
    }));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
});


router.post('/feche', RotaGuard.operarCaixa, async (req: any, res: any) => {
//fecha o caixa se o usuário corrente tiver permissão e existir um aberto
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const operador = req.user;
  const valorFechamento = req.body.valorFechamento;

  //se não existir um caixa aberto, responda com erro

  const caixa = await caixaService.obtenhaCaixaPorId(req.body.cid)

  if (!caixa) {
    return res.json(Resposta.erro('Não existe um caixa aberto com o id informado.'));
  }


  try {
     await caixaService.fecheCaixa(caixa, valorFechamento);



    res.json(Resposta.sucesso(caixa.obtenhaResumo()));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
});

//obtem o caixa com o id informado, se o usuário tiver permissão
router.get('/:id', RotaGuard.operarCaixa, async (req: any, res: any) => {
  const caixaService = new CaixaService();
  const empresa = req.empresa;
  const id = req.params.id;

  try {
    const caixa = await caixaService.obtenhaCaixa(empresa, id);
    res.json(Resposta.sucesso(
      {caixa: caixa.obtenhaDTO(),
        resumo: caixa.obtenhaResumo()}));
  } catch (error) {
    res.json(Resposta.erro(error.message));
  }
})

export const CaixaController: Router = router;
