import { Resposta } from "../utils/Resposta";
import { MapeadorDeConfiguracoesMia } from "../mapeadores/MapeadorDeConfiguracoesMia";
import { ConfiguracoesMia } from "../domain/chatbot/ConfiguracoesMia";
import { Router } from "express";
import {RegistroDeOperacaoService} from "../service/RegistroDeOperacaoService";
import {Ambiente} from "../service/Ambiente";
import {Empresa} from "../domain/Empresa";
import {ProdutoEmbeddings} from "../domain/ProdutoEmbeddings";
import { TypebotService } from "../service/ia/TypebotService";

const router: Router = Router();

router.post('/', async (req: any, res) => {
  const dados: any = req.body;
  const empresa = req.empresa;

  insiraOuAtualize(empresa, dados).then((resposta) => {
    res.json(resposta);
  }).catch(erro => {
    res.json(erro);
  });
});

router.put('/', async (req: any, res) => {
  const dados: any = req.body;
  const empresa = req.empresa;

  insiraOuAtualize(empresa, dados).then((resposta) => {
    res.json(resposta);
  }).catch(erro => {
    res.json(erro);
  });
});

router.post('/atualizeMudouPlano', async (req: any, res) => {
  const dados: any = req.body;
  const empresa = req.empresa;

  dados.dataInicioTrial = new Date();

  insiraOuAtualize(empresa, dados).then((resposta) => {
    res.json(resposta);
  }).catch(erro => {
    res.json(erro);
  });
});

router.get('/', async (req: any, res) => {
  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();
  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});

  if (!configuracoesMia)
    return res.json(Resposta.erro('configuracoes mia mão encontrado'));

  //console.log('configuracoes mia: ', configuracoesMia);
  res.json(Resposta.sucesso(configuracoesMia));
});

router.get('/verificarTypebotConfigurado', async (req: any, res) => {
  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();
  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});

  if (!configuracoesMia) {
    return res.json(Resposta.erro('Configurações da Mia não encontradas'));
  }

  res.json(Resposta.sucesso({
    configurado: configuracoesMia.typebotConfigurado,
    usarFluxoTypebot: configuracoesMia.usarFluxoTypebot
  }));
});

function insiraOuAtualize(empresa: Empresa, objetoDados: any) {
  console.log('objeto dados: ' + JSON.stringify(objetoDados));

  return new Promise(async (resolve, reject) => {
    console.log('promise');
    const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();

    const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({id: objetoDados.id});
    console.log('depois');

    console.log('configuracoes mia2: ' + configuracoesMia);

    const novaConfiguracao = new ConfiguracoesMia();

    Object.assign(novaConfiguracao, objetoDados);

    if (configuracoesMia) {
      novaConfiguracao.id = configuracoesMia.id;
    }

    await new RegistroDeOperacaoService(Ambiente.Instance.usuarioLogado(), Ambiente.Instance.ip()).ativouOuDesativouChatbotMia(
      empresa, novaConfiguracao.status);

    if (novaConfiguracao.id) {
      mapeadorDeConfiguracoesMia.atualizeSync(novaConfiguracao).then(() => {
        if( novaConfiguracao.responderSobreProdutos ) {
          ProdutoEmbeddings.facaEmbedding(empresa);
        }

        resolve(Resposta.sucesso(novaConfiguracao));
      }).catch((erro: Error) => {
        resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
      });
    } else {
      mapeadorDeConfiguracoesMia.insiraGraph(novaConfiguracao).then((novoId: number) => {
        novaConfiguracao.id = novoId;
        resolve(Resposta.sucesso(novaConfiguracao));
      }).catch((erro: Error) => {
        resolve(Resposta.erro('Falha ao realizar a operação: ' + erro.message));
      });
    }
  });
}

router.post('/crieFluxosTypebotPadrao', async (req: any, res) => {
  const empresa = req.empresa;
  const objetoDados = req.body;

  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();
  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({id: objetoDados.id});

  if( configuracoesMia.idFluxoTypebotWhatsapp && configuracoesMia.idFluxoTypebotInstagram ) {
    return res.json(Resposta.erro('Configurações mia já possui fluxos typebot'));
  }

  const typebotService = new TypebotService();

  typebotService.crieFluxosTypebotPadrao(empresa, configuracoesMia).then(async (resposta: any) => {
    configuracoesMia.idFluxoTypebotWhatsapp = resposta.whatsapp.id;
    configuracoesMia.idFluxoTypebotInstagram = resposta.instagram.id;
    configuracoesMia.publicIdFluxoWhatsapp = resposta.whatsapp.publicId;
    configuracoesMia.publicIdFluxoInstagram = resposta.instagram.publicId;

    mapeadorDeConfiguracoesMia.atualizeSync(configuracoesMia);

    res.json(Resposta.sucesso({
      idFluxoTypebotWhatsapp: resposta.whatsapp.id,
      idFluxoTypebotInstagram: resposta.instagram.id,
      publicIdFluxoWhatsapp: resposta.whatsapp.publicId,
      publicIdFluxoInstagram: resposta.instagram.publicId
    }));
  }).catch((erro: any) => {
    res.json(Resposta.erro(erro));
  });
});

router.post('/publicarFluxo', async (req: any, res) => {
  const idFluxo = req.body.idFluxo;

  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();

  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});

  if (!configuracoesMia) {
    return res.json(Resposta.erro('Configurações da Mia não encontradas'));
  }

  const typebotService = new TypebotService();
  typebotService.publiqueFluxo(configuracoesMia, idFluxo).then((resposta: any) => {
    res.json(Resposta.sucesso(resposta));
  }).catch((erro: any) => {
    res.json(Resposta.erro(erro));
  });
});

router.post('/verificarChaveApi', async (req: any, res) => {
  const dados = req.body;
  const empresa = req.empresa;

  const typebotService = new TypebotService();
  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();

  try {
    const workspaces = await typebotService.obtenhaWorkspaces(dados.chaveApi);

    if (!workspaces || workspaces.length === 0) {
      return res.json(Resposta.erro('Nenhum workspace encontrado para esta chave API'));
    }

    const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});

    if (!configuracoesMia) {
      return res.json(Resposta.erro('Configurações da Mia não encontradas'));
    }

    configuracoesMia.workspaceId = workspaces[0].id;
    configuracoesMia.chaveApiTypebot = dados.chaveApi;
    configuracoesMia.typebotConfigurado = true;
    configuracoesMia.usarFluxoTypebot = true;

    await mapeadorDeConfiguracoesMia.atualizeSync(configuracoesMia);

    res.json(Resposta.sucesso({
      workspaceId: workspaces[0].id,
      typebotConfigurado: true,
      usarFluxoTypebot: true
    }));
  } catch (erro: any) {
    res.json(Resposta.erro('Falha ao verificar chave API: ' + erro.message));
  }
});

router.post('/ativarTrial', async (req: any, res) => {
  const empresa = req.empresa;
  
  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();
  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});
  
  if (!configuracoesMia) {
    return res.json(Resposta.erro('Configurações da Mia não encontradas'));
  }
  
  // Define data de início como agora
  configuracoesMia.dataInicioTrial = new Date();
  
  // Define data de fim como 30 dias a partir de agora
  const dataFim = new Date();
  dataFim.setDate(dataFim.getDate() + 30);
  configuracoesMia.dataFimTrial = dataFim;
  
  // Atualiza as configurações
  try {
    await mapeadorDeConfiguracoesMia.atualizeSync(configuracoesMia);
    res.json(Resposta.sucesso({
      mensagem: 'Trial ativado com sucesso',
      dataInicio: configuracoesMia.dataInicioTrial,
      dataFim: configuracoesMia.dataFimTrial
    }));
  } catch (erro) {
    res.json(Resposta.erro('Falha ao ativar trial: ' + erro.message));
  }
});

router.get('/statusTrial', async (req: any, res) => {
  const mapeadorDeConfiguracoesMia = new MapeadorDeConfiguracoesMia();
  const configuracoesMia: ConfiguracoesMia = await mapeadorDeConfiguracoesMia.selecioneSync({});
  
  if (!configuracoesMia) {
    return res.json(Resposta.erro('Configurações da Mia não encontradas'));
  }
  
  const agora = new Date();
  const trialAtivo = configuracoesMia.dataInicioTrial && 
                     configuracoesMia.dataFimTrial && 
                     agora <= configuracoesMia.dataFimTrial;
  
  const diasRestantes = configuracoesMia.dataFimTrial ? 
    Math.max(0, Math.ceil((configuracoesMia.dataFimTrial.getTime() - agora.getTime()) / (1000 * 60 * 60 * 24))) : 0;
  
  res.json(Resposta.sucesso({
    trialAtivo: trialAtivo,
    dataInicio: configuracoesMia.dataInicioTrial,
    dataFim: configuracoesMia.dataFimTrial,
    diasRestantes: diasRestantes
  }));
});

export const ConfiguracoesMiaController: Router = router;
