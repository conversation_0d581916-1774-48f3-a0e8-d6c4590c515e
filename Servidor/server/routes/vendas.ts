import {Router} from "express";
import {MapeadorDePedido} from "../mapeadores/MapeadorDePedido";

import {Resposta} from "../utils/Resposta";
import {
  DTOResumoVendas,
  DTOResumoVendasAdicionais, DTOResumoVendasCupons,
  DTOResumoVendasGarcon,
  DTOResumoVendasProduto
} from "../lib/dto/DTOResumoVendas";
import {DTOVendaPorForma} from "../domain/DTOVendaPorForma";
// @ts-ignore
import moment = require("moment");
import {DTOVendaAdicional } from "../domain/DTOVendaAdicional";
import {FiltroTelaVendasProdutos} from "../utils/FiltroTelaVendasProdutos";
import {FormatadorUtils} from "../lib/FormatadorUtils";
import {DTOVendaCupom} from "../domain/DTOVendaCupom";
import {DTOVendaOpcaoAdicional} from "../domain/DTOVendaOpcaoAdicional";
import {MapeadorDeProdutoVendido} from "../mapeadores/MapeadorDeProdutoVendido";
import {EnumTipoProdutoVendido} from "../domain/delivery/ProdutoVendido";

const router: Router = Router();


//crie uma rota /vendas/resumo
router.get('/resumo', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  const empresa = req.empresa;
  let rede = usuario.adminRede;

  if( empresa.dadosRede ) {
    rede = empresa.dadosRede.grupo;
  }

  if( query.r )  query.adminRede = rede;

  let filtro: any = new FiltroTelaVendasProdutos(req.user,   empresa, query).toSqlQuery();

  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoVendas(filtro) ;

  let dtoResumo = new DTOResumoVendas(vendasPorForma);
  res.json(Resposta.sucesso(dtoResumo));

})


router.get('/resumoPedidosEComandas', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  const rede = req.empresa?.dadosRede?.grupo;

  if( query.r ) {
    query.adminRede = rede;
  }

  let filtro: any = new FiltroTelaVendasProdutos(req.user, req.empresa, query).toSqlQuery();

  filtro.comMesas = true;
  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoVendas(filtro);

  let dtoResumo = new DTOResumoVendas(vendasPorForma)
  res.json(Resposta.sucesso(dtoResumo))

})

router.get('/resumoComandas', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  let empresa = req.empresa;
  let rede = usuario.adminRede;

  if( empresa.dadosRede ) {
    rede = empresa.dadosRede.grupo;
  }

  if( query.r ) {
    query.adminRede = rede;
  }

  let filtro: any = new FiltroTelaVendasProdutos(req.user, req.empresa, req.query).toSqlQuery();

  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoVendasComandas(filtro);

  let dtoResumo = new DTOResumoVendas(vendasPorForma)
  res.json(Resposta.sucesso(dtoResumo))
})


router.get('/resumoComandas/garcons', async(req: any, res: any) => {

  const query: any = req.query;
  const usuario = req.user;
  const rede = req.empresa?.dadosRede?.grupo;

  if( query.r ) {
    query.adminRede = rede;
  }

  let filtro: any = new FiltroTelaVendasProdutos(req.user,  req.empresa, query).toSqlQuery();

  filtro.agruparGarcons = true;

  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoVendasComandas(filtro);

  res.json(Resposta.sucesso(new DTOResumoVendasGarcon(vendasPorForma)))
})

router.get('/resumo/produtos', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  const rede = req.empresa?.dadosRede?.grupo;

  if( query.r )   query.adminRede = rede;

  let filtro: any = new FiltroTelaVendasProdutos(req.user,  req.empresa, query).toSqlQuery();

  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoProdutos(filtro);

  let totalDescontos = await new MapeadorDePedido().listeTotalDescontos(filtro)

  res.json(Resposta.sucesso(new DTOResumoVendasProduto(vendasPorForma, totalDescontos)))
})

router.get('/resumo/cupons', async(req: any, res: any) => {
  const query = req.query;

  let filtro: any = new FiltroTelaVendasProdutos(req.user,  req.empresa, query).toSqlQuery();

  let vendasPorCupons: Array<DTOVendaCupom> =  await new MapeadorDePedido().listeResumoCupons(filtro);


  res.json(Resposta.sucesso(new DTOResumoVendasCupons(vendasPorCupons)))
})

router.get('/resumo/produtos/adicionais', async(req: any, res: any) => {
  let filtro: any =  new FiltroTelaVendasProdutos(req.user,  req.empresa, req.query).toSqlQuery();
  const empresa =  req.empresa;

  if(req.query.pid){
    filtro.idProduto = Number(req.query.pid)

    let pedidoCorte  = await new MapeadorDePedido().selecioneUltimoPedidoSemProdutoVenda({ idEmpresa: empresa.id});

    if(pedidoCorte) filtro.pedidoMax = pedidoCorte;

    let vendasAdicionais: Array<DTOVendaAdicional> =  await new MapeadorDePedido().listeResumoAdicionaisProdutos(filtro);

    let vendasAdicionaisMultiplos: Array<DTOVendaOpcaoAdicional> =
      await new MapeadorDePedido().listeResumoOpcoesAdicionaisMultiplaEscolha(filtro);

    filtro.tipo  = EnumTipoProdutoVendido.OpcaoAdicional;

    let adicionaisVendidosNovos: Array<DTOVendaOpcaoAdicional> = await new MapeadorDeProdutoVendido().listeResumo(filtro);

    if(adicionaisVendidosNovos.length)
      vendasAdicionaisMultiplos.push(...adicionaisVendidosNovos)

    res.json(Resposta.sucesso(new DTOResumoVendasAdicionais(vendasAdicionais, vendasAdicionaisMultiplos)))
  } else {
    res.json(Resposta.sucesso([]));
  }

})

router.get('/resumo/adicionais', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  const rede = req.empresa?.dadosRede?.grupo;
  const empresa =  req.empresa;

  let filtro: any = new FiltroTelaVendasProdutos(req.user,  empresa, query).toSqlQuery();

  let pedidoCorte  = await new MapeadorDePedido().selecioneUltimoPedidoSemProdutoVenda({ idEmpresa: empresa.id});

  if(pedidoCorte) filtro.pedidoMax = pedidoCorte;

  let vendasAdicionais: Array<DTOVendaAdicional> =  await new MapeadorDePedido().listeResumoAdicionaisProdutos(filtro);

  let vendasAdicionaisMultiplos: Array<DTOVendaOpcaoAdicional> =
    await new MapeadorDePedido().listeResumoOpcoesAdicionaisMultiplaEscolha(filtro);

  filtro.tipo  = EnumTipoProdutoVendido.OpcaoAdicional;

  let adicionaisVendidosNovos: Array<DTOVendaOpcaoAdicional> = await new MapeadorDeProdutoVendido().listeResumo(filtro);

  if(adicionaisVendidosNovos.length)
    vendasAdicionaisMultiplos.push(...adicionaisVendidosNovos)

  res.json(Resposta.sucesso(new DTOResumoVendasAdicionais(vendasAdicionais, vendasAdicionaisMultiplos, false)))

})

router.get('/resumo/relatorio', async(req: any, res: any) => {
  const query = req.query;
  const usuario = req.user;
  const rede = req.empresa?.dadosRede?.grupo;

  if( query.r ) {
    query.adminRede = rede;
  }

  let filtro: any = new FiltroTelaVendasProdutos(req.user,  req.empresa, query).toSqlQuery();

  console.log(filtro);
  let vendasPorForma: Array<DTOVendaPorForma> =  await new MapeadorDePedido().listeResumoVendas(filtro);

  let dtoResumo: any = new DTOResumoVendas(vendasPorForma)
  dtoResumo.empresa = req.empresa
  res.render('relatorio-vendas.ejs', {resumoVendas: dtoResumo, formatador: FormatadorUtils});
})

export const VendasController: Router = router;
