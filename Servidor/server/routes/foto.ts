import {Router} from "express";
import {EmpresaService} from "../service/EmpresaService";
import {Resposta} from "../utils/Resposta";
import {Foto} from "../domain/Foto";
import {MapeadorDeEmpresa} from "../mapeadores/MapeadorDeEmpresa";
import {MapeadorDeFoto} from "../mapeadores/MapeadorDeFoto";
import {Ambiente} from "../service/Ambiente";
import {Empresa} from "../domain/Empresa";
let path = require('path');
let fs = require('fs');
const router: Router = Router();

router.post('/', async (req, res) => {
  const dados: any = req.body;

  let foto = new Foto(null, dados.titulo, dados.descricao, dados.link, dados.empresa);

  new EmpresaService().salveFoto(foto).then( ( erro => {
    if(!erro){
      res.json(Resposta.sucesso({id: foto.id}))
    }else{
      res.json(Resposta.erro(erro))
    }
  }))

});

router.put('/', async (req, res) => {
  const dados: any = req.body;
  let foto = new Foto(dados.id, dados.titulo, dados.descricao, dados.link, dados.empresa);

  new EmpresaService().salveFoto(foto).then( ( erro => {
    if(!erro){
      res.json(Resposta.sucesso( ))
    }else{
      res.json(Resposta.erro(erro))
    }
  }))

});

router.put('/empresa/favicon', async (req, res) => {
  console.log('atualizada capa...')
  const dados: any = req.body;
  console.log(dados)

  const mapeador = new MapeadorDeEmpresa();

  if(!dados.id) return  res.json(Resposta.erro('Empresa inválida'))
  if(!dados.favicon) return  res.json(Resposta.erro('Favicon inválido'))

  let empresa = await mapeador.selecioneSync(dados.id)

  mapeador.desativeMultiCliente();

  empresa.favicon = dados.favicon;

  mapeador.atualizeFavicon(empresa).then( () => {
    res.json(Resposta.sucesso())
  })
});


router.put('/categoria', async (req, res) => {
  console.log('atualizada foto categoria...')
  const dados: any = req.body;
  console.log(dados);


  res.json(Resposta.sucesso())
})

router.put('/empresa/capa', async (req, res) => {
  console.log('atualizada capa...')
  const dados: any = req.body;
  console.log(dados)

  const mapeador = new MapeadorDeEmpresa();

  if(!dados.id) return  res.json(Resposta.erro('Empresa inválida'))
  if(!dados.capa) return  res.json(Resposta.erro('Capa inválida'))

  let empresa = await mapeador.selecioneSync(dados.id)

  mapeador.desativeMultiCliente();

  empresa.capa = dados.capa;

  mapeador.atualizeCapa(empresa).then( () => {
     res.json(Resposta.sucesso())
  })
});

router.put('/empresa/logo', async (req, res) => {
  console.log('atualizada logo...')
  const dados: any = req.body;
  console.log(dados)

  const mapeador = new MapeadorDeEmpresa();

  if(!dados.id) return  res.json(Resposta.erro('Empresa inválida'))
  if(!dados.logo) return  res.json(Resposta.erro('Logo inválida'))

  let empresa = await mapeador.selecioneSync(dados.id)

  mapeador.desativeMultiCliente();

  empresa.logo = dados.logo.replace(Empresa.SUFIXOFULL, '');

  mapeador.atualizeLogo(empresa).then( () => {
    res.json(Resposta.sucesso())
  })
});

router.delete('/:id', async(req, res) => {
  let id = req.params.id;

  if(!id)
    return res.json(Resposta.erro('É necessário informar o id da foto a ser removida'));

  const mapeadorDeFoto = new MapeadorDeFoto();
  let foto = {id: id}

  mapeadorDeFoto.removaFoto(foto ).then(() => {
    return res.json(Resposta.sucesso());
  }).catch((reason: any) => {
    let mensagem = 'Não foi possível remover a foto.'

    if(reason)
      mensagem += ' ' + reason

    return res.json(Resposta.erro(mensagem));
  });
});


router.get('/empresas/capa/gereFull', async (req: any, res: any) => {
  let empresas = await new MapeadorDeEmpresa().listeTodas( );
  let diretorio = Ambiente.Instance.config.caminhoImagens;


  let resposta: any  = { totalProcessar: empresas.length, totalCriados: 0, jaGerados: 0, arquivoNaoExiste: 0};

  console.log('total empresas verificar: ' + empresas.length)
  for(let i = 0 ; i < empresas.length; i++){
    let empresa: any = empresas[i];
    const arquivoCapa = `${diretorio}/empresa/${empresa.logo}`;

    if(fs.existsSync(arquivoCapa)){
      // Quebra o caminho para extrair nome e extensão
      const dir = path.dirname(arquivoCapa);
      const ext = path.extname(arquivoCapa); // ex: .jpg
      const base = path.basename(arquivoCapa, ext); // ex: aaaa

      const arquivoFull = path.join(dir, `${base}_full${ext}`);

      if(!fs.existsSync(arquivoFull)){
        // Copia o arquivo
        fs.copyFileSync(arquivoCapa, arquivoFull);
        resposta.totalCriados++
      } else {
        resposta.jaGerados ++
      }
    } else {
      resposta.arquivoNaoExiste ++;
    }
  }
  res.json(resposta)
})

export const FotosController: Router = router;
