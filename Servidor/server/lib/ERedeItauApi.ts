import axios, {AxiosInstance} from "axios";
import {Ambiente} from "../service/Ambiente";
import {IMeioPagamentoService} from "../service/meiospagamentos/IMeioPagamentoService";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";
import {EnumStatusPagamento, StatusPagamentoDeParaRede} from "./emun/EnumStatusPagamento";
import {PedidoService} from "../service/PedidoService";
import {NotificacaoMeioPagamentoService} from "../service/NotificacaoMeioPagamentoService";
import {Pedido} from "../domain/delivery/Pedido";
import {DTOTransacaoERede} from "./erede/DTOTransacaoERede";
import {EnumStatusPagamentoRede} from "./emun/EnumStatusPagamentoRede";
import {ErrosRedeTokenizacao} from "./ErrosRedeTokenizacao";

import {CacheService} from "../service/CacheService";
import {ErrosRedeTransacao} from "./ErrosRedeTransacao";

const sandbox: any = {
   host: 'https://sandbox-erede.useredecloud.com.br',
   hostToken : 'https://rl7-sandbox-api.useredecloud.com.br', //token-service
   pv: '68173218',
   token: '6ee47b87184147f993be6393ef249087',
   email: '<EMAIL>'
}

export class ERedeItauApi implements IMeioPagamentoService{
  hostApi = 'https://api.userede.com.br/erede';
  hostToken = 'https://api.userede.com.br/redelabs';
  hostSession: string;
  token: string;
  pv: string;
  instance: AxiosInstance;
  instanceToken: AxiosInstance;

  constructor(private integracao: any) {

    this.token = integracao.token.trim();
    this.pv = integracao.publicKey.trim();

    if(this.token === sandbox.token){
       this.token = sandbox.token;
       this.pv = sandbox.pv;
       this.hostApi = sandbox.host;
       this.hostToken = sandbox.hostToken;
    }

    this.instance = axios.create({baseURL:  this.hostApi });
    this.instanceToken = axios.create({baseURL:  this.hostToken });
  }

  obtenhaTokenCartao(tokenizationId: string): Promise<any>{
    return new Promise( async (resolve, reject) => {
        console.log('gerar criptograma token cartão...')
        let tokenizacao: any = await this.obtenhaTokenizacao(tokenizationId).catch((err) => {
            reject(err)
        })

        if(tokenizacao){
          if(tokenizacao.tokenizationStatus === 'Active')
            return resolve(tokenizacao.token)

          if(tokenizacao.tokenizationStatus === 'Pending'){
            return  resolve({ pendente: true})
          }   else {
            let erro = tokenizacao.brand ? tokenizacao.brand.message : null
            if(!erro) erro = 'Token cartão invalido: ' + tokenizacao.tokenizationStatus
            return resolve({  erro : erro})
          }
        }
    })
  }

  gereCriptogramaToken(tokenizationId: string): Promise<string>{
    return new Promise( async (resolve, reject) => {
      this.instanceToken.post( `/token-service/v1/cryptogram/${tokenizationId}`, {
        "subscription": false
      }, this.getHeaders()).then((resposta: any) => {
         console.log(resposta.data)
         resolve(resposta.data.cryptogramInfo.tokenCryptogram)
      }).catch((erro) => {
        reject(this.retornoErro(erro, 'gerar cryptogram'))
      })
    })
  }

  captureTransacao(id: string, valor: number){
    return new Promise( async (resolve, reject) => {
      this.instance.put('/v1/transactions/' + id, { amount: valor}, this.getHeaders()).then((resposta: any) => {
        if(resposta.status === 200){
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'capturar transação'))
        }
      }).catch(async (erro) => {
        reject(this.retornoErro(erro, 'capturar transação'))
      })
    })
  }

  crieTransacao(dto: any){
    return new Promise( async (resolve, reject) => {
      console.log(JSON.stringify(dto))
      this.instance.post('/v1/transactions', dto, this.getHeaders()).then((resposta: any) => {
        if(resposta.status === 200){
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'criar transação'))
        }
      }).catch(async (erro) => {
        if(erro.response && erro.response.data){
          let dadosErro = erro.response.data;
          let erroEsperado: string =  ErrosRedeTransacao.TABELAERROS[dadosErro.returnCode.toString()];
          if(dadosErro.returnCode === '42'){
            let transacao: any = await this.obtenhaTransacaoDoPedido(dto.reference).catch(() => {});
            console.log(transacao);
            // transação que ja existe, como tentar executar nomente a cobrança?
            //retentiva de pagamento como fazer?????
            if(transacao && transacao.authorization)
              return   resolve(transacao);
          }

          if( erroEsperado){
            console.log(dadosErro)
            return reject(`Erro criar transação: "${dadosErro.returnCode}": "${erroEsperado}"`)
          }
        }
        reject(this.retornoErro(erro, 'criar transação'))
      })
    })
  }

  convertaCentavos(valor: number){
    return Number((valor * 100).toFixed(0));
  }

  estornePagamento(id: any, valor: number): Promise<string> {
    return new Promise( (resolve, reject) => {
      let empresa: any = Ambiente.Instance.contexto().empresa;
      let hostLoja: string = empresa.obtenhaHostLoja();

      let dados: any = {
        "amount": this.convertaCentavos(valor),
        "urls": [
          {
            "kind": "callback",
            "url":  `${hostLoja}/pagamentos/notificacao/erede/estorno`
          }
        ]
      }

      //testar local eventos no webhook.site
      if( !Ambiente.Instance.producao)
        dados.urls[0].url = 'https://webhook.site/aec02e15-fc2c-4202-b70b-1b5bfe93532d'

      console.log(dados)
      this.instance.post(`/v1/transactions/${id}/refunds`, dados, this.getHeaders()).then((resposta: any) => {
        if(resposta.data  && resposta.data.refundId){
          console.log(resposta.data)
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'reembolsar transação'))
        }
      }).catch((erro) => {
        reject(this.retornoErro(erro, 'reembolsar transação'))
      })
    })
  }


  obtenhaTransacao(id: string){
    return new Promise( (resolve, reject) => {
      this.instance.get('/v1/transactions/' + id, this.getHeaders()).then((resposta: any) => {
        if(resposta.status === 200 || resposta.status === 202){
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'consultar transação'))
        }
      }).catch((erro) => {
        if(erro.response && erro.response.status === 404) return resolve(null)
        reject(this.retornoErro(erro, 'consultar transação'))
      })
    })
  }

  obtenhaTransacaoDoPedido(reference: string){
    return new Promise( (resolve, reject) => {
      this.instance.get('/v1/transactions?reference=' + reference,   this.getHeaders()).then((resposta: any) => {
        if(resposta.status === 200){
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'consultar transação'))
        }
      }).catch((erro) => {
        if(erro.response && erro.response.status === 404) return resolve(null)
        reject(this.retornoErro(erro, 'consultar transação'))
      })
    })
  }



  obtenhaTokenizacao(id: string){
    return new Promise( (resolve, reject) => {
      this.instanceToken.get('/token-service/v1/tokenization/' + id, this.getHeaders()).then((resposta: any) => {
        if(resposta.status === 200){
          console.log(resposta.data)
          resolve(resposta.data)
        } else {
          reject(this.retornoErro(resposta, 'consultar token'))
        }
      }).catch((erro) => {
        reject(this.retornoErro(erro, 'consultar token'))
      })
    })
  }

  crieTokenCartao(email: string, cardNumber: string, expirationMonth: string, expirationYear: string,
                  cardholderName: string, securityCode: any , storageCard = 0): Promise<string>{

    return new Promise( (resolve, reject) => {
      let dadosCartao: any = {
        "email": email,
        "cardNumber": cardNumber,
        "expirationMonth": expirationMonth,
        "expirationYear": expirationYear,
        "cardholderName": cardholderName,
        "securityCode": securityCode,
        "storageCard": storageCard
      }

      this.instanceToken.post('/token-service/v1/tokenization/', dadosCartao, this.getHeaders()).then((resposta: any) => {
        if(resposta.data && resposta.data.tokenizationId){
          console.log(resposta.data)
          resolve(resposta.data.tokenizationId)
        } else {
          reject('Não foi possivel gerar token cartão')
        }
      }).catch((erro) => {
        if(erro.response && erro.response.data){
          let dadosErro = erro.response.data;
          let erroEsperado: string =  ErrosRedeTokenizacao.TABELAERROS[dadosErro.returnCode];

          if( erroEsperado){
            console.log(dadosErro)
            return reject(`Erro codigo "${dadosErro.returnCode}": "${erroEsperado}"`)
          }
        }
        reject(this.retornoErro(erro, 'gerar token cartão'))
      })
    })
  }

  inicieAutenticacao3ds(){

  }

  getHeaders( ): any{

    let headers: any = {
      'Authorization': 'Basic ' + Buffer.from(String(`${this.pv}:${this.token}`)).toString('base64'),
      'Content-Type': 'application/json',
      'Transaction-Response': 'brand-return-opened'
    }

    console.log(headers)
    return  { headers: headers };

  }

  private retornoErro(erro: any, operacao = ''){
    let prefixoErro = operacao ? ('Falha ao ' + operacao)  : 'Falha executar chamada e.rede';

    if(erro.response && erro.response.data){
      let httpStatus = erro.response.status;
      let dadosErro = erro.response.data;
      console.log(dadosErro)
       if(dadosErro.errors) {
        let msgErro =
          dadosErro.errors.map(
            (item: any) => String(`${item.code || item.field}: ${item.message}`)).join(', ')

        return String(`${prefixoErro}:  ${msgErro}`);
      } else if(dadosErro.returnMessage) {
         return String(`${prefixoErro}: ${dadosErro.returnMessage}`)
      } else if(dadosErro.error_message) {
         let msgErro = String(`HTTP ${httpStatus} - ${dadosErro.error_message}`)
         return String(`${prefixoErro}: ${msgErro}`)
      } else {

        return String(`${prefixoErro}: ${erro.response.data}`)
      }
    } else {
      let respostaErro =  erro.message || erro;
      console.log(respostaErro)
      return String(`${prefixoErro}: ${respostaErro}`)
    }
  }


  async sincronizePagamento(pedido: Pedido, pagamentoOnline: PagamentoPedido, empresa: any ){
    let erro: string,   transacao: any;
    if(pagamentoOnline.codigoTransacao) {
      console.log('sincronizar pagamento erede: ' + pagamentoOnline.codigoTransacao);
      transacao = await this.obtenhaTransacao(pagamentoOnline.codigoTransacao).catch((err) => {
        erro = err
      })
    } else if(pagamentoOnline.codigo) {
      console.log('sincronizar pagamento erede: ' + pagamentoOnline.codigo);
      transacao = await this.obtenhaTransacaoDoPedido(pagamentoOnline.codigo).catch((err) => {
        erro = err
      })
    } else {
      console.log('Pagamento ainda sem codigo de transação: ' + pagamentoOnline.id)
    }


    if(transacao){
      let status = transacao.authorization ? transacao.authorization.status : transacao.status;
      if(status){
        let novoStatus = StatusPagamentoDeParaRede.get(transacao.authorization.status);

        if(novoStatus && novoStatus.toString() !== pagamentoOnline.status.toString()){
          await pagamentoOnline.atualizeRetornoRede(transacao)
          pedido.empresa = empresa;
          await new PedidoService().mudouStatusPagamento(pedido, pagamentoOnline, novoStatus);
          pagamentoOnline.pedido = pedido;
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
        }
      } else {
        console.log(transacao)
      }
    }

    return erro;
  }

  async executeOperacoesTokenizacaoPendente(pedido: Pedido, pagamentoOnline: PagamentoPedido,  empresa: any): Promise<string>{
    return new Promise( async (resolve, reject) => {
      let erro: string, tokenizacaoId: string = pagamentoOnline.tokenizacaoId;
      console.log('Executar sincronização tokenizacaoId: ' + tokenizacaoId);

      let respostaToken: any = await this.obtenhaTokenCartao(tokenizacaoId).catch(async (errObtertoken) => {
        console.error(errObtertoken)
        reject(errObtertoken)
      })

      if(respostaToken){
        if(respostaToken.pendente) return reject('Tokenização pendente ainda')
        if(!pagamentoOnline.aguardandoTokenizar()) return reject('Pagamento já foi processado')

        //a partir daqui registrar erro no pagamento
        try{
          if(respostaToken.erro) throw Error(respostaToken.erro)

          let criptograma: any = await this.gereCriptogramaToken(tokenizacaoId).catch( async (errGerarCriptograma) => {
            console.error(errGerarCriptograma)
            erro = errGerarCriptograma;
          })

          if(erro) throw Error(erro)

          let dadosTokenizacao: any = await CacheService.getJson(`tokid:${tokenizacaoId}`);

          if(!dadosTokenizacao) throw Error('Dados tokenização não estão mais disponiveis')

          const dadosCartao = dadosTokenizacao.dadosCartao,
            enderecoCobranca: any =  dadosCartao.endereco || pedido.endereco;

          enderecoCobranca.telefone = pedido.contato.telefone;

          let config: any = pagamentoOnline.formaDePagamento.configMeioDePagamento;
          let dtocheckout: DTOTransacaoERede  = new DTOTransacaoERede(pedido);

          let descricaoCartao = config ? config.nomeFaturaCartao : null;
          dtocheckout.setPagamentoCartao( dadosCartao, enderecoCobranca, pedido.id.toString(), descricaoCartao);
          dtocheckout.setToken(respostaToken.code, criptograma);

          if(pagamentoOnline.jaTentouPagar())
            dtocheckout.gereCodigoAleatorioPedido();

          let transacao: any = await this.crieTransacao(dtocheckout).catch(  (erroCheckout: any) => {
            erro = erroCheckout
          });

          if(erro) throw Error(erro)

          console.log(transacao);
          await  pagamentoOnline.atualizeRetornoRede(transacao)
          let statusTransacao = transacao.authorization ? transacao.authorization.status :  EnumStatusPagamentoRede.Pendente;
          let novoStatus = StatusPagamentoDeParaRede.get(statusTransacao);
          await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamentoOnline, novoStatus);
          pagamentoOnline.pedido = pedido;
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
          resolve('')
        } catch (err){
          console.error(err)
          await new PedidoService().mudouStatusPagamento(pedido as Pedido, pagamentoOnline,
            EnumStatusPagamento.Negado, null, err.message);
          resolve('')
        }
      }
    })

  }
}
