import {Pedido} from "../../../domain/delivery/Pedido";
import {TabelaLogradouro} from "../../TabelaLogradouro";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import * as moment from "moment";
import {PagamentoPedido} from "../../../domain/delivery/PagamentoPedido";
import {CashbackReserva} from "../../../domain/delivery/CashbackReserva";
import {DTOItemPedidoEcletica} from "./DTOItemPedidoEcletica";
import {EcleticaUtils} from "./EcleticaUtils";


export enum EnumImpressao {
  Novo = 'N',
  Atualizado = 'S',
  Removido = 'R'
}

export class DTODadosEcletica{
  dadosCliente: any =  {};
  itensPedido: any =  [];
  dadosPedido: any = {};
  constructor(){}

  setDadosLoja(rede: any, loja: any, idTransacao: any){
    this.dadosPedido.id_transacao  =  idTransacao;
    this.dadosPedido.rede = rede;
    this.dadosPedido.loja = loja;
  }

  obtenhaDTOItensAdicionalPedido(pedido: Pedido, agrupamento: any,  impressao: EnumImpressao = null){
    let adicionaisPedido: Array<any> = pedido.obtenhaAdicionaisImprimir();

    adicionaisPedido.forEach( (itemAdicional: any) => {
      if (itemAdicional.codigoPdv) {
        let dtoItemAdicional = DTOItemPedidoEcletica.novoDeAdicional(1, itemAdicional, agrupamento.contador, impressao);

        this.itensPedido.push(dtoItemAdicional)
      } else {
        let descricaoItem = String(`Adicional  "${itemAdicional.nome}" do Pedido`);

        throw Error (
          String(`${descricaoItem} não possui código do PDV cadastrado`));
      }
    })
  }

  obtenhaDTOItens(itemPedido: ItemPedido, agrupamento: any, observacoesNoInicio: boolean,
                  impressao: EnumImpressao = null , pedidoMesa: boolean = null) {
    let indicePrimeiroItem = this.itensPedido.length;

    let itens = EcleticaUtils.obtenhaListaItens(itemPedido, agrupamento, observacoesNoInicio, indicePrimeiroItem,
      impressao, pedidoMesa)

    this.itensPedido.push(...itens);
  }

  obtenhaItensRemovidos(){
    return this.itensPedido.filter((item: any) => item.impressao === EnumImpressao.Removido)
  }

  obtenhaItensNovos(){
    return this.itensPedido.filter((item: any) => item.impressao === EnumImpressao.Novo)
  }

  obtenhaEnderecoPadrao(empresa: any){
    let enderecoPadrao: any = {
      logradouro: 'Rua Endereço',
      endereco: "Endereço",
      numero: 1,
      bairro: 'Bairro',
      cep: '99999999'
    }

    if(!empresa.enderecoCompleto)
      throw Error('Endereço Padrão nao pôde ser obtido, informe o endereço completo no cadastro da empresa ')

    enderecoPadrao.cidade  = empresa.enderecoCompleto.cidade

    return enderecoPadrao;
  }

  setDadosPedidoEPagamento(pedido: Pedido, delivery: boolean){
    this.dadosPedido = {
      // Delivery, Encomenda ou FastFila onde o parâmetro tipo_pedido será 'D', 'E' ou 'F' respectivamente.
      tipo_pedido: 'D'
    }

    let totalPagamentos = pedido.obtenhaTotal();

    if(!delivery){
      let horarioRetirada = pedido.horarioEntregaAgendada ? pedido.horarioEntregaAgendada : pedido.horarioAtualizacao;
      this.dadosPedido.tipo_pedido = 'E' // quando tiver data agendamento enviar como ECOMENDA
      this.dadosPedido.data_retirada  = moment(horarioRetirada).format('DD/MM/YYYY');
      this.dadosPedido.hora_retirada =   moment(horarioRetirada).format('HH:mm:ss');
      this.dadosPedido.pagamento =  {
        desconto: Number(pedido.obtenhaTotalDesconto())
      }

      if( pedido.pagamentos ) {
        pedido.pagamentos.forEach((pagamento: any) => {
          //quando for retirada, so enviar pagamento por cashback / online
          if (!pagamento.foiPorCashback()  && !pagamento.foiOnline()) return;

          if(pagamento.foiPorCashback() && pagamento.enviarCashbackComoDesconto(pedido.valor)){
            this.dadosPedido.pagamento.desconto = Number ((this.dadosPedido.pagamento.desconto + pagamento.valor).toFixed(2));
            return;
          }

          if (!pagamento.formaDePagamento.referenciaExterna)
            throw Error(String(`Forma de pagamento "${pagamento.formaDePagamento.nome}" não configurada na loja`))

          if(!this.dadosPedido.pagamento.total){
            this.dadosPedido.pagamento.total = Number(totalPagamentos.toFixed(2));
            this.dadosPedido.pagamento.vlr_dinheiro = 0;
            this.dadosPedido.pagamento.vlr_ticket = 0;
            this.dadosPedido.pagamento.vlr_cheque = 0;
            this.dadosPedido.pagamento.vlr_outras = 0;
            this.dadosPedido.pagamento.troco = 0;
            this.dadosPedido.pagamento.pago_online = true;
            this.dadosPedido.pagamento.taxa_entrega = 0;
          }

          if (pagamento.foiPorCashback()){
            this.dadosPedido.pagamento.vlr_outras += pagamento.valor;
          } else { //online
            this.dadosPedido.pagamento.vlr_cartao =  pedido.obtenhaTotalPagoEm('cartao',  pedido.valor);
            this.dadosPedido.pagamento.vlr_outras +=  pedido.obtenhaTotalPagoEm('outras', pedido.valor);
          }
          this.dadosPedido.pagamento.vlr_outras =  Number(this.dadosPedido.pagamento.vlr_outras.toFixed(2));
          this.adicioneBandeiraPagamento(pagamento, pedido);
        });
      }

    } else {
      let descontoCashback = 0;

      if(!pedido.pagamentos ||  pedido.pagamentos.length === 0)
        throw Error(String(`Nenhuma forma de pagamento  associado ao pedido`))

      pedido.pagamentos.forEach( (pagamento) => {
        if(pagamento.enviarCashbackComoDesconto(pedido.valor))
          descontoCashback = pagamento.valor;
        else if(!pagamento.formaDePagamento.referenciaExterna)
          throw Error(String(`Forma de pagamento "${pagamento.formaDePagamento.nome}" não configurada na loja`))
      })

      if(pedido.horarioEntregaAgendada){
        this.dadosPedido.tipo_pedido = 'A'
        this.dadosPedido.data_agendada  = moment(pedido.horarioEntregaAgendada).format('DD/MM/YYYY');
        this.dadosPedido.hora_agendada =   moment(pedido.horarioEntregaAgendada).format('HH:mm:ss');
      }

      let troco =  pedido.obtenhaValorTroco(),
          totalDesconto =   pedido.desconto,
          taxaEntrega: number = pedido.taxaEntrega;

      if(pedido.descontoTaxaEntrega){
        totalDesconto += pedido.descontoTaxaEntrega;
        taxaEntrega += pedido.descontoTaxaEntrega
      }

      if(pedido.descontoFormaDePagamento){
        totalDesconto += pedido.descontoFormaDePagamento
        //totalPagamentos -= pedido.descontoFormaDePagamento
      }

      if(descontoCashback){ //cashback vai entrar como desconto
        totalDesconto += descontoCashback;
        totalPagamentos -= descontoCashback
      }

      this.dadosPedido.pagamento =  {
        total: Number(totalPagamentos.toFixed(2)),
        vlr_dinheiro: !troco ? pedido.obtenhaTotalPagoEm('dinheiro') : pedido.obtenhaTrocoPara(),
        vlr_ticket: pedido.obtenhaTotalPagoEm('ticket',  pedido.valor),
        vlr_cartao: pedido.obtenhaTotalPagoEm('cartao',  pedido.valor),
        vlr_cheque: pedido.obtenhaTotalPagoEm('cheque',  pedido.valor),
        vlr_outras: pedido.obtenhaTotalPagoEm('outras',  pedido.valor),
        pago_online:  pedido.pagarOnline(),
        desconto: Number( totalDesconto.toFixed(2)),
        troco: troco,
        taxa_entrega: Number(taxaEntrega.toFixed(2))
      }

      this.dadosPedido.pagamento.vlr_dinheiro = Number(  this.dadosPedido.pagamento.vlr_dinheiro.toFixed(2));
      this.dadosPedido.pagamento.troco = Number(  this.dadosPedido.pagamento.troco.toFixed(2));

      pedido.pagamentos.forEach( (pagamento: any) => {
        this.adicioneBandeiraPagamento(pagamento, pedido)
      })
    }

    if(pedido.pagarOnline()){
      this.dadosPedido.integrador_externo = {
        nome_integracao:  "MeuCardapio.ai",
        cnpj:  "08150325000162",
        id_estabelecimento:  pedido.empresa.id,
        id_pedido_externo:  pedido.id
      }
    }

    this.setDadosFidelidade(pedido);

    if(pedido.fazParteMultipedido()){
      if(!this.dadosCliente.obs_cliente) this.dadosCliente.obs_cliente = '';
      //enviar somente marca principal
      let decricaoPedidos = String(`${pedido.multipedido.empresa.nome} Cód: #${pedido.multipedido.codigo}`);

      this.dadosCliente.obs_cliente +=
        String(`Marca: ${pedido.empresa.nome} Cód: #${pedido.codigo} - Multi Marca: ${decricaoPedidos}`);


      this.dadosCliente.obs_cliente.substring(0, 200)
    }

    if(pedido.comerNaLoja()){
      //cliente vai consumir na loja
      this.dadosCliente.obs_cliente  = `${this.dadosCliente.obs_cliente || ''} CLIENTE VAI CONSUMIR NA LOJA`.trim();
      this.dadosCliente.obs_cliente.substring(0, 200)
    }

    if(pedido.observacoes){
      let ultimoItem: any = this.itensPedido[this.itensPedido.length - 1]

      if(ultimoItem)
        ultimoItem.obs  = String(`${ultimoItem.obs} ${ pedido.observacoes}`).trim().substr(0, 300)
    }
  }

  adicioneBandeiraPagamento(pagamento: any, pedido: any){
    if(pagamento.enviarCashbackComoDesconto(pedido.valor)) return;

    let formaDePagamentoEclecita = pagamento.formaDePagamento.referenciaExterna;

    if(formaDePagamentoEclecita){
      let bandeira = pagamento.formaDePagamento.bandeirasCartaoIntegrada[0];

      if(pagamento.codigoTipoPagamento && pagamento.formaDePagamento.bandeirasCartaoIntegrada.length > 1)
        bandeira =  pagamento.formaDePagamento.bandeirasCartaoIntegrada.find( (bandeiraIntegrada: any) =>
          bandeiraIntegrada.codigoPdvOnline.toString() === pagamento.metodoPagamento.toString())

      //   add referencia direita de referenciaExterna ou buscar da empresa a forma_de_pagamento_integrada
      let obrigatorios = ['cartao', 'ticket', 'outras']

      if(!bandeira && obrigatorios.indexOf(formaDePagamentoEclecita) >= 0 )
        throw Error (String(`Nenhum bandeira de cartão configurada para o pagamento em: "${pagamento.formaDePagamento.descricao}"`));

      if(bandeira){
        if(!this.dadosPedido.detalhe_bandeira)
          this.dadosPedido.detalhe_bandeira = []

        let dadosBandeira = {
          codigo: bandeira.codigoPdv ?  bandeira.codigoPdv : bandeira.id,
          valor:  pagamento.valor,
          descricao: bandeira.nome ?  bandeira.nome.substring(0, 15) : '',
          tipo: bandeira.tipo
        }

        console.log( dadosBandeira)

        this.dadosPedido.detalhe_bandeira.push(dadosBandeira)

        this.setComprovantePagamentoOnline(pagamento, dadosBandeira)

      }
    }
  }

  setComprovantePagamentoOnline(pagamento: PagamentoPedido, dadosBandeira: any){
    if(!pagamento.foiOnline() || pagamento.foiPorPix()  ) return;

    if(!this.dadosPedido.comprovante_pagto)  this.dadosPedido.comprovante_pagto = []

    this.dadosPedido.comprovante_pagto.push({
      numero_cartao: pagamento.finalCartao || '9999',
      nome_cartao: 'Nome cartao',
      cod_loja_adquirente: pagamento.codigoAdquirente || '999999999',
      cod_transacao: pagamento.codigoTransacao ? pagamento.codigoTransacao.substring(0, 50)  :  '999999999',
      autorizacao_adquirente: pagamento.codigoAutorizacao ? pagamento.codigoAutorizacao.substring(0, 30) : '999999999',
      nome_bandeira: (pagamento.bandeira || dadosBandeira.descricao).substring(0, 15).toUpperCase(),
      valor_pagamento: dadosBandeira.valor,
      tipo_detalhe: dadosBandeira.tipo,
      codigo_detalhe: dadosBandeira.codigo
    })

  }

  setDadosFidelidade(pedido: Pedido){
    let empresa = pedido.empresa, contato = pedido.contato;

    if(empresa.ehUmaGedai() || empresa.ehUmaCib()){ //
      this.dadosPedido.fidelidade_gcom = {
        id_marca: empresa.ehUmaGedai() ? 168 : 120,
        chave_identificador : 'CELULAR',
        numero_identificador :  contato.telefone,
        cashback : null
      }

      pedido.pagamentos.forEach((pagamento: any) => {
        if(pagamento.foiPagoFidelidadeExterna()){
          let dados: CashbackReserva =  pagamento.cashbackReserva;

          this.dadosPedido.fidelidade_gcom.cashback = {
            id_reserva: dados.idReserva,
            id_cliente: dados.idCliente,
            usou_cashback: true,
            valor_cashback: pagamento.valor,
            pagamento_parcial: pagamento.valor === pedido.valor
          }
        }
      })

    }
  }
  protected distribuaTaxaEntreItens(valorDeTaxa: number) {
    let totalPedido = this.itensPedido.reduce((total: number, item: any) => total + (item.quantidade * item.valor_unitario), 0);
    let taxaDistribuidaCentavos = 0;
    let itensComValor = this.itensPedido.filter((item: any) => item.valor_unitario > 0);
    let valorDeTaxaCentavos = Math.round(valorDeTaxa * 100); // Convertendo a taxa para centavos

    if (this.dadosPedido.pagamento.taxa_entrega) {
      totalPedido += this.dadosPedido.pagamento.taxa_entrega;
      itensComValor.push({
        quantidade: 1,
        valor_unitario: this.dadosPedido.pagamento.taxa_entrega,
        taxaEntrega: true
      });
    }

    let totalDistribuido = 0; // Controla a soma exata

    itensComValor.forEach((item: any, index: number) => {
      let proporcao = (item.quantidade * item.valor_unitario) / totalPedido;
      let valorDistribuido = Math.floor(proporcao * valorDeTaxaCentavos); // Arredondando para baixo
      totalDistribuido += valorDistribuido;

      if (index === itensComValor.length - 1) {
        // Último item recebe ajuste final para bater exato
        let ajusteFinal = valorDeTaxaCentavos - totalDistribuido;
        valorDistribuido += ajusteFinal;
      }

      let valorDistribuidoPorItem = valorDistribuido / item.quantidade;

      // Ajusta o valor unitário para incluir a taxa distribuída
      item.valor_unitario = Math.round((item.valor_unitario * 100) + valorDistribuidoPorItem) / 100;

      if (item.taxaEntrega)
        this.dadosPedido.pagamento.taxa_entrega = item.valor_unitario;

    });

    // 🛠️ Verificação Final - Se houver erro de 0,01, corrige no último item
    let totalRecalculado = itensComValor.reduce((sum: any, item: any) => sum + (item.quantidade * item.valor_unitario), 0);
    let diferenca = Math.round((totalRecalculado - (totalPedido + valorDeTaxa)) * 100) / 100; // Erro possível

    if (diferenca !== 0) {
      // Aplica o ajuste final ao último item
      let ultimoItem = itensComValor[itensComValor.length - 1];
      ultimoItem.valor_unitario = Math.round((ultimoItem.valor_unitario * 100 - diferenca * 100)) / 100;

      if (ultimoItem.taxaEntrega)
        this.dadosPedido.pagamento.taxa_entrega = ultimoItem.valor_unitario;
    }
  }

  distribuaDescontoEntreItens(desconto: any){
    let totalPedido = this.itensPedido.reduce((total: number, item: any) => total + (item.quantidade * item.valor_unitario), 0);
    let itensComValor = this.itensPedido.filter((item: any) => item.valor_unitario > 0);
    let valorDeTaxaCentavos = Math.round(desconto * 100); // Convertendo a taxa para centavos

    let totalDistribuido = 0; // Controla a soma exata

    itensComValor.forEach((item: any, index: number) => {
      let proporcao = (item.quantidade * item.valor_unitario) / totalPedido;
      let valorDistribuido = Math.floor(proporcao * valorDeTaxaCentavos); // Arredondando para baixo
      totalDistribuido += valorDistribuido;

      if (index === itensComValor.length - 1) {
        // Último item recebe ajuste final para bater exato
        let ajusteFinal = valorDeTaxaCentavos - totalDistribuido;
        valorDistribuido += ajusteFinal;
      }

      let valorDistribuidoPorItem = valorDistribuido / item.quantidade;

      // Ajusta o valor unitário para incluir a taxa distribuída
      item.valor_unitario = Math.round((item.valor_unitario * 100) - valorDistribuidoPorItem) / 100;

    });

    // 🛠️ Verificação Final - Se houver erro de 0,01, corrige no último item
    let totalRecalculado = itensComValor.reduce((sum: any, item: any) => sum + (item.quantidade * item.valor_unitario), 0);
    let diferenca = Math.round((totalRecalculado - (totalPedido - desconto)) * 100) / 100; // Erro possível

    if (diferenca !== 0) {
      // Aplica o ajuste final ao último item
      let ultimoItem = itensComValor[itensComValor.length - 1];
      ultimoItem.valor_unitario = Math.round((ultimoItem.valor_unitario * 100 - diferenca * 100)) / 100;

    }
  }
}

export class DTOPedidoEcletica extends DTODadosEcletica {

  constructor(pedido: Pedido, empresa: any) {
    super();
    let enderecoCliente: any = pedido.ehDelivery() ?  pedido.endereco :
                                   ( pedido.endereco && pedido.endereco.cep ? pedido.endereco : null);

    if(!enderecoCliente)
       enderecoCliente = Object.assign({}, this.obtenhaEnderecoPadrao(empresa))

    this.dadosCliente =  new DTODadosClienteEcletica(pedido.contato, enderecoCliente);
    this.itensPedido = [];

    let agrupamento = { contador: 1 };

    let observacoesNoInicio = empresa.integracaoDelivery.enviarObservacoesNoInicio()

    pedido.itens.forEach( (itemPedido: ItemPedido) => {
      this.obtenhaDTOItens(itemPedido, agrupamento, observacoesNoInicio);
    })

    this.obtenhaDTOItensAdicionalPedido(pedido, agrupamento);

    this.setDadosPedidoEPagamento(pedido, pedido.ehDelivery());

    if(pedido.taxaFormaDePagamento > 0)
      this.distribuaTaxaEntreItens(pedido.taxaFormaDePagamento)


  }


}

export class DTODadosClienteEcletica {
  nome: string;
  telefone: string;
  telefone_celular: string;
  email: string;
  logradouro: string;
  endereco: string;
  numero_endereco: number;
  complemento: string;
  bairro: string;
  cidade: string;
  uf: string;
  cep: string;
  latitude: any = null;
  longitude: any  = null;
  tipo_cadastro: string;
  cpf_cnpj: string;
  obs_cliente: string;
  ponto_referencia: string;
  constructor(contato: any, endereco: any) {
    this.nome =  contato.nome.substring(0, 50) ;
    this.telefone = contato.telefone;
    this.telefone_celular = contato.telefone;
    this.email = contato.email ;

    if(!this.email) {
    //  let primeiroNome = contato.nome.split(' ')[0].toLowerCase().normalize("NFD").replace(/[\u0300-\u036f]/g, "");
      this.email = String(`<EMAIL>`)
    }

    let enderecoAbreviado: any = TabelaLogradouro.obtenhaAbreviado( endereco.logradouro);

    this.logradouro =  enderecoAbreviado.abreviacao;

    let enderecoCompleto =  enderecoAbreviado.semAbreviacao ? enderecoAbreviado.semAbreviacao.trim() : '',
        complemento = endereco.complemento ? endereco.complemento : '';

    if(!enderecoCompleto)
      enderecoCompleto = endereco.logradouro

    if(endereco.pontoDeReferencia)
      complemento = complemento.trim() + ' PONTO REF.:' + endereco.pontoDeReferencia;


    this.endereco = enderecoCompleto.substring(0, 65);

    this.numero_endereco =  Number(endereco.numero) >= 0 ? Number(endereco.numero) : 0 ;
    this.bairro =  endereco.bairro ? endereco.bairro.substring(0, 70)  : '';
    this.cep =  endereco.cep;

    if(complemento.length <= 20){
      this.complemento = complemento;
    } else {
      this.complemento =  ''
      this.ponto_referencia = String(`COMPLEMENTO: ${complemento}`).substring(0, 200)
    }

    if(endereco.cidade){
      this.cidade =  endereco.cidade.nome.substring(0, 35) ;
      this.uf =  endereco.cidade.estado.sigla;
    }

    if(endereco.localizacao){
      let cordenadas = endereco.localizacao.split(',')
      if(cordenadas.length > 1){
        this.latitude = Number(cordenadas[0]);
        this.longitude = Number(cordenadas[1]);
      }
    }

    this.tipo_cadastro = contato.qtdePedidos > 1 ?  'C' : 'N'; //C(ja comprou) ou N (novo)

    if(contato.cpf)
      this.cpf_cnpj =  contato.cpf;
  }
}
