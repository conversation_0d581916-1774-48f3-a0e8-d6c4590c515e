import {Comand<PERSON>} from "../../../domain/comandas/Comanda";
import * as moment from "moment";
import {Pedido} from "../../../domain/delivery/Pedido";
import {ItemPedido} from "../../../domain/delivery/ItemPedido";
import {DTOClienteTiny, DTODadosPedidoTiny} from "./DTOPedidoTiny";

export class DTOPedidoComandaTiny extends DTODadosPedidoTiny{
  constructor(comanda: Comanda) {
    super();
    this.data_pedido = moment().format('DD/MM/YYYY')
    this.obs = `Pedido fechamento de Mesa ${comanda.mesa.nome}`;
    this.cliente = new DTOClienteTiny(comanda.contato, null)

    comanda.pedidos.forEach((pedido: Pedido) => {
      if(!pedido.foiCanceladoOuDevolvido()){
        pedido.itens.forEach( (itemPedido: ItemPedido) => {
          this.obtenhaDTOItens(itemPedido);
        })
      }
    })

    if(comanda.pagamentos.length > 1){
      this.forma_pagamento = 'multiplas'
    } else if (comanda.pagamentos[0]) {
      this.forma_pagamento =  comanda.pagamentos[0].formaDePagamento.referenciaExterna
    }

    this.situacao = 'entregue'; //faturado
    this.numero_pedido_ecommerce = comanda.codigo;

    if(comanda.desconto) this.valor_desconto  =  Number( comanda.desconto.toFixed(2));
  }
}
