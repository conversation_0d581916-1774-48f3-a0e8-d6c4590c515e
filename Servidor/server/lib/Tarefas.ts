import {JobTarefasDiarias} from "../service/JobTarefasDiarias";
import {JobEnvioDeCampanhasAgendadas} from "../service/JobEnvioDeCampanhasAgendadas";
import {MonitoradorDeEnvioSMS} from "../service/MonitoradorDeEnvioSMS";
import {EnviadorDeMensagemService} from "../service/EnviadorDeMensagemService";
import { JobClassificacaoContato } from '../service/JobClassificacaoContato';

const globalAny: any = global;

export class Tarefas {
  private jobs: any[];

  constructor() {
    this.jobs = [
      JobEnvioDeCampanhasAgendadas.Instance,
      JobClassificacaoContato.Instance
    ];
  }

  public iniciar(): void {
    this.jobs.forEach(job => job.iniciar());
  }

  public parar(): void {
    this.jobs.forEach(job => job.parar());
  }

  static inicie(){
    console.log('####INICIANDO TAREFAS DO SISTEMA NO MAIN CORE#####')
    const jobMensagens: JobEnvioDeCampanhasAgendadas = JobEnvioDeCampanhasAgendadas.Instance;
    //const jobClassificacao: JobClassificacaoContato = JobClassificacaoContato.Instance;

    let monitoradorDeEnvioSMS: MonitoradorDeEnvioSMS = null;

    monitoradorDeEnvioSMS = MonitoradorDeEnvioSMS.Instance;

    console.log('monitorador');
    console.log(monitoradorDeEnvioSMS);

    globalAny.jobMensagens = jobMensagens;

    jobMensagens.inicialize();
    //jobClassificacao.inicialize();
    JobTarefasDiarias.Instance.inicieTarefas();

    const enviadorMensagem: EnviadorDeMensagemService = EnviadorDeMensagemService.Instancia();
    monitoradorDeEnvioSMS.monitore();
  }
}
