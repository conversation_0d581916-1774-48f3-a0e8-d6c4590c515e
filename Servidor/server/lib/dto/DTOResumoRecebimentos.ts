import * as moment from "moment";

export class DTORecebimento{
  valorMensalidade  = 0;
  valorAdesao = 0;
  valorBruto = 0;
  valorTarifas = 0;
  total = 0;
  descricao: string;
  empresa: any;
  fatura: any ;
  dataCompensacao: Date;
  dataAtivacao: Date;
  referencia: string;
  tipo: string
  constructor(recebimento: any, fatura: any = null) {
    this.valorBruto = recebimento.amount.indexOf('R$') >= 0 ?
      Number( recebimento.amount.replace('R$ ', '').replace('.', '').replace(',', '.')) :
      Number( recebimento.amount.replace('BRL', '').replace(',', ''));

    this.total =   this.valorBruto;


    if( Number.isNaN(this.valorBruto)){
      console.log('recebimento invalido nao precessado:')
      console.log(recebimento)
    }

    if(recebimento.amount.indexOf('BRL') >= 0){
      console.log('ver')
    }
    this.descricao = recebimento.description;
    this.dataCompensacao = new Date(recebimento.entry_date);
    this.tipo = recebimento.transaction_type;

    if(fatura){
      this.dataAtivacao = fatura.contrato.dataAtivacao;
      this.empresa = {
        id: fatura.empresa.id,
        nome: fatura.empresa.nome
      }
      this.fatura = {
        id: fatura.id,
        codigo: fatura.codigo
      }
      this.referencia =  moment(fatura.referencia, 'YYYYMM').format('MMM/YYYY');
      this.valorAdesao = fatura.obtenhaValorPagoAdesaoMensal();
      this.valorMensalidade = Number((this.total - this.valorAdesao).toFixed(2))
    } else {
      this.valorMensalidade = this.total;
      this.empresa = {
        nome: recebimento.customer_name,
        email: recebimento.customer_ref
      }

      this.fatura = {
        codigo: recebimento.reference
      }
    }

    this.empresa.totalPago = recebimento.balance;

  }

  calculeTarifas(recebimento: DTORecebimento){
    this.valorTarifas = recebimento.total;
    this.total  += this.valorTarifas;
  }
}

export class DTOResumoRecebimentos{
  qtde = 0;
  totalMensalidade = 0;
  totalAdesao = 0;
  totalBruto = 0;
  totalTarifas = 0;
  total = 0;
  recebimentos: any = []
  constructor(recebimentos: Array<any>) {
    this.qtde = recebimentos.length;
    recebimentos.forEach(  (recebimento: DTORecebimento) => {
      this.totalAdesao += recebimento.valorAdesao;
      this.totalMensalidade += recebimento.valorMensalidade;
      this.totalBruto += recebimento.valorBruto;
      this.totalTarifas += recebimento.valorTarifas;
      this.total += recebimento.total;
    })
    this.recebimentos = recebimentos;
  }
}

