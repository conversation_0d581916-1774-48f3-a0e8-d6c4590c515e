import {EnumStatusPagamentoPagseguro} from "./emun/EnumStatusPagamentoPagseguro";
import {EnumStatusPagamento} from "./emun/EnumStatusPagamento";

export class PagSeguroUtils {
  static getStatus(status: any){
    if(status === EnumStatusPagamentoPagseguro.Paga || status === EnumStatusPagamentoPagseguro.Disponivel){
      return EnumStatusPagamento.Aprovado
    } else if(EnumStatusPagamentoPagseguro.AguardandoPagamento === status){
      return EnumStatusPagamento.Gerado
    } else if(status === EnumStatusPagamentoPagseguro.EmAnalise ){
      return EnumStatusPagamento.EmAnalise
    } else if(status === EnumStatusPagamentoPagseguro.Cancelada){
      return EnumStatusPagamento.Cancelado
    } else if(status === EnumStatusPagamentoPagseguro.Devolvida || status === EnumStatusPagamentoPagseguro.ChargebackDebitado){
      return EnumStatusPagamento.Reembolsado
    } else if(status === EnumStatusPagamentoPagseguro.EmDisputa || status === EnumStatusPagamentoPagseguro.EmContestacao ){
      return EnumStatusPagamento.Suspenso
    }
  }
}
