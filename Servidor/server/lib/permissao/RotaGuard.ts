import {Resposta} from "../../utils/Resposta";
import {Usuario} from "../../domain/Usuario";
import {Ambiente} from "../../service/Ambiente";
import {EnumOperacaoSistema} from "./EnumOperacaoSistema";
import {MapeadorDePapel} from "../../mapeadores/MapeadorDePapel";


export class RotaGuard{

  static temUmaDasPermissoes(usuario: Usuario, codigosOperacoes: Array<number>){
    let erro, temUmaPermissao = false;

    codigosOperacoes.forEach((operacao) => {
       let erro1 = RotaGuard.temPermissao(usuario, operacao)

       if(!erro1) temUmaPermissao = true;

       if(erro1) erro = erro1;
    })

    if(temUmaPermissao) return null;

    return erro;
  }

  static temPermissao(usuario: Usuario, codigoOperacao: number){
    if(!usuario) return Resposta.erroRequerLogin;

    Ambiente.Instance.determineUsuarioLogado(usuario)

    if(usuario.admin ||  usuario.adminRede) return  null

    let temPapel = usuario.papeis.find((papel: any) => {

           let temOperacao = papel.operacoes.find((operacao: any) => operacao.id === codigoOperacao) != null

           return temOperacao;
    })  != null


    if(temPapel) return null

    console.log(String(`Usuario "${usuario.id} - ${usuario.nome}" nao tem a permissão: ${codigoOperacao}`))

    return Resposta.erroPermissaoUsuario;

  }

  static ehAdmin(req: any, res: any, next: any){
    if(req.user && req.user.admin) return  next();

    res.json( Resposta.erroPermissaoUsuario)
  }

  static  pontuarFidelidade(req: any, res: any, next: any){
    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.PontuarFidelidadeManual );

    if(!erro)
      return  next();

    res.json(erro)
  }

  static  trocarBrindeFidelidade(req: any, res: any, next: any){
    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.TrocarBrindesFidelidadeManual );

    if(!erro)
      return  next();

    res.json(erro)
  }

  static alterarContatos(req: any, res: any, next: any){
    if(req.user && req.user.operador) {
      Ambiente.Instance.determineUsuarioLogado(req.user)
      return  next();
    }


    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.CadastrarContatos);

    if(!erro)
      return  next();

    res.json(erro)
  }

  static alterarCadastrarNotificacoes(req: any, res: any, next: any){

    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.CadastrarNotificacoes );

    if(!erro)
      return  next();

    res.json(erro)

  }

  static gerenciarIntegracoes(req: any, res: any, next: any){
    if(req.user && req.user.operador) {
      Ambiente.Instance.determineUsuarioLogado(req.user)
      return  next();
    }


    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.GerenciarIntegracoes);

    if(!erro)
      return  next();

    res.json(erro)

  }

  static alterarCadastrarProdutos(req: any, res: any, next: any){
    if(req.user && req.user.operador) {
      Ambiente.Instance.determineUsuarioLogado(req.user)
      return  next();
    }



    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.CadastrarProduto);

    if(!erro)
      return  next();

    res.json(erro)

  }

  static alterarPedido(req: any, res: any, next: any){

    if(req.user && req.user.operador) {
      Ambiente.Instance.determineUsuarioLogado(req.user)
      return  next();
    }


    let erro =
      RotaGuard.temUmaDasPermissoes(req.user,
        [EnumOperacaoSistema.CancelarPedido, EnumOperacaoSistema.AterarStatusPedido]);

    if(!erro)
      return  next();

    res.json(erro)
  }


  static editarPedido(req: any, res: any, next: any){
    if(req.user && req.user.operador) {
      Ambiente.Instance.determineUsuarioLogado(req.user)
      return  next();
    }

    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.EditarPedido);

    if(!erro)
      return  next();

    res.json(erro)
  }

  static alterarLoja = (req: any, res: any, next: any) => {
    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.GerenciarLoja);

    if(!erro)
      return  next();

    res.json(erro)
  }

  static operarCaixa(req: any, res: any, next: any) {
    let erro = RotaGuard.temPermissao(req.user, EnumOperacaoSistema.OperarCaixa);

    if(!erro)
      return  next();

    res.json(erro)
  }




  static podeCancelarPedido(usuario: any) {
    if(usuario.operador) return true;
    return  RotaGuard.temPermissao(usuario, EnumOperacaoSistema.CancelarPedido);
  }

}
