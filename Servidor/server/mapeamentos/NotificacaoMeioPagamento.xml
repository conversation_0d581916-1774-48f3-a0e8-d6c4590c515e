<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="notificacaomp">

  <resultMap id="notificacaompResultMap" type="NotificacaoMeioPagamento">
      <id property="id" column="notificacaomp_id"/>

      <result property="meio" column="notificacaomp_meio" />
      <result property="tipo" column="notificacaomp_tipo" />
      <result property="codigo" column="notificacaomp_codigo" />
      <result property="status" column="notificacaomp_status" />
      <result property="referencia" column="notificacaomp_referencia" />
      <result property="horario" column="notificacaomp_horario" />
      <result property="horario_notificado" column="notificacaomp_horario_notificado" />
      <result property="dados" column="notificacaomp_dados" />
      <result property="executada" column="notificacaomp_executada" />
      <result property="erro" column="notificacaomp_erro" />
      <result property="ignorar" column="notificacaomp_ignorar" />

    <discriminator javaType="String" column="notificacaomp_meio" >

      <case value="iugu" resultType="NotificacaoIugu"></case>
      <case value="cielo" resultType="NotificacaoCielo"></case>
      <case value="cielocheckout" resultType="NotificacaoCieloCheckout"></case>
      <case value="pagarme" resultType="NotificacaoPagarme"></case>
      <case value="erede" resultType="NotificacaoERede"></case>
      <case value="pagseguro" resultType="NotificacaoPagSeguro"></case>

    </discriminator>

  </resultMap>

  <select id="selecione" parameterType="map" resultMap="notificacaompResultMap" prefix="true">
      select notificacaomp.*
            from notificacao_meio_pagamento notificacaomp
              where
                    <choose>
                      <when test="id">
                        id = #{id}
                      </when>
                       <when test="naoExecutadas">
                         executada is not true and ignorar is not true and TIMESTAMPDIFF(MINUTE,horario,now()) > 10

                         <if test="status">
                           and status = #{status}
                         </if>
                         order by horario
                       </when>
                    </choose>

  </select>

  <insert id="insira">
      insert into notificacao_meio_pagamento (id,meio,tipo,status,codigo,referencia,horario,horario_notificado,dados,ignorar)
          values (#{id}, #{meio}, #{tipo}, #{status}, #{codigo}, #{referenica}, #{horario}, #{horarioNotificado}, #{dados}, ignorar)
                ON DUPLICATE KEY UPDATE dados = #{dados};
  </insert>

  <update id="atualize">
      update notificacao_meio_pagamento
          set executada = #{executada}, erro = #{erro}, ignorar = #{ignorar}
            where id = #{id}

  </update>
</mapper>
