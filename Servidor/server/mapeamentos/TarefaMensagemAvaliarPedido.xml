<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tarefaMensagemAvalidarPedido">
  <resultMap id="tarefaMensagemAvalidarPedidoRM" type="TarefaMensagemAvaliarPedido">
    <id property="id" column="tarefa_mensagem_avaliar_pedido_id"/>

    <result property="horario" column="tarefa_mensagem_avaliar_pedido_horario"/>

    <association property="pedido" resultMap="pedido.pedidoRM"/>
    <association property="empresa" resultMap="empresa.empresaRM"/>
    <association property="mensagemAvaliarPedido" resultMap="mensagemEnviada.mensagemEnviadaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="tarefaMensagemAvalidarPedidoRM" prefix="true">
    select *
    from
    tarefa_mensagem_avaliar_pedido join pedido on(pedido.id = tarefa_mensagem_avaliar_pedido.pedido_id)
    join contato on contato.id = pedido.contato_id
    join empresa on(empresa.id = tarefa_mensagem_avaliar_pedido.empresa_id)
    left join numero_whatsapp on numero_whatsapp.empresa_id = empresa.id and numero_whatsapp.principal is true
    left join empresa_modulo em on em.empresa_id = empresa.id
    left join modulo on modulo.id = em.modulo_id
    left join mensagem_enviada on(mensagem_enviada.id = tarefa_mensagem_avaliar_pedido.mensagem_avaliar_pedido_id)
    where
    <if test="idEmpresa != null">
      tarefa_mensagem_avaliar_pedido.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <if test="pedido">
      and tarefa_mensagem_avaliar_pedido.pedido_id = #{pedido.id}
    </if>
    <if test="tarefasPendentes">
      and tarefa_mensagem_avaliar_pedido.mensagem_avaliar_pedido_id is null
      and tarefa_mensagem_avaliar_pedido.horario &lt; date_sub(now(), interval 30 MINUTE)
      and tarefa_mensagem_avaliar_pedido.horario > date_sub(now(), interval 1 DAY);
    </if>
  </select>

  <update id="atualize"  parameterType="map">
    update tarefa_mensagem_avaliar_pedido
    set mensagem_avaliar_pedido_id = #{mensagemAvaliarPedido.id}
    where id = #{id}
  </update>
</mapper>
