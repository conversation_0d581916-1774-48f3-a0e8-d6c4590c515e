<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="sistemaIntegrado">
  <resultMap id="sistemaIntegradoRM" type="SistemaIntegrado">
    <id property="id" column="sistema_integrado_id"/>

    <result property="nome" column="sistema_integrado_nome" />
    <result property="site" column="sistema_integrado_site" />
    <result property="tipo" column="sistema_integrado_tipo" />
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="sistemaIntegradoRM" prefix="true">
    select *
    from sistema_integrado
    order by nome
  </select>


</mapper>
