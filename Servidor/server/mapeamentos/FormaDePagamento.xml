<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="formaDePagamento">
  <resultMap id="formaDePagamentoRM" type="FormaDePagamento">
    <id property="id" column="forma_de_pagamento_id"/>

    <result property="nome" column="forma_de_pagamento_nome"/>
    <result property="descricao" column="forma_de_pagamento_descricao"/>

    <result property="exibirCardapio" column="forma_de_pagamento_exibir_cardapio"/>

    <result property="habilitarRetirada" column="forma_de_pagamento_habilitar_retirada"/>

    <result property="habilitarEntrega" column="forma_de_pagamento_habilitar_entrega"/>

    <result property="removida" column="forma_de_pagamento_removida"/>
    <result property="online" column="forma_de_pagamento_online"/>
    <result property="referenciaExterna" column="forma_de_pagamento_referencia_externa"/>
    <result property="chavePix" column="forma_de_pagamento_chave_pix"/>

    <result property="pix" column="forma_de_pagamento_pix"/>
    <result property="voucher" column="forma_de_pagamento_voucher"/>
    <result property="numeroParcelasFixas" column="forma_de_pagamento_numero_parcelas_fixas"/>
    <result property="enviarComoDesconto" column="forma_de_pagamento_enviar_como_desconto"/>
    <result property="notificarConfirmacaoPagamento" column="forma_de_pagamento_notificar_confirmacao_pagamento"/>
    <result property="notificarNovoPedido" column="forma_de_pagamento_notificar_novo_pedido"/>

    <result property="possuiDesconto" column="forma_de_pagamento_possui_desconto" />
    <result property="desconto" column="forma_de_pagamento_desconto" />
    <result property="cobrarTaxaRetorno" column="forma_de_pagamento_cobrar_taxa_retorno" />

    <result property="opendeliveryMethod" column="forma_de_pagamento_opendelivery_method" />
    <result property="opendeliveryBrand" column="forma_de_pagamento_opendelivery_brand" />
    <result property="opendeliveryMethodInfo" column="forma_de_pagamento_opendelivery_method_info" />

    <association property="formaIntegrada"     resultMap="formaIntegrada.formaDePagamentoIntegradaSalvaRM"/>
    <association property="taxaCobranca"     resultMap="taxaCobranca.taxaCobrancaRM"/>
    <association property="configMeioDePagamento" column="config_meio_pagamento_id"
                 resultMap="configMeioDePagamento.configMeioDePagamentoRM"/>
    <association property="formaDePagamentoPdv"     resultMap="formaDePagamentoPdv.formaDePagamentoPdvRM"/>
    <association property="bandeira"     resultMap="bandeira.bandeiraRM"/>

    <collection property="bandeirasCartaoIntegrada"  resultMap="formaDePagamento.bandeiraCartaoIntegradoDaFormaRM"/>

  </resultMap>

  <resultMap id="formaDePagamentoSeguraRM" type="FormaDePagamento">
    <id property="id" column="forma_de_pagamento_id"/>

    <result property="nome" column="forma_de_pagamento_nome"/>
    <result property="descricao" column="forma_de_pagamento_descricao"/>

    <result property="exibirCardapio" column="forma_de_pagamento_exibir_cardapio"/>

    <result property="removida" column="forma_de_pagamento_removida"/>
    <result property="online" column="forma_de_pagamento_online"/>
    <result property="referenciaExterna" column="forma_de_pagamento_referencia_externa"/>
    <result property="chavePix" column="forma_de_pagamento_chave_pix"/>
    <result property="pix" column="forma_de_pagamento_pix"/>
    <result property="voucher" column="forma_de_pagamento_voucher"/>
    <result property="numeroParcelasFixas" column="forma_de_pagamento_numero_parcelas_fixas"/>
    <result property="enviarComoDesconto" column="forma_de_pagamento_enviar_como_desconto"/>

    <result property="notificarConfirmacaoPagamento" column="forma_de_pagamento_notificar_confirmacao_pagamento"/>
    <result property="notificarNovoPedido" column="forma_de_pagamento_notificar_novo_pedido"/>

    <result property="possuiDesconto" column="forma_de_pagamento_possui_desconto" />
    <result property="desconto" column="forma_de_pagamento_desconto" />
    <result property="cobrarTaxaRetorno" column="forma_de_pagamento_cobrar_taxa_retorno" />

    <association property="formaIntegrada"     resultMap="formaIntegrada.formaDePagamentoIntegradaSalvaRM"/>
    <association property="configMeioDePagamento"
                 resultMap="configMeioDePagamento.configMeioDePagamentoSeguraRM"/>

    <collection property="bandeirasCartaoIntegrada"
                resultMap="formaDePagamento.bandeiraCartaoIntegradoDaFormaRM"/>

    <association property="taxaCobranca"     resultMap="taxaCobranca.taxaCobrancaRM"/>

  </resultMap>

  <resultMap id="formaDePagamentoResumoRM" type="FormaDePagamento">
    <id property="id" column="forma_de_pagamento_id"/>

    <result property="nome" column="forma_de_pagamento_nome"/>
    <result property="descricao" column="forma_de_pagamento_descricao"/>

  </resultMap>
  <resultMap id="bandeiraCartaoIntegradoRM" type="BandeiraCartaoIntegrado">
    <id property="id" column="bandeira_cartao_integrada_id"/>

    <result property="nome" column="bandeira_cartao_integrada_nome"/>
    <result property="tipo" column="bandeira_cartao_integrada_tipo"/>
    <result property="codigoPdv" column="bandeira_cartao_integrada_codigo_pdv"/>
    <result property="codigoPdvOnline" column="bandeira_cartao_integrada_codigo_pdv_online"/>
    <result property="descricaoPdvOnline" column="bandeira_cartao_integrada_descricao_pdv_online"/>

    <association property="formaDePagamento"
                 resultMap="formaDePagamento.formaDePagamentoRM"/>
  </resultMap>

  <resultMap id="bandeiraCartaoIntegradoDaFormaRM" type="BandeiraCartaoIntegrado">
    <id property="id" column="bandeira_cartao_integrada_id"/>

    <result property="nome" column="bandeira_cartao_integrada_nome"/>
    <result property="tipo" column="bandeira_cartao_integrada_tipo"/>
    <result property="codigoPdv" column="bandeira_cartao_integrada_codigo_pdv"/>
    <result property="codigoPdvOnline" column="bandeira_cartao_integrada_codigo_pdv_online"/>
    <result property="descricaoPdvOnline" column="bandeira_cartao_integrada_descricao_pdv_online"/>

    <association property="configMeioDePagamento"
                 resultMap="configMeioDePagamento.configMeioDePagamentoSeguraRM"/>

  </resultMap>

  <select id="selecioneBandeiraIntegrada" parameterType="map" resultMap="bandeiraCartaoIntegradoRM" prefix="true">
     select bandeira_cartao_integrada.* , forma_de_pagamento.*
        from  bandeira_cartao_integrada_nova bandeira_cartao_integrada join forma_de_pagamento  on forma_de_pagamento_id = forma_de_pagamento.id
      where bandeira_cartao_integrada.empresa_id = #{idEmpresa} and codigo_pdv = #{codigoPdv} and tipo = #{tipo} and forma_de_pagamento.removida is not true

  </select>

  <select id="selecione" parameterType="map" resultMap="formaDePagamentoRM" prefix="true">
    select *  from
      forma_de_pagamento  left join forma_de_pagamento_integrada_nova  forma_de_pagamento_integrada_salva on forma_de_pagamento_integrada_id = forma_de_pagamento_integrada_salva.id
                          left join config_meio_pagamento on (config_meio_pagamento.id = forma_de_pagamento.config_meio_de_pagamento_id)
                          left join bandeira_cartao_integrada_nova as bandeira_cartao_integrada on bandeira_cartao_integrada.forma_de_pagamento_id = forma_de_pagamento.id
                          left join taxa_cobranca on taxa_cobranca.id = taxa_cobranca_id
                          left join forma_de_pagamento_pdv on forma_de_pagamento_pdv.id = forma_de_pagamento_pdv_id
                          left join forma_de_pagamento_pdv_forma_integrada forma_pdv_integrada
                            on forma_pdv_integrada.forma_de_pagamento_pdv_id = forma_de_pagamento_pdv.id
                          left join forma_de_pagamento_integrada_nova forma_de_pagamento_pdv_integrada
                           on forma_de_pagamento_pdv_integrada.id = forma_pdv_integrada.forma_de_pagamento_integrada_id
                          left join bandeira on bandeira_id = bandeira.id
        where forma_de_pagamento.empresa_id = #{idEmpresa}
      <if test="id">
        and forma_de_pagamento.id = #{id}
      </if>
      <if test="nome">
        and forma_de_pagamento.nome = #{nome}
      </if>
      <if test="descricao">
        and forma_de_pagamento.descricao = #{descricao}
      </if>
      <if test="naEntrega != null">
        and online is false  and exibir_cardapio is true
      </if>
      <if test="online != null">
        and online is true
      </if>
      <choose>
        <when test="todas != null"> </when>
        <otherwise>
          and removida is not true;
        </otherwise>
      </choose>
  </select>

  <insert id="insira"  parameterType="map">
    insert into forma_de_pagamento(exibir_cardapio,descricao,online,forma_de_pagamento_integrada_id, referencia_externa,nome,empresa_id,taxa_cobranca_id, bandeira_cartao_integrada_nova_id,
    config_meio_de_pagamento_id,   enviar_como_desconto, pix, voucher, numero_parcelas_fixas, removida,notificar_novo_pedido,notificar_confirmacao_pagamento,
    possui_desconto, desconto, habilitar_retirada, habilitar_entrega,cobrar_taxa_retorno, chave_pix, forma_de_pagamento_pdv_id, bandeira_id)
        values(#{exibirCardapio}, #{descricao}, #{online}, #{formaIntegrada.id}, #{formaIntegrada.descricao}, #{nome},#{empresa.id},#{taxaCobranca.id},#{bandeiraCartaoIntegrada.id},
    #{configMeioDePagamento.id},#{enviarComoDesconto}, #{pix}, #{voucher}, #{numeroParcelasFixas}, false, #{notificarNovoPedido}, #{notificarConfirmacaoPagamento},
    #{possuiDesconto}, #{desconto}, #{habilitarRetirada}, #{habilitarEntrega}, #{cobrarTaxaRetorno}, #{chavePix}, #{formaDePagamentoPdv.id}, #{bandeira.id})
  </insert>


  <update id="atualize"  parameterType="map">
    update forma_de_pagamento
      set nome = #{nome}, descricao = #{descricao}, exibir_cardapio = #{exibirCardapio},
    removida = #{removida}, habilitar_retirada = #{habilitarRetirada}, habilitar_entrega = #{habilitarEntrega},
          forma_de_pagamento_integrada_id =  #{formaIntegrada.id},
          referencia_externa = #{formaIntegrada.descricao}, chave_pix = #{chavePix},
          taxa_cobranca_id = #{taxaCobranca.id}, cobrar_taxa_retorno =  #{cobrarTaxaRetorno},
          pix = #{pix}, voucher = #{voucher}, enviar_como_desconto = #{enviarComoDesconto},
          numero_parcelas_fixas = #{numeroParcelasFixas},
          notificar_novo_pedido = #{notificarNovoPedido}, notificar_confirmacao_pagamento =  #{notificarConfirmacaoPagamento},
          possui_desconto = #{possuiDesconto}, desconto = #{desconto},
          opendelivery_method = #{opendeliveryMethod}, opendelivery_brand = #{opendeliveryBrand}, opendelivery_method_info = #{opendeliveryMethodInfo}
         where id = #{id} and forma_de_pagamento.empresa_id = #{empresa.id};
  </update>

  <update id="atualizeConfigMeioDePagamento"  parameterType="map">
    update config_meio_pagamento
      set client_id = #{clientID},
      token = #{token},
      meio_de_pagamento = #{meioDePagamento},
      client_secret = #{clientSecret}
    where id = #{id};
  </update>

  <update id="marqueRemovida"  parameterType="map">
    update forma_de_pagamento
    set removida = true, bandeira_cartao_integrada_nova_id = null, bandeira_cartao_integrada_id =null
     where id = #{id}  and empresa_id = #{empresa.id};

    delete from  bandeira_cartao_integrada_nova where forma_de_pagamento_id =  #{id}  and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeAtiva"  parameterType="map">
    update forma_de_pagamento set exibir_cardapio = #{exibirCardapio}
        where id = #{id}  and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeChavePix"  parameterType="map">
    update forma_de_pagamento set chave_pix = #{chavePix}
        where id = #{id}  and empresa_id = #{empresa.id};
  </update>

  <update id="atualizePdvBandeira"  parameterType="map">
    update forma_de_pagamento
      set   forma_de_pagamento_pdv_id = #{formaDePagamentoPdv.id}, bandeira_id = #{bandeira.id},
    removida = #{removida},  exibir_cardapio = #{exibirCardapio}
    where id = #{id}  and empresa_id = #{empresa.id};
  </update>

  <insert id="insiraBandeira"  parameterType="map">
     insert into bandeira_cartao_integrada_nova(nome, codigo_pdv, tipo,empresa_id,forma_de_pagamento_id, codigo_pdv_online,descricao_pdv_online)
        values (#{nome}, #{codigoPdv}, #{tipo},#{empresa.id}, #{formaDePagamento.id}, #{codigoPdvOnline}, #{descricaoPdvOnline})   ;
  </insert>

  <update id="atualizeBandeira"  parameterType="map">
    update bandeira_cartao_integrada_nova
        set nome = #{nome}, codigo_pdv = #{codigoPdv}, tipo = #{tipo},
    codigo_pdv_online = #{codigoPdvOnline}, descricao_pdv_online = #{descricaoPdvOnline}
        where id = #{id} and empresa_id = #{empresa.id};
  </update>

  <update id="removaBandeira"  parameterType="map">
    update forma_de_pagamento
    set  bandeira_cartao_integrada_id =null, bandeira_cartao_integrada_nova_id =null
        where bandeira_cartao_integrada_nova_id = #{id}  and empresa_id = #{empresa.id};

    delete from bandeira_cartao_integrada_nova
        where id = #{id} and empresa_id = #{empresa.id};
  </update>

</mapper>
