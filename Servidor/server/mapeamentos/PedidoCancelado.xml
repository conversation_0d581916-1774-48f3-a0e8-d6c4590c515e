<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="pedidoCancelado">

  <resultMap id="PedidoCanceladoResultMap" type="PedidoCancelado">
    <id property="id" column="id" />
    <result property="pedidoId" column="pedido_id" />
    <result property="motivo" column="motivo" />
    <result property="codigo" column="codigo" />
    <result property="modo" column="modo" />
    <result property="operadorId" column="operador_id" />
  </resultMap>

  <insert id="inserirPedidoCancelado" parameterType="com.example.model.PedidoCancelado">
    INSERT INTO pedido_cancelado(pedido_id, motivo, codigo, modo, operador_id)
    VALUES (#{pedidoId}, #{motivo}, #{codigo}, #{modo}, #{operadorId})
  </insert>

  <update id="atualizarPedidoCancelado" parameterType="com.example.model.PedidoCancelado">
    UPDATE pedido_cancelado
    SET pedido_id = #{pedidoId},
    motivo = #{motivo},
    codigo = #{codigo},
    modo = #{modo},
    operador_id = #{operadorId}
    WHERE id = #{id}
  </update>

  <delete id="excluirPedidoCancelado" parameterType="long">
    DELETE FROM pedido_cancelado
    WHERE id = #{id}
  </delete>

  <select id="buscarPedidoCanceladoPorId" resultMap="PedidoCanceladoResultMap" parameterType="long">
    SELECT id, pedido_id, motivo, codigo, modo, operador_id
    FROM pedido_cancelado
    WHERE id = #{id}
  </select>

  <select id="buscarTodosPedidosCancelados" resultMap="PedidoCanceladoResultMap">
    SELECT id, pedido_id, motivo, codigo, modo, operador_id
    FROM pedido_cancelado
  </select>

</mapper>
