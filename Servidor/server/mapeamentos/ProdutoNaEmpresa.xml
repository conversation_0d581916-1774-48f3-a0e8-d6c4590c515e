<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="produtoNaEmpresa">

  <resultMap id="produtoNaEmpresaRM" type="ProdutoNaEmpresa">
    <id property="id" column="produto_na_empresa_id"/>

    <result property="preco" column="produto_na_empresa_preco"/>
    <result property="disponibilidade" column="produto_na_empresa_disponibilidade"/>
    <result property="temEstoque" column="produto_na_empresa_tem_estoque"/>
    <result property="exibirPrecoNoCardapio" column="produto_na_empresa_exibir_preco_no_cardapio"/>
    <result property="disponivelNaMesa" column="produto_na_empresa_disponivel_na_mesa"/>
    <result property="disponivelParaDelivery" column="produto_na_empresa_disponivel"/>
    <result property="novoPreco" column="produto_na_empresa_novo_preco"/>
    <result property="destaque" column="produto_na_empresa_destaque" />
    <result property="mensagemPedido" column="produto_na_empresa_mensagem_pedido" />

    <association property="produto"   resultMap="produto.produtoResultMap"/>
    <association property="empresa"   resultMap="empresa.empresaRM"/>
  </resultMap>

  <resultMap id="produtoNaEmpresaDoPrudutoRM" type="ProdutoNaEmpresa">
    <id property="id" column="produto_na_empresa_id"/>

    <result property="preco" column="produto_na_empresa_preco"/>
    <result property="disponibilidade" column="produto_na_empresa_disponibilidade"/>
    <result property="temEstoque" column="produto_na_empresa_tem_estoque"/>
    <result property="exibirPrecoNoCardapio" column="produto_na_empresa_exibir_preco_no_cardapio"/>
    <result property="disponivelNaMesa" column="produto_na_empresa_disponivel_na_mesa"/>
    <result property="disponivelParaDelivery" column="produto_na_empresa_disponivel"/>
    <result property="novoPreco" column="produto_na_empresa_novo_preco"/>
    <result property="destaque" column="produto_na_empresa_destaque" />
    <result property="mensagemPedido" column="produto_na_empresa_mensagem_pedido" />

  </resultMap>

  <insert id="insira" parameterType="map" useGeneratedKeys="true" >

    insert into produto_na_empresa(produto_id, empresa_id, preco, novo_preco, destaque)
    values (#{produto.id}, #{empresa.id}, #{preco}, #{novoPreco}, #{destaque})
    ON DUPLICATE KEY     UPDATE preco = #{preco}, novo_preco = #{novoPreco}, destaque = #{destaque};
  </insert>

  <insert id="insiraDisponibilidade" parameterType="map" useGeneratedKeys="true" >
    insert into produto_na_empresa(produto_id, empresa_id, preco, disponibilidade)
    values (#{produto.id}, #{empresa.id}, #{preco}, #{disponibilidade})
  </insert>
  <insert id="insiraDisponibilidades" parameterType="map" useGeneratedKeys="true" >
    insert into produto_na_empresa(produto_id, empresa_id, preco, disponibilidade)
    values
    <foreach item="dado" collection="dados" separator="," >
    (#{dado.produto.id}, #{dado.empresa.id}, #{dado.preco}, #{dado.disponibilidade})
    </foreach>
  </insert>

  <update id="atualizeDisponibilidade" parameterType="map">
    update produto_na_empresa
    set disponibilidade = #{disponibilidade}
    where id = #{id}
        and empresa_id = #{empresa.id};
  </update>

  <update id="atualizeDisponibilidades" parameterType="map">
    update produto_na_empresa
    set
    disponibilidade = #{disponibilidade}
    where  empresa_id = #{empresa.id} and id in
    <foreach item="id" collection="ids" open="( " separator="," close=")">
      #{id}
    </foreach>
  </update>

  <update id="atualizePreco" parameterType="map" >
    insert into produto_na_empresa(produto_id, empresa_id, preco)
    values (#{produto.id}, #{empresa.id}, #{preco})
    ON DUPLICATE KEY     UPDATE preco = #{preco};
  </update>

</mapper>
