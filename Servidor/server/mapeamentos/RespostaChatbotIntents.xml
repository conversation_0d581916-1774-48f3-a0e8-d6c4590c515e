<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="respostaChatbotIntents">
  <resultMap id="RespostaChatbotIntentsResultMap" type="com.example.RespostaChatbotIntents">
    <id column="id" property="id"/>
    <result column="intent" property="intent"/>
    <result column="resposta" property="resposta"/>
    <result column="empresa_id" property="empresaId"/>
  </resultMap>

  <insert id="insira" parameterType="com.example.RespostaChatbotIntents">
    INSERT INTO resposta_chatbot_intents (intent, resposta, empresa_id)
    VALUES (#{intent}, #{resposta}, #{empresaId})
  </insert>

  <update id="atualize" parameterType="com.example.RespostaChatbotIntents">
    UPDATE resposta_chatbot_intents
    SET intent = #{intent}, resposta = #{resposta}, empresa_id = #{empresaId}
    WHERE id = #{id}
  </update>

  <delete id="remova" parameterType="long">
    DELETE FROM resposta_chatbot_intents
    WHERE id = #{id}
  </delete>

  <select id="selecione" resultMap="RespostaChatbotIntentsResultMap" parameterType="map" prefix="true">
    SELECT * FROM resposta_chatbot_intents
    WHERE empresa_id = #{empresaId}
  </select>

  <select id="selecioneTotal" parameterType="map" resultType="int" prefix="true">
    SELECT COUNT(*) FROM resposta_chatbot_intents WHERE
    empresa_id = #{empresaId}
    and data_criacao > #{dataCriacao}
  </select>
</mapper>
