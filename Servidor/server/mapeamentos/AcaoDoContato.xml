<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="acaoDoContato">
  <resultMap id="acaoDoContatoResultMap" type="AcaoDoContato">
    <id property="id" column="acao_contato_id"/>

    <result property="mensagem" column="acao_contato_mensagem"/>
    <result property="horario" column="acao_contato_horario"/>
    <result property="tipoDeAcao" column="acao_contato_tipo_de_acao"/>
    <result property="pontos" column="acao_contato_pontos"/>

    <association property="pontuacaoRegistrada" resultMap="pontuacaoRegistrada.pontuacaoRegistradaRM"/>
    <association property="brindeResgatado" resultMap="brindeResgatado.brindeResgatadoRM"/>
    <association property="pedido" resultMap="pedido.pedidoRM"/>

    <association property="contato" resultMap="contato.contatoRM"/>
    <association property="cartao" resultMap="cartao.cartaoDoContatoRM"/>
  </resultMap>

  <resultMap id="acaoDoContatoDTO" type="AcaoDoContato">
    <id property="id" column="acao_contato_id"/>

    <result property="mensagem" column="acao_contato_mensagem"/>
    <result property="horario" column="acao_contato_horario"/>
    <result property="tipoDeAcao" column="acao_contato_tipo_de_acao"/>
    <result property="pontos" column="acao_contato_pontos"/>
  </resultMap>

  <select id="existe" parameterType="map" resultType="int">
    select count(*) total from CARTAO C where   C.ID_CONTATO = #{idContato}
    and empresa_id = #{idEmpresa}
    and c.removido is not true
  </select>

  <select id="selecione" parameterType="map" resultMap="acaoDoContatoResultMap" prefix="true">
    select
      acao_contato.*,
      contato.*,
      cartao.*, plano.*, brinde_resgatado.*,
      pontuacao.*, tipo_de_pontuacao.*
    from acao_contato join contato  on acao_contato.contato_id = contato.id
                      left join pontuacao_registrada pontuacao on pontuacao.id = acao_contato.pontuacao_registrada_id
                      left join brinde_resgatado on brinde_resgatado.id = acao_contato.brinde_resgatado_id
                      left join cartao on cartao.id = acao_contato.cartao_id
                      left join plano on plano.id = cartao.plano_id
                      left join  tipo_de_pontuacao on id_tipo_de_pontuacao = tipo_de_pontuacao.id
                      left join pedido on pedido.id = acao_contato.pedido_id
          where
            contato.empresa_id = #{idEmpresa}  and acao_contato.removida is not true
            <choose>
              <when test="id != null">
                and acao_contato.id = #{id}
              </when>
              <when test="idCartao != null">
                and acao_contato.cartao_id = #{idCartao}
              </when>

              <when test="idBrindeResgatado != null">
                and acao_contato.brinde_resgatado_id  = #{idBrindeResgatado}
              </when>

              <when test="idPontuacao != null">
                and acao_contato.pontuacao_registrada_id   = #{idPontuacao}
              </when>

              <when test="idContato != null">
                and contato.id = #{idContato}
              </when>

              <when test="reprocessarPontos != null">
                and  tipo_de_acao  in (2,3) and plano_id = 4  order by acao_contato.horario
              </when>

            </choose>
            <if test="soPontuado">
                and acao_contato.pontos > 0
            </if>

            <if test="ultimasAcoes">
              order by acao_contato.horario desc
            </if>
            <if test="inicio != null">
              limit #{inicio}, #{quantidade}
            </if>
  </select>

  <update id="remova">
    update acao_contato set removida = true
          where id = #{id} and empresa_id = #{empresa.id}
  </update>


  <insert id="insira" parameterType="AcaoDoContato">
    insert into acao_contato(contato_id, horario, mensagem, tipo_de_acao, empresa_id, cartao_id, pontos, pontuacao_registrada_id, brinde_resgatado_id, pedido_id, removida) values
        (#{contato.id},#{horario}, #{mensagem}, #{tipoDeAcao}, #{empresa.id}, #{cartao.id},  #{pontos}, #{pontuacaoRegistrada.id}, #{brindeResgatado.id}, #{pedido.id} , false)
  </insert>

  <insert id="inserirSaldoKimokes">
    insert into acao_contato(contato_id,horario,mensagem,tipo_de_acao,empresa_id,pontos,cartao_id)
      values (23840, now(),'Ganhou R$ 100,00 em creditos referente mensalidade 03/2020', 2, 62,100,23755 );

    update cartao set pontos = pontos + 100  where contato_id = 23840;

  </insert>

  <update id="removaAcoesPontosExpiradosCartao">
    update acao_contato , empresa
    set acao_contato.removida = true
    where cartao_id = #{id} and tipo_de_acao = 8 and empresa.id = empresa_id
  </update>

</mapper>
