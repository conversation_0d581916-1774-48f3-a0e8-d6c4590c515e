<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="dadosInstagram">
  <resultMap id="dadosInstagramRM" type="DadosInstagram">
    <id property="id" column="dados_instagram_id"/>

    <result property="nomeInstagram" column="dados_instagram_nome_instagram"/>
    <result property="imageProfileUrl" column="dados_instagram_image_profile_url"/>
    <result property="accessToken" column="dados_instagram_access_token"/>
    <result property="accessTokenPagina" column="dados_instagram_access_token_pagina"/>
    <result property="userIdInsta" column="dados_instagram_user_id_insta"/>
    <result property="idPaginaFace" column="dados_instagram_id_pagina_face"/>

    <association property="empresa" resultMap="empresa.empresaRM"/>
  </resultMap>

  <select id="selecione" parameterType="map" resultMap="dadosInstagramRM" prefix="true">
    select *
    from dados_instagram join empresa on(dados_instagram.empresa_id = empresa.id)
    where
    <if test="idEmpresa != null">
      dados_instagram.empresa_id = #{idEmpresa}
    </if>
    <if test="idEmpresa == null">
      1 = 1
    </if>
    <if test="id != null">
      and dados_instagram.id = #{id}
    </if>
    <if test="idInstagram != null">
      and dados_instagram.user_id_insta = #{idInstagram}
    </if>
    order by dados_instagram.id desc limit 1
  </select>

  <update id="atualize" parameterType="DadosInstagram">
    update dados_instagram set
    nome_instagram = #{nomeInstagram},
    image_profile_url = #{imageProfileUrl},
    access_token = #{accessToken},
    access_token_pagina = #{accessTokenPagina},
    user_id_insta = #{userIdInsta},
    data_criacao = #{dataCriacao},
    id_pagina_face = #{idPaginaFace}
    where
    id = #{id}
    and dados_instagram.empresa_id = #{empresa.id}
  </update>

  <update id="remova">
    delete from dados_instagram where
    id = #{id}
    and empresa_id = #{empresa.id};
  </update>
</mapper>
