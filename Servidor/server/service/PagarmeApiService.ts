
import {IMeioPagamentoService} from "./meiospagamentos/IMeioPagamentoService";
import {Pedido} from "../domain/delivery/Pedido";
import {PagamentoPedido} from "../domain/delivery/PagamentoPedido";
import {StatusPagamentoDeParaPagarme} from "../lib/emun/EnumStatusPagamento";
import {PedidoService} from "./PedidoService";
import {NotificacaoMeioPagamentoService} from "./NotificacaoMeioPagamentoService";

const axios = require('axios');
declare const global: any;

const AppMeucadapio: any = {
   publicKey: '21a6596d-5eeb-4324-9dc2-c5742d5db0d1',
   secretKey: '4512e78f-4e74-4007-aa7b-3ee05de0c7fa'
}

export class PagarmeApiService implements IMeioPagamentoService{
  host = 'https://api.pagar.me/core/v5'
  headers = {}

  static getPublicKey(){
    return AppMeucadapio.publicKey;
  }
  static getPrivateKey(){
    return AppMeucadapio.secretKey;
  }

  constructor(private privateKey: string, hub: boolean) {
    if(hub) this.host = 'https://hubapi.pagar.me/core/v1'
    this.headers = {
      'Authorization': 'Basic ' + Buffer.from(String(`${this.privateKey}:`)).toString('base64'),
      'Content-Type': 'application/json'
     // ,'ServiceRefererName': 'add headers  criaçao pedido ( pegar id com Igor pagarme)'
    }
    console.log(this.host)
    console.log('token:' + this.privateKey)
  }

  static confirmeAtivacaoHub(code: string, homologacao: boolean){
    let payload: any = {
      code: code
    }

    let host = "https://hubapi.pagar.me";

    if( global.desenvolvimento || homologacao )
      host = String(`${host}/dev`)

    const url = String(`${host}/auth/apps/access-tokens`);
    const headers =  {
      'Content-Type': 'application/json',
      'PublicAppKey':  PagarmeApiService.getPublicKey()
    };

    console.log(url);
    console.log(headers);
    console.log(payload);

    return new Promise((resolve, reject) => {
      axios.post(url, payload, {headers: headers}).then(  async  (response: any) => {
        let dados = response.data;
        console.log(dados);
        resolve(dados)
      }).catch( (request: any) => {
        let erro = String(`Não foi possivel confirmar o codigo autorização "${code}"`);
        if( request.response &&   request.response.data){
          console.log(  request.response.data);

          if(request.response.data.errors){
            let retornoErro = request.response.data.errors.map((item: any) => String(`${item.message} `))
            erro += ': ' + retornoErro;
          }
        }
        console.log(erro)
        reject(erro)
      })
    })
  }

  atualizeCliente(contato: any){
    return new Promise((resolve, reject) => {
     let payload: any = {
       name: contato.nome,
       email: contato.email,
       code: contato.id,
       type: 'individual',
       phones: {
         mobile_phone: {
           country_code: "55",
           area_code: contato.telefone.substr(0, 2),
           number: contato.telefone.substr(2)
         }
       }
     }

     if(contato.cpf){
       payload.document = contato.cpf;
       payload.document_type = 'CPF'
     }

      let url = String(`${this.host}/customers/${contato.codigoPagarme}`);
      console.log(url)
      console.log(payload)

      axios.put(url, payload, {headers: this.headers}).then(   (response: any) => {
        let dados = response.data;
        console.log(dados);
        resolve(dados)
      }).catch( (request: any) => {
        let erro = String(`Falha atualizar cliente "${contato.codigoPagarme}"`);
        if( request.response &&   request.response.data){
          let data: any = request.response.data;
          console.log( data);

          if(data.errors){
            let retornoErro = request.response.data.errors.map((item: any) => String(`${item.message} `))
            erro += ': ' + retornoErro;
          } else  if(data.message) {
              erro += ': ' + data.message;
          }
        }
        console.log(erro)
        reject(erro)
      })
    })
  }


  criePedido(payload: any){
    return new Promise((resolve, reject) => {
      axios.post(String(`${this.host}/orders`), payload, {headers: this.headers}).then(   (response: any) => {
        console.log(response.data)
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('criar pedido', request))
      })
    })

  }

  estornePagamento(id: any): Promise<string> {
    return new Promise((resolve, reject) => {
      axios.delete(String(`${this.host}/charges/${id}`),   {headers: this.headers}).then(   (response: any) => {
        console.log(response.data)
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('estornar cobrança', request))
      })
    })
  }


  async sincronizePagamento(pedido: Pedido, pagamentoOnline: PagamentoPedido, empresa: any){
    let erro: string;
    if(pagamentoOnline.codigo) {
      let charge: any =  await this.obtenhaPagamento(pagamentoOnline.codigo).catch((err) => {
        erro = err
      })

      if(charge){
        let novoStatus = StatusPagamentoDeParaPagarme.get(charge.status);
        if(novoStatus.toString() !== pagamentoOnline.status.toString()){
          await pagamentoOnline.atualizeRetornoPagarme(charge);
          pedido.empresa = empresa;
          await new PedidoService().mudouStatusPagamento(pedido, pagamentoOnline, novoStatus);
          pagamentoOnline.pedido = pedido;
          await NotificacaoMeioPagamentoService.notifiqueIntegracaoAtiva(pagamentoOnline, empresa)
        }
      }
    }

    return erro;
  }

  obtenhaPagamento(codigo: string){
    return new Promise((resolve, reject) => {
      axios.get(String(`${this.host}/charges/${codigo}`),  {headers: this.headers}).then(   (response: any) => {
        console.log(response.data)
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('obter pagamento', request))
      })
    })
  }

  listePagamentos(){
    return new Promise((resolve, reject) => {
      axios.get(String(`${this.host}/charges`),  {headers: this.headers}).then(   (response: any) => {
        console.log(response.data)
        resolve(response.data)
      }).catch( (request: any) => {
        reject(this.retornoErro('listar pagamentos', request))
      })
    })
  }

  private retornoErro(operacao: string, req: any, prefixo  = 'Nao foi possivel') {
    let detalhesErro = '';

    if (req.response) {
      console.log('Erro na request ' + req.response.status)
      if (typeof req.toJSON === 'function')
        console.log(req.toJSON())

      if(req.response.status === 403)
        return  String(`${operacao}: Permissão negada (HTTP 403)`)

      if(req.response.status === 401)
        return  String(`${operacao}: Não autorizado, verifique as chaves informadas da conta stone/pargarme`)


      if(req.response.data) {
        console.log(req.response.data)
        if (typeof req.response.data === 'string')
          detalhesErro = req.response.data

        if(req.response.data.message)
          detalhesErro = req.response.data.message

        if(req.response.data.errors){
          let erros: any = []
          Object.keys(req.response.data.errors).forEach((key: string) => {
             let lista = req.response.data.errors[key].join(', ')
             erros.push(String(`${key}: ${lista}`))
          })
          detalhesErro = erros.join(', ');
        }
      } else {
        if(req.response.message)
          detalhesErro = req.response.message
      }
    } else if (req.message){
      detalhesErro = req.message
    }

    let erro = prefixo + ' ' + operacao +  ""

    if(detalhesErro) erro = erro + ": " + detalhesErro

    return erro.trim();
  }


}
