
import {IServiceIntegracaoExternaERP} from "../../domain/integracoes/IServiceIntegracaoExternaERP";
import {Pedido} from "../../domain/delivery/Pedido";
import axios from "axios";
import {BlueSoftProdutoUtils} from "../../lib/integracao/bluesoft/BlueSoftProdutoUtils";

import {DTOPedidoBlueSoft} from "../../lib/integracao/bluesoft/DTOPedidoBlueSoft";
import * as moment from "moment";
import {EnumTipoDeOrigem} from "../../lib/emun/EnumTipoDeOrigem";
import {DTOProdutoSincronizar} from "../../lib/integracao/ecletica/DTOProdutoSincronizar";
import {EnumDisponibilidadeProduto} from "../../lib/emun/EnumDisponibilidadeProduto";
import {Comanda} from "../../domain/comandas/Comanda";
import {MapeadorDeProduto} from "../../mapeadores/MapeadorDeProduto";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
declare const global: any;

//31 - Lojas Consultar
//1.176 - Pedido Venda Consultar
//3.240 - API Categorias de Venda (Consultar)
//3088 - Consultar API de produtos de e-commerce"
//3.089 - Consultar API de preços
//2.731 - Consultar estoque do produto]
export class BlueSoftERPService  implements IServiceIntegracaoExternaERP{
  private urlApi = '';
  token: string;
  grupo: string;
  loja: number;
  lojaEcommerce: number;
  cpfCnpj: any;
  ecommerceKey: number;
  axinst: any;
  constructor(credenciais: any) {
    this.token = credenciais.token;
    this.loja = credenciais.loja;
    this.lojaEcommerce = credenciais.loja;
    this.ecommerceKey = credenciais.idEcommerce ;
    this.cpfCnpj = credenciais.cpfCnpj;
    this.grupo = credenciais.grupo;
    this.urlApi = String(`https://erp.bluesoft.com.br/${this.grupo}/api`)

    this.axinst = axios.create({
      baseURL: this.urlApi,
      timeout: 45 * 1000
    })

    //fixar loja 5 soneda (mesma usada para vtex)
     if(this.ehDoGrupoSoneda())
       this.lojaEcommerce = 5;

  }

  ehDoGrupoSoneda(){
    return this.grupo === 'soneda';
  }

  obtenhaDTOPedido(pedido: any, empresa: any){
    return new DTOPedidoBlueSoft(pedido, empresa);
  }

  adicionePedido(pedido: Pedido, empresa: any): Promise<string> {
    return new Promise(async (resolve, reject) => {
      try {
        if(pedido.referenciaExterna){
          console.log('Já foi notificado para o bluesoft' + pedido.referenciaExterna)
          return  resolve(pedido.referenciaExterna)
        }

        if(global.desenvolvimento)
          return reject('Bluesoft nao possui Ambiente de Teste.');

       if(pedido.foiCanceladoOuDevolvido())
          return  reject('Pedido foi cancelado, não pode ser enviado.')

        let dados: any = this.obtenhaDTOPedido(pedido, empresa)

        console.log(dados)
        let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};
        axios.post(String(`${this.urlApi}/venda/pedidovenda/e-commerce`) , dados,
          { headers: headers}).then(   (response: any) => {
          resolve(response.data.pedidoVendaKey)
        }).catch((erro: any) => {
          reject(this.retornoErro('adicionar pedido', erro))
        })
      } catch (execption){
        console.log('**erro adicionar pedido**')
        console.log(execption)
        reject(this.retornoErro('enviar pedido', execption.message))
      }
    })
  }

  alterePedido(pedidoNovo: Pedido, pedidoAntigo: Pedido, empresa: any): Promise<string> {
    return Promise.resolve("");
  }

  cancelePedido(pedido: Pedido): Promise<any> {
    return Promise.resolve(undefined);
  }

  listeBandeiras(tipo: string): Promise<Array<any>> {
    return Promise.resolve(undefined);
  }

  listeLojas(): Promise<Array<any>> {
    return new Promise(async (resolve, reject) => {
      let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};

      axios.get(String(`${this.urlApi}/lojas`) ,
        { headers: headers}).then(   (response: any) => {
        resolve(response.data.lojas)
      }).catch((erro: any) => {
        if(erro.response && erro.response.status === 404)
          return reject(String(`Verifique o grupo informado "${this.grupo}"`))

        if(erro.response && erro.response.status === 403)
          return reject(String(`Verifique o token informado`))

        reject(this.retornoErro('listar lojas', erro))
      })
    })
  }

  obtenhaArvoreMercadologica(){
    return new Promise(async (resolve, reject) => {
      let headers = {"x-CustomToken": this.token, "Content-Type": "application/json;charset=UTF-8"};
      //exportaPdv ? //vendaExterna
      axios.get(String(`${this.urlApi}/comercial/arvoremercadologica`) ,
        { headers: headers}).then(   (response: any) => {
        resolve(response.data.data)
      }).catch((erro: any) => {
        reject(this.retornoErro('buscar produtos', erro))
      })
    })
  }

  private listeProximosEstoque(listaProdutos: any[],  ultimaSincronizacaoEstoque: any,
                               pagina: number, total: number, produtoKey: number = null,
                               tempoExecucao = { inicio: new Date().getTime(), tempo: 0 }): Promise<any>{
    return new Promise(async (resolve, reject) => {
      let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};
      let query = String(`currentPage=${pagina}&pageSize=${total}&lojaKey=${this.loja}`);

      if(produtoKey){
        query += '&produtoKey=' + produtoKey;
      } else {
        if(ultimaSincronizacaoEstoque){
          let dataAlteracao = moment(ultimaSincronizacaoEstoque).startOf('day');

          if(moment().diff(moment(ultimaSincronizacaoEstoque), 'h') > 24)
            dataAlteracao = moment().add(-24 , 'h')

          query += '&dataAlteracaoInicio=' + dataAlteracao.format('DD/MM/YYYY HH:mm')
        }

      }

      console.log('buscando estoque produtos: ' + pagina + "/" + listaProdutos.length);
      let t1 = new Date().getTime();

      axios.get(String(`${this.urlApi}/comercial/estoques?${query}`) ,
        { headers: headers}).then( async  (response: any) => {
        console.log('Tempo: ' + (new Date().getTime() - t1));
        let items: any = response.data.data;

        if(items.length) { // ainda tem produtos
          let proximaPagina = response.data.currentPage + 1;
          let totalPagina = response.data.pageSize;
          let produtosPagina: any = BlueSoftProdutoUtils.converaEstoqueEmProdutos(items, this.loja)
          listaProdutos.push(...produtosPagina)

          tempoExecucao.tempo = Number(((new Date().getTime() - tempoExecucao.inicio) / 1000).toFixed(0))

          console.log(tempoExecucao)
          if(tempoExecucao.tempo  % 20   === 0 ){
            console.log('fazendo ping perder conexão com mysql: ' + new Date())
            await new MapeadorDeEmpresa().ping();
          }

          if(!produtoKey)
            await this.listeProximosEstoque(listaProdutos, ultimaSincronizacaoEstoque, proximaPagina,
              totalPagina, produtoKey, tempoExecucao);
        }
        resolve(true);
      }).catch((erro: any) => {
        console.log(erro)
        reject(this.retornoErro('Consultando Estoque', erro))
      })
    });
  }

  private listeProximosPrecos(listaProdutos: any[],  ultimaSincronizacaoPrecos: any,
                              pagina: number, total: number, produtoKey: number = null){
    return new Promise(async(resolve, reject) => {
      let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};
      let query = String(`currentPage=${pagina}&pageSize=${total}&lojaKey=${this.loja}`);

      if(produtoKey){
        query += '&produtoKey=' + produtoKey;
      } else {
        if(ultimaSincronizacaoPrecos)
          query += '&dataAlteracaoInicio=' + moment(ultimaSincronizacaoPrecos).startOf('day').format('DD/MM/YYYY HH:mm')
      }

      console.log('buscando preços produtos: ' + pagina + "/" + listaProdutos.length);
      let t1 = new Date().getTime();

      //dataAlteracaoInicio dataDoPrecoPraticado: "05/10/2021"
      axios.get(String(`${this.urlApi}/vendas/precos?${query}`) ,
        { headers: headers}).then( async  (response: any) => {
        console.log('Tempo: ' + (new Date().getTime() - t1));
        let items: any = response.data.data;
        if(items.length) { // ainda tem produtos
          let proximaPagina = response.data.currentPage + 1;
          let totalPagina = response.data.pageSize;
          let produtosPagina: any = BlueSoftProdutoUtils.converaPrecosEmProdutos(items)
          listaProdutos.push(...produtosPagina)
          if(!produtoKey)
           await this.listeProximosPrecos(listaProdutos, ultimaSincronizacaoPrecos, proximaPagina, totalPagina);
        }
        resolve(true);

      }).catch((erro: any) => {
        console.log(erro)
        reject(this.retornoErro('Consultando preços', erro))
      })
    })
  }

  private listeProximosProdutos(listaProdutos: any[], ultimaSincronizacaoProdutos: any, inicio: number, total: number){
    return new Promise(async(resolve, reject) => {

      let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};
      let query = String(`ecommerceKey=${this.ecommerceKey}&currentPage=${inicio}&pageSize=${total}&lojaKey=${this.lojaEcommerce}`);
      console.log('buscando produtos: ' + inicio + "/" + listaProdutos.length);
      let t1 = new Date().getTime();

      if(ultimaSincronizacaoProdutos)
        query += '&dataAlteracaoInicio=' + moment(ultimaSincronizacaoProdutos).startOf('day').format('DD/MM/YYYY HH:mm')

      this.axinst.get(String(`vendas/integracao/ecommerces/produtos?${query}`) ,
        { headers: headers}).then(  async  (response: any) => {
         console.log('Tempo: ' + (new Date().getTime() - t1));
        let produtosPagina = response.data.data;
        if(produtosPagina.length){ // ainda tem produtos
          let proximaPagina = response.data.currentPage + 1;
          let totalPagina = response.data.pageSize;
          listaProdutos.push(...produtosPagina)
           this.listeProximosProdutos(listaProdutos, ultimaSincronizacaoProdutos, proximaPagina, totalPagina)
             .then(() => resolve(true)).catch((err) => { reject(err) });
        } else {
          resolve(true);
        }
      }).catch(async (erro: any) => {

        if(erro.message && erro.message.indexOf('timeout') >= 0){
           await this.listeProximosProdutos(listaProdutos, ultimaSincronizacaoProdutos, inicio, total)
           resolve(true);
        } else {
          console.log(erro)
          reject(this.retornoErro('Consultando Produtos', erro))
        }
      })
    })
  }

  listeProdutos(ultimaSincronizacaoProdutos: any = null): Promise<any> {
    return new Promise(async (resolve, reject) => {
      let todosProdutos: any = [];
      this.listeProximosProdutos(todosProdutos, ultimaSincronizacaoProdutos, 0, 1000).then( () => {
        resolve(todosProdutos)
      }).catch((erro) => reject(erro))
    })
  }

  obtenhaProduto(codigo: string){

    return new Promise(async(resolve, reject) => {
      let headers = { "x-CustomToken":  this.token , "Content-Type": "application/json;charset=UTF-8"};
      let query = String(`ecommerceKey=${this.ecommerceKey}&produtoKey=${codigo}&lojaKey=${this.lojaEcommerce}`);

      axios.get(String(`${this.urlApi}/vendas/integracao/ecommerces/produtos?${query}`) ,
        { headers: headers}).then(  async  (response: any) => {
        let produtosPagina = response.data.data;

        resolve(produtosPagina.length ? produtosPagina[0] : null);
      }).catch((erro: any) => {
        console.log(erro)
        reject(this.retornoErro('buscar produto por codigo', erro))
      })
    })
  }

  listeEstoqueProdutos(ultimaSincronizacaoEstoque: any, produtoKey: number = null): Promise<Array<any>> {
    return new Promise(  (resolve, reject) => {
      let todosProdutos: any = [];
      this.listeProximosEstoque(todosProdutos, ultimaSincronizacaoEstoque, 0, 1000, produtoKey).then( () => {
        resolve(todosProdutos)
      }).catch((erro) => reject(erro))
    })
  }

  listePrecosProdutos(ultimaSincronizacaoPrecos: any, produtoKey: any = null): Promise<Array<any>> {
    return new Promise(  (resolve, reject) => {
      let todosProdutos: any = [];
        this.listeProximosPrecos(todosProdutos, ultimaSincronizacaoPrecos, 0, 1000, produtoKey).then( () => {//max é 1000 api retorna
          resolve(todosProdutos)
        }).catch((erro) => reject(erro))
    })
  }

  obtenhaProdutoConvertido(codigo: string){
    return new Promise(async (resolve, reject) => {
      this.obtenhaProduto(codigo).then(async (produtoErp: any) => {
        if(produtoErp){
          let estoqueProdutos: any = [], precosProdutos: any = [];

          let cagetorias = await this.listeCategoriasEcommerce(0, 1000);

          if(this.ehDoGrupoSoneda()){ // procesar estoque e preços fora do ecommerce
            estoqueProdutos = await this.listeEstoqueProdutos(null, produtoErp.produtoKey);
            precosProdutos = await this.listePrecosProdutos(null, produtoErp.produtoKey);
          }

          let  produtos =
            BlueSoftProdutoUtils.convertaParaProdutos(this.lojaEcommerce, [produtoErp], cagetorias, estoqueProdutos, precosProdutos);

          resolve(produtos[0])
        } else {
          resolve(null)
        }
      }).catch((erro) => {
        reject(erro)
      })
    })
  }

  listeProdutosConvertidos(ultimaSincronizacaoProdutos: any): Promise<Array<any>>{
    return new Promise(async (resolve, reject) => {
      let cagetorias = await this.listeCategoriasEcommerce(0, 1000);

      let produtos: any = await this.listeProdutos(ultimaSincronizacaoProdutos).catch((erroIntegracao: any) => {
        reject(erroIntegracao)
      })

      if (produtos) {
        let estoqueProdutos: any = [], precosProdutos: any = [];

       if(this.ehDoGrupoSoneda()){ // procesar estoque e preços fora do ecommerce
          estoqueProdutos = await this.listeEstoqueProdutos(ultimaSincronizacaoProdutos);
          precosProdutos = await this.listePrecosProdutos(ultimaSincronizacaoProdutos);
        }

        let produtosDisponiveis: any =
          BlueSoftProdutoUtils.convertaParaProdutos(this.lojaEcommerce, produtos, cagetorias, estoqueProdutos, precosProdutos);


        resolve(produtosDisponiveis);
      }
    })
  }


  listeProdutosIndisponiveis(): Promise<any>{
    return Promise.resolve([])
  }

  listeProdutosDisponiblidadeAtualizada(produtosIntegrados: any, ultimaSincronizacao: Date): Promise<Array<DTOProdutoSincronizar>> {
    let produtosAtualizados: any = [], sincronizandoTudo = !ultimaSincronizacao;

    return new Promise(async (resolve, reject) => {
      if(produtosIntegrados && produtosIntegrados.length){
        let produtosEstoque: any = await this.listeEstoqueProdutos(ultimaSincronizacao).catch(erro => reject(erro));

        if(produtosEstoque && produtosEstoque.length){
          produtosIntegrados.forEach( (produto: any) => {
            let produtoEstoque: any = produtosEstoque.find((item: any) => item.codigoPdv === produto.codigoPdv);

            if(produto.estaDisponivel()) {
              if ( (produtoEstoque && produtoEstoque.disponibilidade === EnumDisponibilidadeProduto.NaoDisponivel) ||
                   (!produtoEstoque  && sincronizandoTudo))
                produtosAtualizados.push(new DTOProdutoSincronizar(produto, null, null, true))
            } else{
              if(produtoEstoque && produtoEstoque.disponibilidade === EnumDisponibilidadeProduto.SempreDisponivel)
                produtosAtualizados.push( new DTOProdutoSincronizar(produto, produto.obtenhaPrecos(), null, false))
            }
          })
        }


      }
      resolve(produtosAtualizados)
    })
  }


  valideToken(): Promise<any> {
    return new Promise(async (resolve, reject) => {

      this.obtenhaProduto('1213456').then(    (response: any) => {
          resolve( true)
      }).catch((erro: any) => {
        reject(this.retornoErro('obter produto (validar token) ', erro))
      })
    })
  }

  veririqueUpdates(data: any): Promise<any> {
    return Promise.resolve({});
  }

  fecheComanda(comanda: Comanda, empresa: any){
    return Promise.resolve();
  }

  private retornoErro(operacao: string, erro: any) {
    if( erro.response){
      if(erro.response.status === 404)
        return String(`${operacao}: recurso nao econtrado "${erro.config.url}"`)

      if(erro.response.status === 403)
        return  String(`${operacao}: Permissão negada (HTTP 403)`)

      if(erro.response.data && erro.response.data.erros){
        console.log(erro.response.data.erros)
        let erroDestalhes: any = erro.response.data.erros[0];

        if(erroDestalhes.campo)    return String(`${operacao}: ${erroDestalhes.campo} - ${erroDestalhes.mensagem}`)

        return String(`${operacao}: ${erroDestalhes.mensagem}`)
      }
    }

    if(erro.message) return String(`${operacao}: ${erro.message}`)

    return String(`${operacao}: ${erro}`)
  }

  async listeCategoriasEcommerce(inicio = 0, total = 30) {
    return new Promise(async (resolve, reject) => {
      let headers = {"x-CustomToken": this.token, "Content-Type": "application/json;charset=UTF-8"};
      let query = String(`ecommerceKey=${this.ecommerceKey}&currentPage=${inicio}&pageSize=${total}`);

      //401 não autorizado
      axios.get(String(`${this.urlApi}/e-commerce/categoria-de-venda?${query}`), {headers: headers}).then((response: any) => {
        resolve( response.data.data)
      }).catch((erro: any) => {
        reject(this.retornoErro('buscar categorias vendas', erro))
      })
    });
  }

  obtenhaTipoDeOrigem(): EnumTipoDeOrigem {
    return EnumTipoDeOrigem.ImportadoBluesoft;
  }
}
