import {IEnviadorDeMensagem} from "./IEnviadorDeMensagem";
import {SituacaoDeMensagem} from "./SituacaoDeMensagem";
import {StatusDeMensagem} from "./StatusDeMensagem";
import {MensagemEnviada} from "../domain/MensagemEnviada";
import {TipoDeNotificacaoEnum} from "../domain/TipoDeNotificacaoEnum";
import {EnumMeioDeEnvio} from "../domain/EnumMeioDeEnvio";
import {Empresa} from "../domain/Empresa";
import {EnviadorDeMensagemCloudWhatsapp} from "./EnviadorDeMensagemCloudWhatsapp";
import {EnviadorDeMensagemWhatsapp} from "./EnviadorDeMensagemWhatsapp";

/**
 * Implementação de IEnviadorDeMensagem que combina o comportamento de EnviadorDeMensagemCloudWhatsapp e EnviadorDeMensagemWhatsapp.
 * Mensagens relacionadas a pedidos são enviadas pela API oficial (EnviadorDeMensagemCloudWhatsapp),
 * enquanto outros tipos de mensagens são enviadas pela extensão (EnviadorDeMensagemWhatsapp).
 */
export class EnviadorDeMensagemMisto implements IEnviadorDeMensagem {
  private enviadorCloudWhatsapp: EnviadorDeMensagemCloudWhatsapp;
  private enviadorWhatsapp: EnviadorDeMensagemWhatsapp;

  constructor() {
    this.enviadorCloudWhatsapp = new EnviadorDeMensagemCloudWhatsapp();
    this.enviadorWhatsapp = new EnviadorDeMensagemWhatsapp();
  }

  /**
   * Verifica se o tipo de notificação está relacionado a pedidos
   * @param tipoDeNotificacao Tipo de notificação a ser verificado
   * @returns true se o tipo de notificação estiver relacionado a pedidos, false caso contrário
   */
  private deveSerTratadoPelaCloudWhatsapp(tipoDeNotificacao: TipoDeNotificacaoEnum): boolean {
    return tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoAlterado ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoSaiuParaEntrega ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoEmPreparacao ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoPronto ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoCancelado ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoEntregue ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoConfirmado ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoRealizadoCardapioOnline ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.ConfirmacaoPedido ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.AvaliarPedido ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PedidoAssociadoEntregador ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.LinkPagamentoPedido ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PagamentoPendenteOnline ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.MensagemSaudacaoWhatsappPedido ||
      tipoDeNotificacao === TipoDeNotificacaoEnum.PagamentoConfirmadoOnline;
  }

  /**
   * Obtém o enviador apropriado com base no tipo de notificação
   * @param mensagemEnviada Mensagem a ser enviada
   * @returns O enviador apropriado para o tipo de notificação
   */
  private obtenhaEnviadorApropriado(mensagemEnviada: MensagemEnviada): IEnviadorDeMensagem {
    if (this.deveSerTratadoPelaCloudWhatsapp(mensagemEnviada.tipoDeNotificacao)) {
      console.log(`[EnviadorDeMensagemMisto] Usando API oficial para enviar mensagem do tipo ${mensagemEnviada.tipoDeNotificacao}`);
      return this.enviadorCloudWhatsapp;
    } else {
      console.log(`[EnviadorDeMensagemMisto] Usando extensão para enviar mensagem do tipo ${mensagemEnviada.tipoDeNotificacao}`);
      return this.enviadorWhatsapp;
    }
  }

  acompanheMensagem(idMensagem: string): Promise<SituacaoDeMensagem> {
    // Ambos os enviadores podem acompanhar a mensagem, mas vamos usar o CloudWhatsapp por padrão
    return this.enviadorCloudWhatsapp.acompanheMensagem(idMensagem);
  }

  envieMensagem(mensagemEnviada: MensagemEnviada, telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    const enviador = this.obtenhaEnviadorApropriado(mensagemEnviada);
    return enviador.envieMensagem(mensagemEnviada, telefone, mensagem);
  }

  envieSMS(telefone: string, mensagem: string): Promise<SituacaoDeMensagem> {
    // Para SMS, vamos usar o CloudWhatsapp por padrão
    return this.enviadorCloudWhatsapp.envieSMS(telefone, mensagem);
  }

  envieMensagemAtivacaoContato(mensagemEnviada: MensagemEnviada, telefoneContato: string): Promise<SituacaoDeMensagem> {
    // Para ativação de contato, vamos usar o CloudWhatsapp por padrão
    return this.enviadorCloudWhatsapp.envieMensagemAtivacaoContato(mensagemEnviada, telefoneContato);
  }

  requerAtivacaoDoTelefone(): boolean {
    // Ambos os enviadores podem requerer ativação do telefone, mas vamos usar o CloudWhatsapp por padrão
    return this.enviadorCloudWhatsapp.requerAtivacaoDoTelefone();
  }

  obtenhaMeioDeEnvio(): EnumMeioDeEnvio {
    // Retorna o meio de envio misto
    return EnumMeioDeEnvio.Whatsapp; // Ou poderia ser um novo tipo específico para o modo misto
  }

  notifiqueAssinantes(mensagemEnviada: MensagemEnviada) {
    const enviador = this.obtenhaEnviadorApropriado(mensagemEnviada);
    enviador.notifiqueAssinantes(mensagemEnviada);
  }

  notifiqueAssinantesEmpresa(empresa: Empresa) {
    // Ambos os enviadores podem notificar assinantes da empresa, mas vamos usar o CloudWhatsapp por padrão
    this.enviadorCloudWhatsapp.notifiqueAssinantesEmpresa(empresa);
  }
}
