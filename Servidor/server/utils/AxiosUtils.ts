import axios, { AxiosError } from 'axios';
const axiosRetry = require('axios-retry');

// Configuração global do axios-retry
axiosRetry(axios, {
  retries: 3, // Número de tentativas
  shouldResetTimeout: true,
  retryDelay: (retryCount: number, error: AxiosError) => {
    // Log um pouco mais detalhado
    const url = error.config?.url || 'URL desconhecida';
    console.log(`[axiosRetry] Tentativa ${retryCount} falhou para ${url}. Status: ${error.response?.status || 'N/A'}. Tentando novamente em 200ms...`);
    return 200; // Tempo de espera em milissegundos
  },
  retryCondition: (error: AxiosError): boolean => {
    // Verificar se a requisição foi para qualquer API da TomTom
    const url = error.config?.url;
    const isTomtomApi = url?.includes('api.tomtom.com');

    // Se for qualquer API da TomTom, NÃO retentar
    if (isTomtomApi) {
      console.log(`[axiosRetry] [TomTom] Chamada para ${url}. Não tentando novamente.`);
      return false; // Impede a retentativa para qualquer chamada TomTom
    }

    // Para outros erros e outras APIs, usar a lógica padrão do axios-retry (mais segura)
    // Tenta novamente apenas para erros de rede ou erros 5xx (servidor)
    const shouldRetryDefault = axiosRetry.isNetworkOrIdempotentRequestError(error);
    if (!shouldRetryDefault) {
      console.log(`[axiosRetry] Condição padrão não satisfeita para ${url}. Status: ${error.response?.status || 'N/A'}. Não tentando novamente.`);
    }
    return shouldRetryDefault;
  },
});

// Exporta a instância configurada do axios
export default axios; 