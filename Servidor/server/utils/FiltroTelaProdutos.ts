export class FiltroTelaProdutos{
  constructor(public query: any, public empresa: any) {
  }

  toSqlQuery(){
    const temEstoque = this.query.temEstoque === 'true';

    let dados: any = {
      temEstoque: temEstoque
    };

    if(this.query.i){
      dados.inicio = Number(this.query.i)
      dados.total = Number(this.query.t || 10)
    }

    if(this.query.tipo){ // buscando so produtos
      dados.tipo = this.query.tipo
      dados.semAdicionais = true; // nao carregar adicionais
      if(this.query.q){
        dados.nomeProduto =  String(`%${this.query.q}%`)
        dados.like = String(`%${this.query.q}%`)
      }
    } else if(this.query.q){
      dados.termo = this.query.q
      dados.like = String(`%${this.query.q}%`)
    }

    if( this.query.idc ){
      if(this.empresa.temCategoriasVariosNiveis()){
        dados.categoriaPai =   Number(this.query.idc)
      } else {
        dados.categoria =  Number(this.query.idc)
      }
    }

    if(this.query.disp)
      dados.disponibilidade = this.query.disp.split(',').map((item: any) => Number(item));

    if(this.query.scpdv)
      dados.semCodigoPdv = true;

    if(this.query.ans)
      dados.apenasNaoSincronizados = true;

    if(this.query.rede)
      dados.rede = true

    if(this.query.tam)
      dados.idTamanho = Number(this.query.tam)

    if(this.query.notin)
      dados.idsNotIn = this.query.notin.split(',').map((item: any) => Number(item));

    if(this.query.cod)
      dados.codigoPdv = this.query.cod

    return dados;

  }
}
