<!DOCTYPE html>
<html lang="pt">
<head>
    <link href="/assets/template/css/bootstrap.min.css" rel="stylesheet"/>
    <style media="print" type="text/css">
        .page {
            background-color: white !important;
        }

        .table td, .table th {
            border-top: none !important;
        }

        @media print {
            .table-striped tbody tr:nth-of-type(odd) td {
                background-color: #f1f5f7 !important;
            }
        }
    </style>

    <style>
        body {
            -webkit-print-color-adjust: exact !important;
        }

        html {
            -webkit-print-color-adjust: exact;
        }

        table, tr, td {
            -webkit-print-color-adjust: exact;
        }

        .coluna-preco {
            width: 120px;
            font-weight: bold;
            color: #000;
        }

        .coluna-ordem {
            position: relative;
            width: 150px;
        }

        .ordem {
            position: absolute;
            width: 32px;
            height: 32px;
            background: #299be9;
            border-radius: 50px;
            padding: 5px;
            color: #fff;
            text-align: center;
            line-height: 24px;
            margin-right: 10px;
            top: 50%;
            margin-top: -15px;
        }

        .table td, .table th {
            padding: 0.5rem !important;
        }

        .font-12 {
            font-size: 12px;
        }

        .categoria {
            background: #299be9;
            color: #fff;
            text-align: center;
        }
    </style>
</head>
<body style="background: #fff;font-size: 14px;">
<h1 class="text-center">
    <img src="/images/empresa/<%= empresa.logo %>" style="width: 100px;"/>
    Cardápio <%= empresa.nome%>
</h1>
<div class="container">
    <% for( var i = 0; i < produtos.length; i++ ) { %>
    <% var categoria = produtos[i]; %>
        <h2 class="categoria"><%= categoria.categoria  %></h2>
        <table class="table table-striped">
            <thead>
            <tr>
                <th scope="col">Código</th>
                <th scope="col">Produto</th>
                <th scope="col">Preço</th>
            </tr>
            </thead>
            <tbody>
            <% for( var j = 0; j < categoria.produtos.length; j++ ) { %>
                <% var produto = categoria.produtos[j]; %>
            <tr>
                <td class="coluna-ordem">
                    <div class="ordem" style="position: absolute;width: 32px;height: 32px;background: #299be9;border-radius: 50px;padding: 5px;color: #fff;text-align: center;line-height: 24px;margin-right: 10px;top: 50%;margin-top: -15px;"><%= produto.ordem %></div>
                    <% if( produto.imagens ) { %>
                        <% for( var k = 0; k < produto.imagens.length; k++ ) { %>
                        <img src="/images/empresa/<%= produto.imagens[k].linkImagem %>"
                             class="img-rounded" style="float: left;margin-left: 45px;width: 60px;"/>
                        <% } %>
                    <% } %>
                </td>
                <td>
                    <h4 class="font-14" style="margin-bottom: 8px;font-weight: bold;color: #000;"><%= produto.nome %></h4>
                    <span class="font-12"><i><%= produto.descricao%></i></span>
                </td>
                <td class="coluna-preco">
                    <% if( produto.valorMinimo ) { %>
                        <span class="text-black-50">A partir de:</span><br>
                        <span class="font-16 text-green">R$ <%= produto.valorMinimo.toFixed(2).replace(".", ",") %></span>
                    <% } else {%>
                      <%  if( produto.precoAntigo ) { %>
                          <span class="text-black-50">De:</span>  <span class="text-muted">
                            R$ <%= produto.precoAntigo.toFixed(2).replace(".", ",") %>
                            </span><br> <span class="text-black-50">Por:</span>
                        <% } %>
                          <span class="font-16 text-green">R$ <%= produto.preco ? produto.preco.toFixed(2).replace(".", ",") : '' %></span>
                    <%}%>

                </td>
            </tr>
            <% } %>
            </tbody>
        </table>
    <% } %>
</div>
<script type="text/javascript">
    var img = document.getElementsByTagName("img");
    for( var i = 0; i < img.length; i++ ) {
        console.log(img[i]);
        img[i].onerror = function () {
            this.style.display = "none";
        }
    }
</script>
</body>
</html>
