<html lang="pt">

<head>
  <meta charset="utf-8">
  <base href="/"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-capable" content="yes">


  <meta name="description" content="Compartilhe seu cardápio digital, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp" >
  <meta name="robots" content="index, follow">
  <meta property="og:title" content="Meu Cardápio - Compartilhe seu cardápio, receba pedidos, controle seu Delivery e fidelize seus clientes sem sair do WhatsApp. Cardápio com QR Code.">
  <meta property="og:site_name" content="Meu Cardápio">
  <meta property="og:description" content="Compartilhe seu cardápio, aceite pedidos de seus clientes para retirada ou delivery e fidelize sem sair do WhatsApp. Cardápio com QR Code.">
  <meta property="og:image" content="https://meucardapio.ai/lp/cardapio/images/img02.jpg">
  <meta property="og:type" content="website">

  <script type="application/ld+json">
    {
      "@context" : "http://schema.org",
      "@type" : "SoftwareApplication",
      "name" : "Meu Cardápio",
      "brand": "Meu Cardápio",
      "description": "Compartilhe seu cardápio, receba pedidos, controle seu Delivery e fidelize seus clientes sem sair do WhatsApp. Cardápio com QR Code.",
      "image" : "https://meucardapio.ai/lp/cardapio/images/img02.jpg",
      "applicationCategory" : [ "plataforma", "pedidos", "delivery", "fidelidade", "cardápio", "qrcode" ],
      "screenshot" : "https://promokit.promokit.com.br/lp/cardapio/images/img02.jpg"
    }
  </script>

  <title>Meu Cardápio, o seu Cardápio digital online ou cardápio via qr code.</title>
  <link rel="stylesheet" href="/lp/pedidos/css/styles.css">
  <link rel="stylesheet" href="/lp/cardapio/css/style.css">

  <!-- Global site tag (gtag.js) - Google Ads: 1028673002 -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=AW-1028673002"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'AW-1028673002');
  </script>
</head>

<body>

<header class="navbar navbar-expand navbar-dark flex-column flex-md-row bd-navbar" style="">
  <div class="container">
    <a class="navbar-brand mr-2 mr-md-2">
      <button  class="btn btn-success btn-rounded   shadow-lg " type="button"
               onclick="fazerCadastro();">QUERO CONHECER</button>
    </a>

    <ul class="navbar-nav flex-row ml-auto  d-flex">
      <li class="nav-item">
        <a class="nav-link p-2" href="https://solucoesageis.com.br" target="_blank">
          <img src="/lp/cardapio/images/logo-fibo.png" height="26" alt="" class="logo-fibo-topo" alt="logomarca" >
        </a>
      </li>
    </ul>
  </div>
</header>

<div   class="container-fluid  main fundo-azul bloco1"  >
  <div class="container pt-5">
    <div class="row">
      <div class="col-xs-12 col-lg-6">
        <img class="logo" src="/lp/cardapio/images/logo-meucardapio.png" alt="sistema cardápio digital" >

        <h4 class="text-success"> <b>Cardápio Digital para WhatsApp</b>
          <i class="icone checkin"></i>
        </h4>
      </div>
      <div class="col-xs-12 col-lg-6">
        <h3 class="text-blue" >
          <b>Criar um MeuCardapio é fácil, prático e vai facilitar receber pedidos pelo WhatsApp</b>
        </h3>
        <h4>
          MeuCardapio.ai aumenta a produtividade dos funcionários e melhora  seu atendimento.

        </h4>

        <h4 class="text-black"><b>Faça seu delivery bombar!</b></h4>

        <button  class="btn btn-success btn-rounded   shadow-lg " type="button"
                 onclick="fazerCadastro();">QUERO CONHECER</button>

      </div>
    </div>
    <img class="img01" src="/lp/cardapio/images/img02.jpg" class="centralizado" alt="cardápio digital" >
  </div>

</div>

<div class="container  pt-5 bloco2">
  <div class="row" style="position: relative">
    <div class=" col-xs-12  col-lg-7 coluna-cadastro">
      <h3 class="text-blue">
        <b>Planos flexíveis que cabem no seu bolso</b>
      </h3>
      <h4>Planejado para realidade de pequenos e médios negócios, inclusive no bolso.</h4>

      <form class="needs-validation ng-untouched ng-pristine mt-4" novalidate>

        <div   class="form-group">
          <input   class="form-control ng-untouched ng-pristine ng-invalid"
                   id="nome" name="nome" placeholder="Informe seu nome"
                   required="" type="text">
          <div   class="invalid-feedback">
            <p  >Nome é obrigatório</p>
          </div>
        </div>

        <div  class="form-group">
          <input  class="form-control ng-untouched ng-pristine ng-invalid"  placeholder="(99) 9-999-9999"
                  mask="(00)_0-0000-0000"    type="tel" id="telefone"
                  mask-output="alphanumeric"  minlength="10" name="telefone"
                  placeholder="Informe seu Whatsapp" required=""
          ><!---->
          <div class="invalid-feedback"><!---->
            <div > Whatsapp é obrigatório</div><!----></div>
        </div>



        <div   class="form-group">
          <input   class="form-control ng-untouched ng-pristine ng-invalid"
                   id="email" name="email" placeholder="Informe seu e-mail de contato"
                   required="" type="email">
          <div   class="invalid-feedback">
            <p  >E-mail é obrigatório</p>
          </div>
        </div>

        <div class="form-group">

          <div class="input-group">
            <div class="input-group-prepend">
              <span class="input-group-text" id="basic-addon3">@</span>
            </div>
            <input type="text" class="form-control"
                   placeholder="Instagram, exemplo: @promokitoficial" id="verification-instagram" name="instagram" aria-describedby="basic-addon3" required="">

            <div class="invalid-feedback"  >
              <p>Instagram é obrigatório</p>
            </div>
          </div>

        </div>

        <div  class="form-group">
          <input  class="form-control ng-untouched ng-pristine ng-invalid" type="text"
                  name="empresa"     placeholder="Informe o nome da Empresa" required="" >
          <div class="invalid-feedback">
            <p  >Empresa é obrigatório</p>
          </div>
        </div>
        <div  class="form-group">
          <select   class="form-control"  id="cboTipoDeNegocio" required="true" name="cboTipoDeNegocio" style="height: 50px;">
            <option value="">Minha empresa é</option>
            <option value="2" data>Bar</option>
            <option value="3">Barbearia</option>
            <option value="4">Casa de Bolos</option>
            <option value="5">Cervejas Especiais</option>
            <option value="6">Clínicas de Estética</option>
            <option value="1">Empresa de Acrílico</option>
            <option value="7">Esmalteria</option>
            <option value="8">Estúdios de beleza</option>
            <option value="9">EVS</option>
            <option value="10">Hamburgueria</option>
            <option value="11">Loja de Chocolate</option>
            <option value="12">Petshop</option>
            <option value="13">Pizzaria</option>
            <option value="14">Redes de Franquias</option>
            <option value="15">Restaurante</option>
            <option value="16">Sorveteria</option>
            <option value="17">Outros</option>
          </select>
          <div class="invalid-feedback"><!---->
            <div > Segmento é obrigatório</div><!----></div>

          <input  type="hidden" id="tipoDeNegocio" name="tipoDeNegocio"       >
        </div>


        <div  class="form-group">
          <label style="color: #333;font-weight: bold;">Quais são os principais desafios que te levaram a procurar um cardápio digital?</label>

          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="consquistar" name="desafios"  value="Conquistar novos clientes" >
            <label class="form-check-label" for="consquistar">Conquistar novos clientes</label>
          </div>

          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="pedidos" name="desafios"   value="Recebo muitos pedidos ao mesmo tempo no Whatsapp" >
            <label class="form-check-label" for="pedidos">
              Recebo muitos pedidos ao mesmo tempo no Whatsapp
            </label>
          </div>
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="fidelizar"  name="desafios" value="Fidelizar meus clientes atuais">
            <label class="form-check-label" for="fidelizar">
              Fidelizar meus clientes atuais
            </label>
          </div>
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="funcionario" name="desafios" value="Ter um funcionário apenas para responder os clientes">
            <label class="form-check-label" for="funcionario">
              Ter um funcionário apenas para responder os clientes
            </label>
          </div>
          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="gerenciar" name="desafios" value="Tenho que gerenciar minhas vendas em planilhas ou papel">
            <label class="form-check-label" for="gerenciar">
              Tenho que gerenciar minhas vendas em planilhas ou papel
            </label>
          </div>

          <div class="form-check">
            <input type="checkbox" class="form-check-input" id="aplicativos" name="desafios" value="As taxas dos aplicativos de delivery são muito altas">
            <label class="form-check-label" for="aplicativos">
              As taxas dos aplicativos de delivery são muito altas
            </label>
          </div>


        </div>
        <div class="clearfix"></div>

        <p id="msgErro" class="text-danger mb-2 mt-2"></p>


        <button  class="btn btn-success btn-rounded   shadow-lg botaoEnviar mt-2"  type="submit">
          &nbsp;&nbsp;&nbsp;ENVIAR&nbsp;&nbsp;&nbsp;</button>




      </form>
    </div>
    <div class="  col-xs-12 col-lg-5 coluna-segmentos">
      <div class="segmentos-palavras">
        <p><span class="font-g text-blue"><b>Hamburgueria</b></span> <span class="text-muted"><b>Pizzaria</b></span></p>
        <p><span>Barbeararia</span> <span class="text-success font-18 top1"><b>Restaurante</b></span></p>
        <p><span class="text-success">Fitness</span> <span class="text-muted top-1">EVS</span> <span class="text-blue top1">Sorveteria</span></p>
        <p><span class="text-blue font-22">Pizzaria</span> <span class="text-success top-1"><b>Casa de bolos</b></span></p>
        <p><span class="text-muted"><b>Lanchonete</b></span> <span class="top-1">Salão de beleza</span></p>
        <p><span class="text-success">Churrascaria</span> <span class="text-muted"><b>Tapiocaria</b></span>
          <span class="text-success">Fitness</span>
        </p>
        <p><span>Cervejaria</span> <span class="text-blue font-22"><b>Barbearia </b></span></p>
        <p><span>Pastelaria</span> <span>Churrascaria</span></p>
        <p><span class="text-success">Restaurante</span> <span class="text-blue font-22"> Açai </span></p>
        <p><span class="text-muted"><b>EVS</b></span> <span class="text-success">Churrascaria</span></p>
        <p><span></span>PitDog</p>
      </div>

    </div>
  </div>
</div>

<div   class="container-fluid  main fundo-azul finalizado bloco3"  >
  <div class="container pt-5">
    <div class="row">
      <div class="col">
        <div class="centralizado obrigado"   >
          <div class=" texto">
            <h2 class="text-blue text-center">
              <b>Obrigado!</b>
            </h2>
            <h4 class="mb-5">
              Dentro de instantes um de nossos especialistas irá entrar
              em contato.
            </h4>

            <img src="/lp/cardapio/images/logo-meucardapio.png" class="logo" alt="logo meucardapio">

            <h5 class="text-success "> <b>Cardápio Digital para WhatsApp</b>
              <i class="icone checkin"></i>
            </h5>
          </div>

          <img src="/lp/cardapio/images/img-representante.png" class="representante" alt="consultor">
        </div>
      </div>

    </div>
  </div>
</div>


<div class="container pt-5 bloco4">

  <div class="centralizado col col-xs-4 text-center">
    <h4 class="text-black"><b>Siga-nos</b></h4>

    <div class="social text-center ">
      <a class="link" target="_blank" href="https://www.facebook.com/PromoKitOficial/">
        <img src="/lp/cardapio/images/social-icons-face2.png" alt="facebook">
      </a>
      <a class="link" target="_blank"  href="https://www.instagram.com/promokitoficial/">
        <img src="/lp/cardapio/images/social-icons-insta2.png" alt="instagram">
      </a>
    </div>
    <h4 class="text-black pt-3"><b>Quem somos</b></h4>

    <img src="/lp/cardapio/images/produtos-fibo.jpg" class="centralizado produtos">

  </div>

</div>

<footer class="mt-4">
  <div class="container  ">
    <div class="pt-3 float-left">
      <img src="/lp/cardapio/images/logo-fibo2.png">
      <label>
        2020 Todos direitos reservados
      </label>
    </div>

    <div class="social float-right pt-3">
      <a class="link" target="_blank"  href="https://www.facebook.com/PromoKitOficial/">
        <img src="/lp/cardapio/images/social-icons-face.png">
      </a>
      <a class="link" target="_blank" href="http://wa.me/556283239457">
        <img src="/lp/cardapio/images/social-icons-zap.png">
      </a>

      <a class="link" target="_blank" href="https://www.instagram.com/promokitoficial/">
        <img src="/lp/cardapio/images/social-icons-insta.png">
      </a>
    </div>

  </div>
</footer>

<script type="text/javascript" src="/lp/cardapio/js/cardapiolp.js"></script>
<script src="/assets/js/vanilla-masker.min.js"></script>
<!--Start of Tawk.to Script-->
<script type="text/javascript">
  var Tawk_API=Tawk_API||{}, Tawk_LoadStart=new Date();
  (function(){
    var s1=document.createElement("script"),s0=document.getElementsByTagName("script")[0];
    s1.async=true;
    s1.src='https://embed.tawk.to/5f1204e37258dc118bee740b/default';
    s1.charset='UTF-8';
    s1.setAttribute('crossorigin','*');
    s0.parentNode.insertBefore(s1,s0);
  })();
</script>
<!--End of Tawk.to Script-->
</body>
</html>
