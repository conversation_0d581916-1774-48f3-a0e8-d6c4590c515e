import {MapeadorBasico} from './MapeadorBasico';
import {ProdutoTamanho} from "../domain/templates/ProdutoTamanho";
import {Categoria} from "../domain/delivery/Categoria";
import {Ambiente} from "../service/Ambiente";
import {EnumDisponibilidadeProduto} from "../lib/emun/EnumDisponibilidadeProduto";
import {DTOResumoProdutosContato} from "../domain/relatorios/DTOResumoProdutosContato";
import {AdicionalDeProdutoEscolhaSimples} from "../domain/delivery/AdicionalDeProdutoEscolhaSimples";
import {AdicionalDeProdutoMultiplaEscolha} from "../domain/delivery/AdicionalDeProdutoMultiplaEscolha";
import {OpcaoDeAdicionalDeProduto} from "../domain/delivery/OpcaoDeAdicionalDeProduto";
import {Produto} from "../domain/Produto";
import {AdicionalDeProduto} from "../domain/delivery/AdicionalDeProduto";
import {AdicionalDePedidoEscolhaSimples} from "../domain/delivery/AdicionalDePedidoEscolhaSimples";
import {AdicionalDePedidoMultiplaEscolha} from "../domain/delivery/AdicionalDePedidoMultiplaEscolha";
import {ProdutoPizza} from "../domain/ProdutoPizza";
import {ProdutoTemplate} from "../domain/templates/ProdutoTemplate";
import {MapeadorDeProdutoTemplate} from "./MapeadorDeProdutoTemplate";
import {CacheService} from "../service/CacheService";
import {ProdutoTurno} from "../domain/ProdutoTurno";
import {ProdutoTemplateOpcao} from "../domain/templates/ProdutoTemplateOpcao";
import {Catalogo} from "../domain/catalogo/Catalogo";
import {MapeadorDeAdicionalDeProduto} from "./MapeadorDeAdicionalDeProduto";
import {Estoque} from "../domain/estoque/Estoque";
import {InsumoFactory} from "../domain/estoque/InsumoFactory";
import {ProdutoBrindeFidelidade} from "../domain/ProdutoBrindeFidelidade";
const crypto = require('crypto');

export class MapeadorDeProduto extends MapeadorBasico {
  public static _instance: any;

  public todosCatalogos = false;

  constructor(private catalogo: Catalogo) {
    super('produto', false);
    this.gerenciadorDeMapeamentos = new GerenciadorDeMapeamentosCatalogo(catalogo, this.contexto());
  }

  static Instancia(catalogo: Catalogo): MapeadorDeProduto {
    if( MapeadorDeProduto._instance ) {
      return MapeadorDeProduto._instance;
    }


    return new MapeadorDeProduto(catalogo);
  }

  static carregueCampos(adicional: any): AdicionalDeProduto {
    if( !adicional.dadosJson ) {
      return adicional;
    }

    const objAdicional = JSON.parse(adicional.dadosJson);

    let novoAdicional = null;
    if( objAdicional.entidade === 'produto' ) {
      if (objAdicional.tipo === 'escolha-simples') {
        novoAdicional = new AdicionalDeProdutoEscolhaSimples(objAdicional.nome, objAdicional.obrigatorio,
          objAdicional.opcoesDisponiveis, objAdicional.entidade);
      } else {
        novoAdicional = new AdicionalDeProdutoMultiplaEscolha(objAdicional.nome, objAdicional.obrigatorio,
          objAdicional.opcoesDisponiveis, objAdicional.qtdMinima, objAdicional.qtdMaxima,
          objAdicional.podeRepetirItem, objAdicional.tipoDeCobranca, objAdicional.entidade);
      }
    } else if( objAdicional.entidade === 'pedido' ) {
      if (objAdicional.tipo === 'escolha-simples') {
        novoAdicional = new AdicionalDePedidoEscolhaSimples(objAdicional.nome, objAdicional.obrigatorio,
          objAdicional.opcoesDisponiveis);
      } else {
        novoAdicional = new AdicionalDePedidoMultiplaEscolha(objAdicional.nome, objAdicional.obrigatorio,
          objAdicional.opcoesDisponiveis, objAdicional.qtdMinima, objAdicional.qtdMaxima,
          objAdicional.podeRepetirItem, objAdicional.tipoDeCobranca);
      }
    }

    novoAdicional.id = adicional.id;
    novoAdicional.ordem = objAdicional.ordem;
    novoAdicional.codigoPdv = objAdicional.codigoPdv;
    novoAdicional.classe = objAdicional.classe;
    novoAdicional.campoOrdenar = objAdicional.campoOrdenar;
    if(objAdicional.compartilhado !== undefined ) {
      novoAdicional.compartilhado = objAdicional.compartilhado;
    }
    novoAdicional.produto = objAdicional.produto;
    novoAdicional.codigoIfood = objAdicional.codigoIfood;
    novoAdicional.template = objAdicional.template;

    let listaOpcoes = [];
    for( let opcao of objAdicional.opcoesDisponiveis ) {
      let novaOpcao = new OpcaoDeAdicionalDeProduto(opcao.nome, opcao.valor, opcao.disponivel, opcao.descricao,
        opcao.template, opcao.linkImagem);
      novaOpcao.id = opcao.id;
      novaOpcao.codigoPdv = opcao.codigoPdv;
      novaOpcao.idIfood = opcao.idIfood;

      listaOpcoes.push(novaOpcao);
    }

    novoAdicional.opcoesDisponiveis = listaOpcoes;

    return novoAdicional;
  }

  async selecioneTamanhos(produtos: any): Promise<any> {
    return new Promise(async (resolve) => {
      if(!produtos || !produtos.length)
        return resolve([]);

        let tamanhos = await  this.gerenciadorDeMapeamentos.selecioneVariosAsync(
          this.metodo('selecioneTamanhos'), {ids: produtos.map((item: any) => item.id)});

        produtos.forEach((produto: any) => {
          if(produto.temTamanho())
            produto.tamanhos = tamanhos.filter((tamanho: any) => tamanho.idProduto === produto.id)
        })

      resolve(produtos);

    })
  }

  async selecioneSync(query: any): Promise<any> {
    if (typeof query !== 'object') {
      query = {id: query};
    }

    let comando = this.metodo('selecione');
    /*
    if( (idEmpresa === 663 || idEmpresa === 604) ) {
      comando = this.metodo('selecioneNovo');
    }

     */
    const produto: Produto = await this.gerenciadorDeMapeamentos.selecioneUmAsync(comando, query);

    if(produto && produto.temTamanho()){
      let tamanhos = await  this.gerenciadorDeMapeamentos.selecioneVariosAsync(
        this.metodo('selecioneTamanhos'), {ids: [produto.id]});

      (produto as ProdutoPizza).tamanhos = tamanhos;
    }



    /*
    if( (idEmpresa === 663 || idEmpresa === 604) ) {
      this.carregueDadosExtras(produto);
    }

     */

    this.removaDadosJson(produto);

    return produto;
  }

  async listeAdicionais(produtos: any, idEmpresa: any){
    let query: any = { ids: produtos.map((item: any) => item.id), idEmpresa: idEmpresa}

    let adicionais =  await  new MapeadorDeAdicionalDeProduto().listeDosProdutos( query);

    return adicionais;
  }



  async   listeAsync(query: any): Promise<any> {
// TODO alterar pra pegar pelo id do CATÁLOGO e não da emp  resa
    if(query.nomeProduto){
      let termosDaBusca = query.nomeProduto.split(' ').filter((palavra: string) => palavra.trim().length > 2);

      query.nomeProduto = termosDaBusca.map((item: any) => String(`+${item}*`)).join(' ')

    } else  if(query.termo){
      //so buscar no mysql termos com + de 2 caracteres
      let termosDaBusca = query.termo.split(' ').filter((palavra: string) => palavra.trim().length > 2);

      query.termo = termosDaBusca.map((item: any) => String(`+${item}*`)).join(' ')
    }


    let idCatalogo

    if(this.todosCatalogos)
      idCatalogo = query.idCatalogo

    let ehDeRede = query.rede;
    //const key = 'produtos::' + super.obtenhaIdEmpresaLogada();
    const key = this.obtenhaChaveCatalogo(idCatalogo, ehDeRede) //  'produtos::' + idCatalogo //super.obtenhaIdEmpresaLogada();

    if(!key)
      throw new Error("É preciso um id de catálogo para buscar na cache de produtos")

    let i =  new Date().getTime();
    const hash = crypto.createHash('sha256').update(JSON.stringify(query)).digest('base64');

    if(!query.naoFazerCache){

      const dados: any = await CacheService.obtenhaValorMap(key, hash);
      if(new Date().getTime() - i > 1000)
        console.log('tempo pegar cache: ' + (new Date().getTime() - i))
      i =  new Date().getTime();

      if( dados ) {
        let listaDeProdutos = this.crieProdutosDaCache(dados);
        if(new Date().getTime() - i > 1000)
          console.log('tempo criar cache: ' + (new Date().getTime() - i))

        return listaDeProdutos;
      }
    }

  //  console.log(query)
    let inicio = new Date();
    let comando = this.metodo('selecione');

    const produtos = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(comando, query);

    const  tempo = (new Date().getTime() - inicio.getTime()) / 1000;
    if(tempo > 1)
     console.log('buscou produtos:'   + tempo);

    if(produtos.length){
      let tamanhos = await  this.gerenciadorDeMapeamentos.selecioneVariosAsync(
        this.metodo('selecioneTamanhos'), {ids: produtos.map((item: any) => item.id)});

      produtos.forEach((produto: any) => {
        if(produto.temTamanho())
          produto.tamanhos = tamanhos.filter((tamanho: any) => tamanho.idProduto === produto.id)
      })
    }

    if(query.naoFazerCache) return  produtos;

    CacheService.insiraNoMapJson(key, hash, produtos);

    for( let produto of produtos ) {
      if(this.catalogo && this.catalogo.id === produto.catalogo.id)
        produto.catalogo = this.catalogo
      this.removaDadosJson(produto);
    }

    return produtos;
  }

  carregueDadosExtras(produto: Produto) {
    let lista = [];
    for(let adicional of produto.camposAdicionais) {
      let novoAdicional = MapeadorDeProduto.carregueCampos(adicional);

      lista.push(novoAdicional);
    }

    produto.camposAdicionais = lista;

    if( produto instanceof ProdutoPizza ) {
      let produtoPizza: ProdutoPizza = produto as ProdutoPizza;
      let produtoTemplate: ProdutoTemplate = produtoPizza.template;
      lista = [];

      for (let templateAdicional of produtoTemplate.adicionais) {
        const adicionalDeProduto = produtoPizza.obtenhaCampoAdicional(templateAdicional);
        let objTemplateAdicional = MapeadorDeProdutoTemplate.carregueCampos(templateAdicional);

        if( adicionalDeProduto ) {
          adicionalDeProduto.template = objTemplateAdicional;
          for( let i = 0; i < adicionalDeProduto.opcoesDisponiveis.length; i++ ) {
            const opcaoDisponivel = adicionalDeProduto.opcoesDisponiveis[i];
            if( opcaoDisponivel.template ) {
              let opcao = objTemplateAdicional.opcoes.find(op => op.id === opcaoDisponivel.template.id);

              opcaoDisponivel.codigoPdv = opcao.codigoPdv;
              opcaoDisponivel.nome = opcao.nome;
              opcaoDisponivel.valor = opcao.valor;
              opcaoDisponivel.disponivel = opcao.disponivel;
              adicionalDeProduto.opcoesDisponiveis[i].template = objTemplateAdicional.opcoes[i];
            }
          }
        }
        lista.push(objTemplateAdicional);
      }

      produtoTemplate.adicionais = lista;
    }
  }

  async listeMercado(query: any): Promise<any> {
    const produtos = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(
      this.metodo('selecioneResumoMercado'), query);

    for( let produto of produtos ) {
      this.removaDadosJson(produto);
    }

    return produtos;
  }

  async listeProdutosVitrine(query: any){
    const produtos = await this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneResumoVitrine'), query);

    for( let produto of produtos ) {
      this.removaDadosJson(produto);
    }

    return produtos;
  }

  atualizeFoto(produto: any){
    this.removaCacheProdutos();
   return  this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeFoto'), produto);
  }

  atualizeOrdem(produto: any) {
    this.removaCacheProdutos();
   return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeOrdem'), produto);
  }

  atualizePreco(produto: any){
    this.removaCacheProdutos();
   return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizePreco'), produto);
  }

  atualizeUnidadeMedida(produto: any){
    return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeUnidadeMedida'), produto);
  }

  atualizeDescricao(produto: any){
    this.removaCacheProdutos();
   return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeDescricao'), produto);
  }

  selecioneMenorOrdemDaCategoria(categoria: Categoria) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('menorOrdemDaCategoria'),
      {idCategoria: categoria.id});
  }

  selecioneMaiorOrdemDaCategoria(categoria: Categoria) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('maiorOrdemDaCategoria'),
      {idCategoria: categoria.id});
  }

  atualizeSubirProdutoTopo(dados: any): Promise<any>{
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('subirTopoCategoria'), dados);
  }

  atualizeDescerProdutoFinal(dados: any): Promise<any>{
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('descerFinalCategoria'), dados);
  }

  atualizeULtimaOrdem(produto: any){
    return  this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeULtimaOrdem'), produto);
  }

  recalculeOrdens(catalogo: Catalogo, porNome: boolean = false){
    let metodo = 'recalculeOrdens';

    const versaoMysql = Ambiente.Instance.config.versao_mysql.trim();

    if( versaoMysql === '5.5' ) {
      metodo = 'recalculeOrdens55';
    }

    let dados: any = { idCatalogo: catalogo.id};

    if(porNome) dados.porNome = true;

   return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo(metodo), dados);
  }

  atualizePontosFidelidade(produto: any): Promise<void>{
    return   this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizePontosFidelidade'), produto);
  }

  atualizePontosFidelidadeTamanho(tamanho: any): Promise<void>{
    return   this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizePontosFidelidadeTamanho'), tamanho);
  }

  atualizeDisponibilidade(produto: any): Promise<void>{
   produto.temEstoque = produto.disponibilidade !== EnumDisponibilidadeProduto.NaoDisponivel

   return this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeDisponibilidade'), produto);
  }

  atualizeDisponibilidadeCatalogo(produto: any) {
    this.removaCacheProdutos();
    return this.gerenciadorDeMapeamentos.insiraAsync( this.metodo('atualizeDisponibilidadeCatalogo'), produto );
  }


  atualizeDisponibilidades(produtos: any){
    return new Promise<void>((resolve) => {
      if(!produtos.length) return resolve();

      let dados = {
          ids: produtos.map( (produto: any) => produto.id),
          disponibilidade: produtos[0].disponibilidade,
          temEstoque: produtos[0].temEstoque
      }

      this.gerenciadorDeMapeamentos.atualize(this.metodo('atualizeDisponibilidades'), dados, () => {
        resolve();
      });

    })
  }



  insiraDisponibilidade(produto: any, disponibilidade: any){
    return new Promise<void>(async (resolve, reject) => {
      this.gerenciadorDeMapeamentos.insira(this.metodo('insiraDisponibilidade'),
        { produto: produto, disponibilidade: disponibilidade }, () => {
        resolve();
      })
    })
  }

  async insiraDisponibilidades(produto: any ){
    if(produto.id)
      await this.removaDisponibiliadesProduto(produto);

    for(let i = 0; i < produto.disponibilidades.length; i++)
      await this.insiraDisponibilidade(produto, produto.disponibilidades[i])

  }

  removaDisponibiliadesProduto(produto: any){
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('removaDisponibiliadesProduto'), produto, () => {
        resolve();
      })
    })
  }

  async atualizeDisponibilidadeTamanho(tamanhos: Array<ProdutoTamanho>) {
    return new Promise<void>(async (resolve, reject) => {
      for(let i = 0; i < tamanhos.length; i++)
          await   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeDisponibilidadeTamanho'), tamanhos[i])
      resolve();
    })
  }

  async salveTamanhosProdutos(produto: any, tamanhos: Array<ProdutoTamanho> ){
    return new Promise<void>(async (resolve, reject) => {
      for(let i = 0; i < tamanhos.length; i++){
        if(tamanhos[i].id){
          await   this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeTamanho'), tamanhos[i])
        } else {
          tamanhos[i].produto = {id: produto.id};
          await   this.gerenciadorDeMapeamentos.insiraAsync(this.metodo('insiraTamanho'), tamanhos[i]);
        }
      }
      resolve();
    })
  }

  atualizePrecoTamanho(tamanho: any){
    this.removaCacheProdutos();
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizePrecoTamanho'), tamanho);
  }


  removaProduto (produto: any) {
    return new Promise<void>((resolve, reject) => {
      this.removaCacheProdutos();
      this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), produto, () => {
        resolve();
      })
    })
  }

  async removaTodosProdutos(){
    await this.removaCacheProdutos();
    return  this.gerenciadorDeMapeamentos.atualize(this.metodo('removaTodosProdutos'), {} );
  }

  obtenhaQtdeProdutos(dados: any) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneQuantidade'), dados);
  }

  obtenhaProdutoAcima(dados: any) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneProdutoAcima'), dados);
  }

  obtenhaProdutoAbaixo(dados: any) {
    return this.gerenciadorDeMapeamentos.selecioneUmAsync(this.metodo('selecioneProdutoAbaixo'), dados);
  }

  obtenhaResumoProdutosContato(contato: any, empresa: any): Promise<DTOResumoProdutosContato[]> {
    let dados = {
      idContato: contato.id,
      idCatalogo: empresa.catalogo.id,
      horarioInicio: contato.horarioInicio,
      horarioFim: contato.horarioFim

    }

    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneResumoProdutosContato'), dados);
  }

  listeProdutosIndisponiveisEcletica(){
    return new Promise((resolve) => {
      this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneIndisponiveisEcletica'), {}).then((produtos: any[]) => {
        if(!produtos)
          return resolve([]);

        this.selecioneTamanhos(produtos.filter((produto: any) => produto.temTamanho())).then(() => {
          resolve(produtos)
        })
      })
    })

  }

  private removaDadosJson(produto: any) {
    if(!produto) return;
    for (let adicional of produto.camposAdicionais) {
      delete adicional.dadosJson;
      if (adicional.template) {
        delete adicional.template.dadosJson;
      }
    }

    if (produto instanceof ProdutoPizza) {
      let produtoPizza: ProdutoPizza = produto as ProdutoPizza;
      let produtoTemplate: ProdutoTemplate = produtoPizza.template;

      if( produtoTemplate ) {
        for (let templateAdicional of produtoTemplate.adicionais) {
          delete templateAdicional.dadosJson;
        }
      }
    }
  }

  insiraGraph(obj: any) {
    this.removaCacheProdutos();

    return super.insiraSync(obj);
  }

  insiraSync(obj: any): any {
    return super.insiraSync(obj);
  }

  atualizeSync(obj: any) {
    return super.atualizeSync(obj);
  }

  private obtenhaChaveCatalogo(idCatalogo: any = null, rede: any = null) {
    let id = idCatalogo

    if(!id)
      id =
        !rede && super.possuiEmpresaLogada() ? super.obtenhaIdEmpresaLogada() : this.catalogo.id

    let prefixo = 'produtos::'

    if(rede)
      prefixo += 'rede::'

    if(id)
      return prefixo +  id



    return null
  }


  public async removaCacheProdutos(todas: boolean = true) {
    return new Promise( async (resolve, reject) => {
      if(!todas) {
        await this.removaCacheProdutosDaEmpresa({id: this.obtenhaIdEmpresaLogada()})
        return resolve(true);
      }

      if(!this.catalogo || !this.catalogo.id) return resolve(false)

        this.gerenciadorDeMapeamentos.selecioneVariosAsync("empresa.selecioneEmpresasQueUsamCatalogo",
          {idCatalogo: this.catalogo.id}, false).then(async (empresas: any) => {
          for(let i = 0; i < empresas.length; i++) {
            let empresa = empresas[i]
            console.log('*removendo cache de produtos da empresa  ' + empresa.id + '*')
            await this.removaCacheProdutosDaEmpresa(empresa)
          }

          if(this.catalogo.compartilhado) {
            let chaveRede = this.obtenhaChaveCatalogo(this.catalogo.id, true)
            console.log('*removendo cache do catalogo compartilhado ' + this.catalogo.id + '*')
            await CacheService.removaCachePrefixo(chaveRede);
          }


          resolve(true);
        });

    });
  }

  public async removaCacheProdutosDaEmpresa(empresa: any) {
    let chaveCatalogo = this.obtenhaChaveCatalogo(empresa.id)

    if( chaveCatalogo ) {
      await CacheService.removaCachePrefixo(chaveCatalogo);

    }
  }

  private crieProdutosDaCache(dados: any) {
    const produtos = [];
    for( let p of dados ) {
      let objProduto: any = null;
      if( p.tipo === 'pizza' ) {
        objProduto = new ProdutoPizza();
      } else if( p.tipo === 'brindefidelidade') {
        objProduto = new ProdutoBrindeFidelidade();
      } else {
        objProduto = new Produto();
      }

      Object.assign(objProduto, p);
      if( p.categoria ) {
        const categoria = new Categoria(p.categoria.id, p.categoria.nome, null,
          p.categoria.impressoras, p.categoria.codigoPdv, p.categoria.nivel);
        Object.assign(categoria, p.categoria);
        objProduto.categoria = categoria;
      }

      if( p.tipo === 'pizza' ) {
        const listaDeTamanhos = [];
        for (let tamanho of objProduto.tamanhos) {
          let produtoTamanho = new ProdutoTamanho(tamanho.id, tamanho.preco, tamanho.template, tamanho.disponivel);

          Object.assign(produtoTamanho, tamanho);
          listaDeTamanhos.push(produtoTamanho);
        }

        objProduto.tamanhos = listaDeTamanhos;
      }

      if( p.turnos ){ // quando nao tiver mais produtos na antigos cache, remover esse if
        const listaDeTurnos = [];
        for( let turno of p.turnos ) {
          const objTurno = new ProdutoTurno(undefined, turno.horaInicio, turno.horaFim);

          objTurno.id = turno.id;
          listaDeTurnos.push(objTurno);
        }
        objProduto.turnos = listaDeTurnos;
      }

      const adicionais = [];
      for( let adic of p.camposAdicionais ) {
        const opcoes: any = [];
        if( adic.opcoesDisponiveis ) {
          for (let opcao of adic.opcoesDisponiveis) {
            let template = opcao.template;
            let objTemplate = null;
            if (template) {
              objTemplate = new ProdutoTemplateOpcao(template.id, template.nome, template.valor, template.descricao,
                template.tamanho);
              Object.assign(objTemplate, template);
            }

            const objOpcao = new OpcaoDeAdicionalDeProduto(opcao.nome, opcao.valor, opcao.disponivel,
              opcao.descricao, objTemplate, opcao.linkImagem);

            Object.assign(objOpcao, opcao);

            if(opcao.insumo )
              objOpcao.insumo = InsumoFactory.novaDaCache(opcao.insumo)


            opcoes.push(objOpcao);
          }
        }

        let adicional = null;
        if( adic.classe === 'escolha-simples-produto' ) {
          adicional = new AdicionalDeProdutoEscolhaSimples(adic.nome, adic.obrigatorio, opcoes, adic.entidade);
        } else if( adic.classe === 'multipla-escolha-produto' ) {
          adicional = new AdicionalDeProdutoMultiplaEscolha(adic.nome, adic.obrigatorio, opcoes, adic.qtdMinima,
            adic.qtdMaxima, adic.podeRepetirItem, adic.tipoDeCobranca, adic.entidade);
        } else if( adic.classe === 'escolha-simples-pedido' ) {
          adicional = new AdicionalDePedidoEscolhaSimples(adic.nome, adic.obrigatorio, opcoes);
        } else if( adic.classe === 'multipla-escolha-produto' ) {
          adicional = new AdicionalDePedidoMultiplaEscolha(adic.nome, adic.obrigatorio, opcoes, adic.qtdMinima,
            adic.qtdMaxima, adic.podeRepetirItem, adic.tipoDeCobranca);
        }

        if( !adic || !adicional ) {
          console.log('produto: ' + p.nome + ' -> ' + p.id);

          continue;
        }

        Object.assign(adicional, adic);
        adicional.opcoesDisponiveis = opcoes;

        adicionais.push(adicional);
      }
      objProduto.camposAdicionais = adicionais;

      if(p.insumo )
        objProduto.insumo =  InsumoFactory.novaDaCache(p.insumo)

      if(p.estoque)
        objProduto.estoque = Estoque.crieDaCache(p.estoque)

      if(this.catalogo && this.catalogo.id === objProduto.catalogo.id)
        objProduto.catalogo = this.catalogo

      produtos.push(objProduto);
    }

    return produtos;
  }

  buscarTodosCatalogos() {
    this.gerenciadorDeMapeamentos.todosCatalogos = true
  }

  listeDisponiblidadeMigrar(){
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneDisponibilidadeMigrar'), {});
  }

  async listeProdutosReordenarAdicionais() {
    this.desativeMultiCliente();
    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(this.metodo('selecioneReordenarAdicionais'), {})
  }

  async reordeneAdicionaisProduto(id: number){
    const params: any =  {idProduto: id, idCatalogo: this.catalogo.id};

    await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('reordeneAdicionais'), params)

    await this.removaCacheProdutos();
  }
}

export class GerenciadorDeMapeamentosCatalogo {
  private gerenciadorDeMapeamentos: any;
  private contexto: any;
  public todosCatalogos = false;
  catalogo: Catalogo;
  constructor(catalogo: Catalogo, contexto: any) {
    this.catalogo = catalogo;
    this.contexto = contexto;

    // @ts-ignore
    this.gerenciadorDeMapeamentos = global['sessionFactory'];
  }

  selecioneUm(nomeCompleto: any, dados: any, callback: any) {
    if(!this.todosCatalogos)
      dados.idCatalogo = this.catalogo.id;

    console.log(dados)
    this.gerenciadorDeMapeamentos.selecioneUm(nomeCompleto, dados, callback, false);
  }

  selecioneVarios(nomeCompleto: any, dados: any, callback: any) {
    const no = this.gerenciadorDeMapeamentos.obtenhaNo(nomeCompleto);
    const textoConsulta = no.filhos[0].texto;

    if(!this.todosCatalogos)
      dados.idCatalogo = this.catalogo.id;

    this.gerenciadorDeMapeamentos.selecioneVarios(nomeCompleto, dados, callback, false);
  }

  insira(nomeCompleto: any, objeto: any, callback: any) {
    if(!this.todosCatalogos)
      if(!objeto.catalogo) objeto.catalogo = {id: this.catalogo.id}

    return this.gerenciadorDeMapeamentos.insira(nomeCompleto, objeto, callback, false);
  }

  insiraGraph(nomeCompleto: any, objeto: any, callback: any) {
    if(!this.todosCatalogos)
      if(!objeto.catalogo) objeto.catalogo = {id: this.catalogo.id}

    return this.gerenciadorDeMapeamentos.insiraGraph(nomeCompleto, objeto, callback, false);
  }

  insiraAsync(nomeCompleto: any, objeto: any, callback: any) {
    if(!this.todosCatalogos)
      if(!objeto.catalogo) objeto.catalogo = {id: this.catalogo.id}

    return this.gerenciadorDeMapeamentos.insiraAsync(nomeCompleto, objeto, callback, false);
  }

  atualize(nomeCompleto: any, objeto: any, callback: any) {
    if(!this.todosCatalogos)
      if(!objeto.catalogo) objeto.catalogo = {id: this.catalogo.id}

    return this.gerenciadorDeMapeamentos.atualize(nomeCompleto, objeto, callback, false);
  }

  atualizeAsync(nomeCompleto: any, objeto: any) {
    if(!this.todosCatalogos)
      if(!objeto.catalogo) objeto.catalogo = {id: this.catalogo.id}

    return this.gerenciadorDeMapeamentos.atualizeAsync(nomeCompleto, objeto, false);
  }

  selecioneUmAsync(nomeCompleto: any, dados: any) {
    if(!this.todosCatalogos)
      if(!dados.idCatalogo) dados.idCatalogo = this.catalogo.id

    return this.gerenciadorDeMapeamentos.selecioneUmAsync(nomeCompleto, dados, false);
  }

  selecioneVariosAsync(nomeCompleto: any, dados: any) {
    const no = this.gerenciadorDeMapeamentos.obtenhaNo(nomeCompleto);

    if(!this.todosCatalogos)
      if (!dados.idCatalogo) {
      dados.idCatalogo = this.catalogo.id;
    }

    return this.gerenciadorDeMapeamentos.selecioneVariosAsync(nomeCompleto, dados, false);
  }
}
