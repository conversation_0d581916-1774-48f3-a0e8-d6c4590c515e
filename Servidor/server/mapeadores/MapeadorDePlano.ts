import {MapeadorBasico} from './MapeadorBasico';

export class MapeadorDePlano extends MapeadorBasico {
  constructor() {
    super('plano');
  }

  salveRegrasExtras(dados: any) {
    this.desativeMultiCliente()

    if(dados.regrasExtras && dados.regrasExtras.length > 0 ) {
      dados.regrasExtras.forEach((regra: any) => {
        regra.idPlano = dados.id;
      })
      console.log(dados);
      return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('salveRegrasExtras'), dados);
    }
    else
      return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaRegrasExtras'), dados);

  }

  atualizeAtivo(plano: any){
    return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtivo'), plano);
  }


  foiUtilizado(query: any): Promise<boolean> {
    this.desativeMultiCliente()
    return new Promise((resolve, reject) => {
      this.gerenciadorDeMapeamentos.selecioneUm(this.metodo('foiUtilizado'), query, function(total: number) {
        resolve(total && total > 0)
      });
    });
  }


  removaPlano (plano: any) {
    return new Promise<void>((resolve, reject) => {
      this.gerenciadorDeMapeamentos.atualize(this.metodo('remova'), plano, () => {
        resolve();
      })
    })
  }

}
