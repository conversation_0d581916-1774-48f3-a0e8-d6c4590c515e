import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDeMeucardapioPay extends MapeadorBasico {
    constructor() {
        super('meucardapioPay');
    }

    async atualizeFormaPagamentoAtiva(obj: any) {
      return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeFormaPagamentoAtiva'), obj);
    }

    async atualizeAtivacaoErro(obj: any) {
      return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeAtivacaoErro'), obj);
    }

    async atualizeLojaAtivou(obj: any) {
      return this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeLojaAtivou'), obj);
    }
}
