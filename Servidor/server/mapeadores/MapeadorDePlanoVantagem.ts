import {MapeadorBasico} from "./MapeadorBasico";

export class MapeadorDePlanoVantagem extends MapeadorBasico {
  constructor() {
    super('planoVantagem', false);
  }

  async removaPlanoVantagem(planoEmpresarial: any, vantagemExcluir: any) {
    await this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('removaPlanoVantagem'), vantagemExcluir);
    return  this.gerenciadorDeMapeamentos.atualizeAsync(this.metodo('atualizeOrdemAposRemocao'),
      {idPlano: planoEmpresarial.id, ordem: vantagemExcluir.ordem});
  }

}
