// 
// plugins-rtl.scss
//


// Nice select

.nice-select {
    float: right;
}


// Nestable-list

.dd-item {
    >button {
        float: right;
    }

    .dd-list {
        padding-right: 30px;
        padding-left: 0;
    }
}

.dd-list {
    .dd3-handle {
        float: right;
    }
}


// Select 2

.select2-container {
    .select2-selection--single {
        .select2-selection__rendered {
            padding-right: 12px;
        }

        .select2-selection__arrow {
            left: 3px;
            right: auto;
        }
    }

    .select2-selection--multiple {
        .select2-selection__choice {
            float: right;
            margin-left: 5px;
            margin-right: 0;
        }
    }

    .select2-search--inline {
        float: right;
    }
}


// Multiple select

.ms-container {
    .ms-optgroup-label {
        padding: 5px 5px 0px 0;
    }
}


// Bootstrap select

.bootstrap-select {
    .dropdown-toggle {
        &:before {
            float: left;
        }

        .filter-option {
            text-align: right;
        }

        .filter-option-inner {
            padding-right: 0;
            padding-left: inherit;
        }
    }
}


// Parsley

.parsley-errors-list {
    >li {
        padding-left: 0;
        padding-right: 20px;

        &:before {
            left: auto;
            right: 2px;
        }
    }
}


// Quilljs

.ql-snow .ql-picker:not(.ql-color-picker):not(.ql-icon-picker) svg {
    left: 0;
    right: auto;
}

.ql-editor {
    direction: rtl;
    text-align: right;
}


// X-ediatable 

.editable-buttons {
    margin-left: 0;
    margin-right: 7px;

    .editable-cancel {
        margin-left: 0;
        margin-right: 7px;
    }
}


// Foo table

.footable.breakpoint>tbody>tr>td>span.footable-toggle {
    padding-left: 5px;
    padding-right: 0;
}