function fazerCadastro() {
  window.scrollTo(0,document.body.scrollHeight);
  document.getElementById("nome").focus();
}

function solicitarContato() {
  window.location.href = "http://wa.me/55";
}

function unmask(val) {
  return val ? val.replace(/\D+/g, "") : val;
}


window.onload = function(e){
  var inputs = document.querySelectorAll("input, select, textarea");
  var form = document.querySelector("form");
  var $btnEnviar = document.getElementsByClassName("botaoEnviar")[0];
  var $nomeInput =  document.getElementById("nome");
  var $tipoDeNegocio =  document.getElementById("tipoDeNegocio");
  var $cboTipoDeNegocio =  document.getElementById("cboTipoDeNegocio");
  var $txtTelefone = document.getElementById("telefone");
  var $body =  document.getElementsByTagName("body")[0];
  var $msgErro = document.getElementById("msgErro");

  VMasker($txtTelefone).maskPattern("(99) 9-9999-99999");

  inputs.forEach(input => {
    input.addEventListener(
      "invalid",
      event => {
        if(!input.valid)  input.classList.add("ng-invalid");
        else input.classList.remove("ng-invalid");

      },
      false
    );

    input.addEventListener("focusout", event => {
      if(input.checkValidity()) input.classList.remove("ng-invalid");
    });

    input.addEventListener("keyup", event => {
      if(input.checkValidity()) input.classList.remove("ng-invalid");
    })
  });

  form.addEventListener("submit", event => {
    form.classList.add('was-validated');
    event.preventDefault();
    event.stopPropagation();
    $msgErro.innerHTML = null;
    let valido = true;
    for(let i =0 ; i< inputs.length;i++){
       if(!inputs[i].checkValidity()) {
         valido = false;
         inputs[i].classList.add("ng-invalid");
       }
    }

    if(!valido) {
      $msgErro.innerHTML = 'Verifique os campos obrigatórios'
      return;
    }

    $tipoDeNegocio.value = $cboTipoDeNegocio.options[$cboTipoDeNegocio.selectedIndex].text;

    const formData = new FormData(form);


    xhr = new XMLHttpRequest();
    xhr.onload = function() {
      $btnEnviar.disabled=false;
      let resposta = JSON.parse(xhr.response);

      if (xhr.status === 200 && resposta.sucesso) {
        window.location.href = window.location.href + 'obrigado';
        /*
        $body.classList.add('finalizado');
        $msgErro.classList.remove('visivel');

        inputs.forEach(input => {
          input.style.display = "none";
        });

        let elementos = document.querySelectorAll('.explicacao')

        elementos.forEach(elemento => {
          elemento.style.display = "none";
        });

        window.scrollTo(0,0);
*/
      }
      else   {
        $msgErro.innerHTML = resposta.erro || 'Não foi possível salvar dados da empresa nesse momento!'

      }
    };

    xhr.onerror = function(){
      $btnEnviar.disabled=false;
    }

    xhr.open('POST', '/prospects/meucardapio');;
    xhr.send(formData);

    $btnEnviar.disabled=true;

  });

}

