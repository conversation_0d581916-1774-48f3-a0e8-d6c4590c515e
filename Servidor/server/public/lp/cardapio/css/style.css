body{
  background: #fff;
  margin:0;
  padding:0;
}

header{
  padding: 0px !important;
  background: #fff;
  height: 50px
}

.main{

}

.logo-fibo-topo{
  position: relative;top: -2px;
}

.fundo-azul{
  background-color: #d7f3ff;;
}

.icone{
  display: inline-block;
}
.checkin{
  height: 20px;
  width: 30px;
  background-image: url('/lp/cardapio/images/check-icon.png') ;
  background-repeat: no-repeat;
  top: 5px;
  position: relative;
}

.icone-zap-cinza{
  height: 30px;
  width: 30px;
  background-image: url('/lp/cardapio/images/icone-zap-cinza.png');
  background-repeat: no-repeat;
  top: 10px;
  position: relative;

}

.text-black{
  color: #000;
}

.centralizado{
  margin: 0 auto;
  display: block;
}


footer{
  height: 75px;
  background: #f1f1f1;
}

.text-muted{
  color: #74768f !important;
}

.text-success{
  color: #358a23 !important;;
}

.text-blue{
  color: #2c3590 !important;;
}

h4{
  line-height: 1.5rem;
}

.form-control  {
  border-top: 0;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  color: rgba(0,0,0,.88);
}

.form-control:not(select) {
  padding: 1.5rem 1rem;
}

.segmentos-palavras{
  width: 300px;
  text-align: center;
}
.segmentos-palavras p{
  padding-bottom: 0;
  margin-bottom: 0;
}

.font-g{
  font-size: 32px;
  line-height: 32px;
}

.top1{
  position: relative;
  top: 2px;
}

.top-1{
  position: relative;
  top: -2px;
}

.finalizado{
  height: 410px;
  margin-bottom: 100px;
}

.obrigado{
  width: 500px
}

.obrigado .texto{
  width: 300px;
}

.obrigado .logo{
  height: 75px;
}

body .bloco3, body .bloco4{
  display: none;
}


body.finalizado .bloco1, body.finalizado .bloco2{
  display: none;
}

body.finalizado .bloco3, body.finalizado .bloco4{
  display: block;
}

.form-check {
  width: 50%;
  float: left;
  margin-top: 8px;
}

.form-check-input {
  height: 16px;
  width: 16px;
  top: -4px;
  margin-left: -1.3rem;

}
.was-validated .form-check-input:valid~.form-check-label{
  color: inherit !important;
}

.input-group-prepend .input-group-text{
  border-left: none;
  border-top: none;
  border-radius: 0;
}

.obrigado .texto{
  float: left;
}

.obrigado .img{
  float: right;
}

@media (max-width: 990px) {
  h4 {
    font-size: 0.8rem;
    line-height: 1.2rem;
  }
  .h3, h3 {
    font-size: 1.0rem;
    line-height: 1.2rem;
  }

  .icone-zap-cinza {
    margin-left: 15px;
  }
  .logo-fibo-topo{
    margin-right: 10px;
  }
  .checkin {
    top: 7px;
    background-size: 22px;
  }


  .bloco1, .bloco2 h4,  .bloco2 h3, .bloco3{
    text-align: center;

  }
  .bloco1 .col-xs-12{
    padding-bottom: 50px;
  }


  .bloco1 .img01{
    width: 100%;
  }

  .bloco1 .logo{
    height: 60px;
    margin: 0 auto;
  }

  .bloco2{
    height: 1360px;
  }

  .bloco2 .coluna-segmentos{
    position: absolute;
    top: -30px;
  }

  .bloco2 .coluna-cadastro{
    position: absolute;
    top:290px
  }
  .form-check {
    width: 100%;
    margin-top: 15px;
  }

  .obrigado .logo{
    margin: 0 auto;
  }


  .obrigado .texto{
    float: none;
  }

  .obrigado .img{
    float: none;
  }

  header .btn{
    padding: .25rem .7rem;
    margin-left: 15px;
  }

  footer {
    height: 100px;
  }
  footer  label{
    display: block;
    margin-top: 10px;
  }
  .botaoEnviar{
    display: block;
    margin: 0 auto;
    top: 20px;
    position: relative;
  }

  .finalizado {
    height: 560px;
    margin-bottom: 170px;
  }

  img.produtos{
    width: 100%;
  }

  img.representante{
    position: relative;
    left: 50px;
    display: block;
    top: 15px;
  }
}
