import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {Produto} from "../Produto";
import {ItemPedido} from "./ItemPedido";
import {MapeadorDeValorDeAdicionaisMultiplaEscolha} from "../../mapeadores/MapeadorDeValorDeAdicionaisMultiplaEscolha";
import {ListaOpcoesEscolhidas} from "./ListaOpcoesEscolhidas";

export class ValorDeAdicionaisMultiplaEscolha extends ObjetoPersistente{
  lista0: ListaOpcoesEscolhidas;
  lista1: ListaOpcoesEscolhidas;
  lista2: ListaOpcoesEscolhidas;
  lista3: ListaOpcoesEscolhidas;
  lista4: ListaOpcoesEscolhidas;
  lista5: ListaOpcoesEscolhidas;
  lista6: ListaOpcoesEscolhidas;
  lista7: ListaOpcoesEscolhidas;
  lista8: ListaOpcoesEscolhidas;
  lista9: ListaOpcoesEscolhidas;

  produto: Produto;
  item: ItemPedido;

  mapeador(): MapeadorBasico {
    return new MapeadorDeValorDeAdicionaisMultiplaEscolha();
  }

  public convertaParaTela(): any {
    let retorno: any = {}


    for(let campo in this) {
      if(campo.startsWith('lista') && this[campo]) {
        let opcoesSelecionadas: any = {
          totalSelecionado: 0,
          valorTotal: 0
        }

        retorno[campo] = opcoesSelecionadas

        let listaDeValores: any = this[campo]
        opcoesSelecionadas.tipoDeCobranca = listaDeValores.tipoDeCobranca
        let i = 0;
        for(let valorDeOpcao of listaDeValores.opcoes) {
          opcoesSelecionadas.valorTotal += valorDeOpcao.opcao.valor;
          opcoesSelecionadas.totalSelecionado += valorDeOpcao.qtde;
          opcoesSelecionadas["opcao_" + valorDeOpcao.opcao.id] = {
            'opcao': valorDeOpcao.opcao,
            'selecionada': true,
            'qtde': valorDeOpcao.qtde,
            'valorTotal': valorDeOpcao.opcao.valor * valorDeOpcao.qtde

          }
          i++;
        }
      }

    }

    return retorno;
  }

}
