import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDeMeucardapioPay} from "../../mapeadores/MapeadorDeMeucardapioPay";
import {EnumMeioDePagamento} from "../delivery/EnumMeioDePagamento";
import {FormaDePagamento} from "../delivery/FormaDePagamento";
import {ConfigMeioDePagamento} from "../delivery/ConfigMeioDePagamento";
import {MapeadorDeConfigMeioDePagamento} from "../../mapeadores/MapeadorDeConfigMeioDePagamento";
import {MapeadorDeFormaDePagamento} from "../../mapeadores/MapeadorDeFormaDePagamento";
import {MapeadorDeEmpresa} from "../../mapeadores/MapeadorDeEmpresa";
import {ContratoMeucardapioPay} from "./ContratoMeucardapioPay";

export enum EnumStatusAtivacaoMeucardapioPay{
  Processando =  "P",
  EmAnalise =  "0",
  Ativo  = "1",
  Inativo  = "2",
  Suspenso = "3"
}

export enum TunaServiceFluxoNome {
  TunaSplitparcartoV2 = "Tuna Split para cartão V2",
  TunaSplitparaPixV4 = "Tuna Split para PIX V4",
}


export class MeucardapioPay extends ObjetoPersistente{
  public dataAtivacao: Date;
  public pixLiberado: boolean;
  public cartaoCreditoLiberado: boolean;
  public idLoja: string;
  public ativacaoStatus: EnumStatusAtivacaoMeucardapioPay;
  public ativacaoErro: string;
  public merchantId: string;
  public lojaAtivou: boolean;

  constructor(public empresa: any,  public contratoMeucardapioPay: ContratoMeucardapioPay,
              public pixTaxaMinima: number = 0.00) {
    super()
    if(this.empresa) this.idLoja   = empresa.dominio;
    this.lojaAtivou = false;

  }

  jaVinculou(){
    return  this.ativacaoStatus != null
  }

  ativeNaEmpresa(empresa: any){
    return new Promise( async (resolve, reject) => {
      this. mapeador().transacao(async (conexao: any, commit: any) => {
        this.empresa = empresa;
        this.dataAtivacao = new Date();
        this.ativacaoStatus = EnumStatusAtivacaoMeucardapioPay.Ativo;
        await this.mapeador().atualizeSync(this);
        await new MapeadorDeEmpresa().removaDasCaches(empresa);
        commit(() => {   resolve(''); })
      })
    })
  }

  atualizePixLiberado(empresa: any){
    return new Promise( async (resolve, reject) => {
      this. mapeador().transacao(async (conexao: any, commit: any) => {
        this.pixLiberado  = true;
        await this.mapeador().atualizeFormaPagamentoAtiva(this);
        await this.sincronizeFormasPagamentosAtivas(empresa);
        await new MapeadorDeEmpresa().removaDasCaches(empresa);
        commit(() => {   resolve(''); })
      })
    })
  }


  atualizeCartaoLiberado(empresa: any){
    return new Promise( async (resolve, reject) => {
      this. mapeador().transacao(async (conexao: any, commit: any) => {
        this.cartaoCreditoLiberado  = true;
        await this.mapeador().atualizeFormaPagamentoAtiva(this);
        await this.sincronizeFormasPagamentosAtivas(empresa);
        await new MapeadorDeEmpresa().removaDasCaches(empresa);
        commit(() => {   resolve(''); })
      })
    })
  }

  async atualizeCriouEmpresa(resposta: any, lojaAtivou: boolean){
    //merchant.merchantId
    this.merchantId =  resposta.merchantId.toString();
    this.ativacaoStatus = EnumStatusAtivacaoMeucardapioPay.Processando;
    this.lojaAtivou = lojaAtivou;
    this.dataAtivacao = new Date();
    await this.mapeador().atualizeSync(this);
    delete this.empresa;
  }

  async registreRetornoErro(ativacaoErro: any){
    this.ativacaoErro =  ativacaoErro;
    await this.mapeador().atualizeAtivacaoErro(this);
  }

  async maqueLojaAtivou(){
    this.lojaAtivou = true;
    await this.mapeador().atualizeLojaAtivou(this);
  }


  async recebeuRetornoTuna(merchant: any, empresa: any){
    console.log(JSON.stringify(merchant));

    let servicosAtivos = merchant.serviceRegistrations.filter((item: any) => item.serviceStatus === 'Authorized')

    for(let i = 0; i < servicosAtivos.length; i++){
      const idServico: any  = servicosAtivos[i].serviceId;

      if(idServico === TunaServiceFluxoNome.TunaSplitparaPixV4){
        await empresa.meucardapioPay.atualizePixLiberado(empresa);
      } else if(idServico === TunaServiceFluxoNome.TunaSplitparcartoV2 ){
        await empresa.meucardapioPay.atualizeCartaoLiberado(empresa);
      }
    }

    if(merchant.statusId){
      if(this.ativacaoStatus !== merchant.statusId){
        await  this.atualizeStatus(merchant.statusId, empresa);
      } else {
        console.log('status já procesado, sera ignorado: ' + merchant.statusId)
      }
    } else {
      console.warn('Nenhum status recebido: ' , merchant)
    }

    delete this.empresa;
  }

  private async  atualizeStatus(status: any, empresa: any){
    if(status  === EnumStatusAtivacaoMeucardapioPay.Ativo ){
      await this.ativeNaEmpresa(empresa)
    } else {
      this.ativacaoStatus = status;
      if(this.dataAtivacao)
         this.dataAtivacao = new Date(this.dataAtivacao);

      await this.mapeador().atualizeSync(this);
      await new MapeadorDeEmpresa().removaDasCaches(empresa);
    }
  }

  estaAtiva(){
    return this.ativacaoStatus === EnumStatusAtivacaoMeucardapioPay.Ativo
  }

  async sincronizeFormasPagamentosAtivas(empresa: any){
    if(!this.lojaAtivou){
      console.log('Cadastro ainda nao ativado pelo lojista')
      return;
    }

    let formaDePagamentoPix: any, formaDePagamentoCartao: any,  meioPagamento = EnumMeioDePagamento.TunaPay;

    let formasPagamento: any = [];

    if(this.pixLiberado){
      formaDePagamentoPix = empresa.formasDePagamento.find((item: any) => item.tunapay() && item.pix);
      formasPagamento.push( 'Pix Online MCI');
    }

    if(this.cartaoCreditoLiberado){
      formaDePagamentoCartao = empresa.formasDePagamento.find((item: any) => item.tunapay() && !item.pix);
      formasPagamento.push( 'Cartão Online MCI');
    }

    for(let i = 0; i < formasPagamento.length; i++){
      let nome = formasPagamento[i];
      const ehPix = nome.indexOf('Pix') >= 0;
      let formaDePagamento: any;

      if(ehPix && formaDePagamentoPix)
        formaDePagamento = formaDePagamentoPix

      if(!ehPix && formaDePagamentoCartao)
        formaDePagamento = formaDePagamentoCartao

      if(!formaDePagamento){
        formaDePagamento = FormaDePagamento.nova(nome, nome, ehPix, empresa);

        formaDePagamento.online = true;
        formaDePagamento.pix = ehPix

        let config = new ConfigMeioDePagamento();

        config.meioDePagamento =  meioPagamento

        formaDePagamento.configMeioDePagamento = config

        await new MapeadorDeConfigMeioDePagamento().insiraConfigMeioDePagamento(formaDePagamento.configMeioDePagamento);
        await  new MapeadorDeFormaDePagamento().insiraSync(formaDePagamento);

        empresa.formasDePagamento.push(formaDePagamento)
      }
    }
  }

  mapeador(): MapeadorDeMeucardapioPay {
    return new MapeadorDeMeucardapioPay();
  }
}
