import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorDePizzaTamanhoSaboresDePara} from "../../mapeadores/MapeadorDePizzaTamanhoSaboresDePara";

export class PizzaTamanhoSaboresDePara  extends ObjetoPersistente  {
  nome: string;
  codigoPdv: string;
  qtdeSabores: number;
  templateTamanho: any
  constructor() {
    super();
  }
  mapeador(): any {
    return new MapeadorDePizzaTamanhoSaboresDePara() ;
  }

}
