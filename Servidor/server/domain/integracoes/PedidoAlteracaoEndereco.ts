import {ObjetoPersistente} from "../ObjetoPersistente";
import {Pedido} from "../delivery/Pedido";
import {OrderEvent} from "../opendelivery/OrderEvent";
import {MapeadorPedidoAlteracaoEndereco} from "../../mapeadores/MapeadorPedidoAlteracaoEndereco";
import * as moment from "moment";
import {PedidoGenerico} from "../delivery/PedidoGenerico";
import {Endereco} from "../delivery/Endereco";

export class PedidoAlteracaoEndereco  extends ObjetoPersistente{
  public horario: Date;
  public dataExpiracao: Date;
  public aceito: boolean;
  public metadata: string;
  public horarioResposta: Date;
  public acao: string;
  public operador: any;
  constructor(public id: string, public pedido: PedidoGenerico, public orderId: string, metadata: any) {
    super();
    if(metadata) this.metadata = JSON.stringify(metadata)
    this.horario  = new Date();
    this.dataExpiracao = moment().add(15, 'm').toDate(); ///ifood 15 miniutos reponder
  }



  mapeador(): any {
    return new MapeadorPedidoAlteracaoEndereco();
  }

  getEndereco(){
    return  JSON.parse(this.metadata).address;
  }

  async atualizeAceito(aceito: boolean, operador: any){
    this.aceito = aceito;
    this.operador = operador;
    this.horarioResposta = new Date();
    await this.mapeador().atualizeSync(this);
  }

  jaRespondeu(){
    return this.aceito != null
  }

  toDto(){
    let dto: any = Object.assign({}, this)

    let address: any = this.getEndereco();

    dto.novoEndereco = Endereco.novoIfood(null, address)
    dto.enderecoCompleto = dto.novoEndereco.obtenhaEndereco();

    return dto;
  }
}
