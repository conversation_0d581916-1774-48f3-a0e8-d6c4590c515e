import {DadosRelatorioClientesFidelidade} from "./DadosRelatorioClientesFidelidade";
import {EnumStatusContato} from "../../lib/emun/EnumStatusContato";

export class RelatorioClientesFidelidade {
  mapDados: any = {};
  qtdeClientes = 0;
  totalGasto = 0.0;

  adicione(registro: DadosRelatorioClientesFidelidade) {
    let rotulo = registro.status;

    if( registro.status === 'Importado' ) {
      return;
    }

    let dadosRegistro = this.mapDados[rotulo];

    if( !this.mapDados[rotulo] ) {
      dadosRegistro = new DadosTipoDeCliente(rotulo, registro.qtdeClientes, registro.qtdeVisitas,
        registro.totalGasto, registro.cicloDeRecorrencia, registro.tempoRetorno, registro.ticketMedio);
      this.mapDados[rotulo] = dadosRegistro
    } else {
      dadosRegistro.adicione(registro);
    }

    this.qtdeClientes += registro.qtdeClientes;

    this.totalGasto += registro.totalGasto;

    return dadosRegistro;
  }
}

class DadosTipoDeCliente {
  mediaVisitas: number;

  constructor(private rotulo: string, private qtdeClientes: number, private qtdeVisitas: number,
              private totalGasto: number, private cicloDeRecorrencia: number, private tempoRetorno: number, private ticketMedio: number) {
    this.mediaVisitas = this.qtdeVisitas / this.qtdeClientes;
  }

  adicione(registro: DadosRelatorioClientesFidelidade) {
    this.qtdeClientes += registro.qtdeClientes;
    this.totalGasto += registro.totalGasto;
    this.qtdeVisitas += (registro.qtdeVisitas * registro.qtdeClientes);
    this.mediaVisitas = this.qtdeVisitas / this.qtdeClientes;
  }
}
