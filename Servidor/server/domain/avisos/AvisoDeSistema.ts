import {TipoDeAvisoEnum} from "./TipoDeAvisoEnum";
import {Mesa} from "../Mesa";
import {Empresa} from "../Empresa";
import {ObjetoPersistente} from "../ObjetoPersistente";
import {MapeadorBasico} from "../../mapeadores/MapeadorBasico";
import {MapeadorDeAvisoDeSistema} from "../../mapeadores/MapeadorDeAvisoDeSistema";

export class AvisoDeSistema extends ObjetoPersistente {
  entregue: boolean;

  constructor(private tipo: TipoDeAvisoEnum, private mensagem: string) {
    super();
    this.entregue = false;
  }

  static AvisoSolicitacaoDeGarcom(empresa: Empresa, mesa: Mesa) {
    return new AvisoDeSistema(TipoDeAvisoEnum.SolicitacaoDeGarcom, empresa.identificadorMesa + " " +
      mesa.nome + " está solicitando um garçom.")
  }

  static AvisoFecharConta(empresa: Empresa, mesa: Mesa) {
    return new AvisoDeSistema(TipoDeAvisoEnum.FecharConta, empresa.identificadorMesa + " " +
      mesa.nome + " pediu para fechar a conta.")
  }

  mapeador(): MapeadorBasico {
    return new MapeadorDeAvisoDeSistema();
  }
}
