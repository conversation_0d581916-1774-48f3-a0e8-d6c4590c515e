import {Empresa} from "./Empresa";
import {TipoDeNotificacaoEnum} from "./TipoDeNotificacaoEnum";
import {Contato} from "./Contato";
import {Cartao} from "./Cartao";
import {StatusDeMensagem} from "../service/StatusDeMensagem";
import {Campanha} from "./Campanha";
import {LinkEncurtado} from "./LinkEncurtado";
import {EnumMeioDeEnvio} from "./EnumMeioDeEnvio";
import {NumeroWhatsapp} from "./NumeroWhatsapp";

export class MensagemBot {
  id: number;
  empresa: Empresa;
  mensagem: string;
  resposta: string;
  horario: Date;
  telefone: string;
  contato: Contato;
  imagem: string;
  intent: string;
  contextos: string;
  nome: string;
  numeroWhatsapp: NumeroWhatsapp;
  sessao: any;
  chatId: string;

  constructor() {}

  static nova(empresa: Empresa, contato: Contato, numeroWhatsapp: NumeroWhatsapp, telefone: string, mensagem: string,
              resposta: string, intent: string, contextos: string, imagem: string = '', sessao: any, nome: string = ''): MensagemBot {
    const mensagemBot = new MensagemBot();

    mensagemBot.empresa = empresa;
    mensagemBot.contato = contato;
    mensagemBot.resposta = resposta;
    mensagemBot.numeroWhatsapp = numeroWhatsapp;
    mensagemBot.mensagem = mensagem;
    mensagemBot.contextos = contextos;
    mensagemBot.intent = intent;
    mensagemBot.horario = new Date();
    mensagemBot.telefone = telefone;
    mensagemBot.imagem = imagem;
    mensagemBot.sessao = sessao;
    mensagemBot.nome = nome;

    return mensagemBot;
  }
}
