import {<PERSON><PERSON><PERSON>} from "../Usuario";
import {Categoria} from "../delivery/Categoria";

export class Catalogo {
  id: number;
  ativo: boolean;

  precoPorEmpresa: boolean;
  disponibilidadePorEmpresa: boolean;
  compartilhado: boolean;
  disponibilidades: any = [];
  constructor(public nome: string = null) {
    this.ativo = true;
    this.precoPorEmpresa = false;
    this.disponibilidadePorEmpresa = false;
    this.criacao = new Date()
    this.atualizacao = new Date()
    this.compartilhado = false;
  }

  //Horários para auditoria
  criacao: Date;
  atualizacao: Date;
  desativacao: Date;

  //Usuário que criou
  criador: Usuario

  categorias: Array<Categoria> = [];
}
