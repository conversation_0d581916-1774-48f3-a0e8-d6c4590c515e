function getReactElement(dom) {
    for (var key in dom) {
        if (key.startsWith('__reactInternalInstance$')) {
            var compInternals = dom[key]._currentElement;
            debugger;
            var compWrapper = compInternals._owner;
            var comp = compWrapper._instance;
            return comp;
        }
    }

    return null;
}

const extensionWidthSize = 322;

// Add prototype to replace whatsappweb styles
var UID = {
    _current: 0,
    getNew: function(){
        this._current++;
        return this._current;
    }
};
HTMLElement.prototype.pseudoStyle = function(element,prop,value){
    var _this = this;
    var _sheetId = 'pseudoStyles';
    var _head = document.head || document.getElementsByTagName('head')[0];
    var _sheet = document.getElementById(_sheetId) || document.createElement('style');
    _sheet.id = _sheetId;
    var className = 'pseudoStyle' + UID.getNew();

    _this.className +=  " "+className;

    _sheet.innerHTML += " ."+className+":"+element+"{"+prop+":"+value+"}";
    _head.appendChild(_sheet);
    return this;
};

// Create iframe to the react app
const iframe = document.createElement('iframe');
iframe.src = window.chrome.extension.getURL('index.html');
iframe.style.cssText = 'position:fixed;top:0;right:0;display:block;width:' + extensionWidthSize + 'px;height:100%;z-index:99;';
iframe.frameBorder = 0;

function isWhatsAppWebReady(){
    return document.getElementsByClassName('app').length > 0;
}

function isChatOpened(){
    return document.getElementById('main') != null;
}

function findValuesInObject(obj, key) {
    var seen = new Set, active = [obj];

    while (active.length) {
        var new_active = [], found = [];
        for (var i=0; i<active.length; i++) {
            Object.keys(active[i]).forEach(function(k){
                var x = active[i][k];
                if (k === key) {
                    found.push(x);
                    return found;
                } else if (x && typeof x === "object" &&
                    !seen.has(x)) {
                    seen.add(x);
                    new_active.push(x);
                }
            });
        }
        if (found.length) return found;
        active = new_active;
    }
    return null;
}

function getWhatsAppReactObject() {
    const key = Object.keys(document.getElementById("main")).find(key=>key.startsWith("__reactInternalInstance$"));

    return document.getElementById("main")[key];
}

function retrieveChatObject(){
    if (!isChatOpened()){
        return null;
    }

    var scriptContent = `
    (function(){
      var chatObjects = (${findValuesInObject})((${getWhatsAppReactObject})(), 'chat');
      if (!chatObjects){
        return;
      }
      var chatObjectJson = JSON.stringify(chatObjects[0]);
      document.getElementsByTagName("body")[0].setAttribute("tmp_chat", chatObjectJson);
    })();
  `;
    var script = document.createElement('script');
    script.id = 'tmpScript';
    script.appendChild(document.createTextNode(scriptContent));
    (document.body || document.head || document.documentElement).appendChild(script);

    var bodyElement = document.getElementsByTagName("body")[0];
    var result = bodyElement.getAttribute('tmp_chat');
    if (!result || result == "undefined"){
        return null;
    }

    bodyElement.setAttribute('tmp_chat', null);
    script.parentNode.removeChild(script);

    return JSON.parse(result);
}

function setup(){
    if (!isWhatsAppWebReady()) {
        setTimeout(function(){
            setup();
        }, 100);
        return;
    }

    // Resize app width to fit the iframe
    document.getElementById('app').style.width = 'calc(100% - ' + extensionWidthSize + 'px)';
    document.getElementsByClassName('app-wrapper-web')[0].pseudoStyle('after', 'width' , 'calc(100% - ' + extensionWidthSize + 'px)');
    document.getElementsByClassName('app')[0].style.maxWidth = '100%';
    // Insert iframe to the html
    document.body.appendChild(iframe);
}

setup();