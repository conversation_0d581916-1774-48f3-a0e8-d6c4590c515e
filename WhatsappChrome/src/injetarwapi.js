var tabId = null;
var urlServidor = null;
var LARGURA = 325;

function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

function insiraIFrame() {
    var extensionOrigin = chrome.extension.getURL("frame.html");

    var iframe = document.createElement('iframe');
    // Must be declared at web_accessible_resources in manifest.json
    iframe.src = extensionOrigin;

    iframe.id = "frame_promokit";
    iframe.style.cssText = 'position:fixed;top:0;right:0;display:block;' +
        'width:' + LARGURA + 'px;height: 100%;z-index:1000;';
    document.body.appendChild(iframe);

    return document.getElementById('frame_promokit');
}

function insiraScript(nomeScript) {
    return new Promise( (resolve, reject) => {
        var s = document.createElement('script');
        s.src = nomeScript;
        s.async = false;
        (document.head||document.documentElement).appendChild(s);
        s.onload = function() {
            s.parentNode.removeChild(s);

            resolve();
        };
    });
}

function insiraCSS(urlCSS) {
    return new Promise( (resolve, reject) => {
        var link = document.createElement('link');
        link.rel  = 'stylesheet';
        link.type = 'text/css';
        link.href = urlCSS;
        link.media = 'all';
        (document.head||document.documentElement).appendChild(link);

        resolve(true);
    });
}

async function injetarScript() {
    var iFrame = await insiraIFrame();

    iFrame.addEventListener("load", function() {
    });
}

injetarScript();

window.onmessage = async (e) => {
    if( e.data.tipo === 'URL_SERVIDOR' ) {
        var urlServidor = new URL(e.data.url).origin;

        await insiraCSS(urlServidor + '/assets/css/interface.css?v=' + new Date().getTime());
        await insiraScript(urlServidor + '/assets/js/wapi.js?v=' + new Date().getTime());
        await insiraScript(urlServidor + '/assets/js/WhatsappAPI.js?v=' + new Date().getTime());
        await insiraScript(urlServidor + '/assets/js/props_react.js?v=' + new Date().getTime());
    }
};