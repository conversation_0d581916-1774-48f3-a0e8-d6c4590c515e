class ScriptManager {
  constructor() {
    this.loadedScripts = new Map();

    // Define a sequência de carregamento dos scripts
    this.scriptSequence = [
      {
        id: 'wppconnect',
        url: 'libs/wa-js/wppconnect-wa.js',
        removable: true
      },
      {
        id: 'libphonenumber',
        url: 'libs/libphonenumber/libphonenumber-js.min.js',
        removable: false
      },
      {
        id: 'libphonenumber-wrapper',
        url: 'libs/libphonenumber-wrapper.js',
        removable: false
      },
      {
        id: 'wpp-checker',
        url: 'libs/wpp-checker.js',
        removable: false
      },
      {
        id: 'wpp-wapi',
        url: 'libs/wa-js/wpp-wapi.js',
        removable: false
      },
      {
        id: 'wpp-handler',
        url: 'libs/wa-js/wpp-handler.js',
        removable: false
      }
    ];
  }

  isScriptLoaded(scriptId) {
    return this.loadedScripts.has(scriptId);
  }

  async loadScript(scriptId, scriptUrl, removable = false) {
    if (this.isScriptLoaded(scriptId)) {
      console.log(`Script ${scriptId} já está carregado`);
      return;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = chrome.runtime.getURL(scriptUrl);

      // Adicionar timeout para evitar bloqueio indefinido
      const timeoutId = setTimeout(() => {
        console.warn(`Timeout ao carregar script ${scriptId}`);
        // Não rejeita a promise para permitir que a aplicação continue
        // mesmo se um script falhar
        this.loadedScripts.set(scriptId, {
          loaded: false,
          timestamp: Date.now(),
          element: script,
          error: 'timeout'
        });
        resolve();
      }, 10000); // 10 segundos de timeout

      script.onload = () => {
        clearTimeout(timeoutId);
        this.loadedScripts.set(scriptId, {
          loaded: true,
          timestamp: Date.now(),
          element: script
        });
        console.log(`Script ${scriptId} carregado com sucesso`);

        // Remove o script do DOM se for removable
        if (removable) {
          script.remove();
        }

        resolve();
      };

      script.onerror = (error) => {
        clearTimeout(timeoutId);
        console.error(`Erro ao carregar script ${scriptId}:`, error);

        // Registra o erro mas não rejeita a promise para permitir que outros scripts sejam carregados
        this.loadedScripts.set(scriptId, {
          loaded: false,
          timestamp: Date.now(),
          element: script,
          error: error
        });

        resolve(); // Resolve em vez de reject para continuar o carregamento
      };

      (document.head || document.documentElement).appendChild(script);
    });
  }

  async loadAllScripts() {
    console.log('Iniciando carregamento sequencial dos scripts...');

    const results = {
      success: [],
      failed: []
    };

    for (const script of this.scriptSequence) {
      try {
        await this.loadScript(script.id, script.url, script.removable);
        const scriptInfo = this.loadedScripts.get(script.id);

        if (scriptInfo && scriptInfo.loaded) {
          results.success.push(script.id);
        } else {
          results.failed.push(script.id);
          console.warn(`Script ${script.id} não foi carregado corretamente, mas continuando...`);
        }
      } catch (error) {
        console.error(`Erro ao carregar ${script.id}:`, error);
        results.failed.push(script.id);
        // Continua mesmo com erro para não bloquear o carregamento dos outros scripts
      }
    }

    if (results.failed.length > 0) {
      console.warn(`${results.failed.length} scripts não foram carregados: ${results.failed.join(', ')}`);
    }

    console.log(`${results.success.length} scripts foram carregados com sucesso`);

    // Dispara um evento para notificar que os scripts foram carregados
    const event = new CustomEvent('scriptsLoaded', { detail: results });
    window.dispatchEvent(event);

    return results;
  }

  getLoadedScripts() {
    return Array.from(this.loadedScripts.keys());
  }

  removeScript(scriptId) {
    const scriptInfo = this.loadedScripts.get(scriptId);
    if (scriptInfo && scriptInfo.element) {
      scriptInfo.element.remove();
    }
    this.loadedScripts.delete(scriptId);
  }

  clearLoadedScripts() {
    // Remove todos os scripts do DOM e limpa o registro
    for (const [scriptId, scriptInfo] of this.loadedScripts.entries()) {
      if (scriptInfo.element) {
        scriptInfo.element.remove();
      }
    }
    this.loadedScripts.clear();
    console.log('Registro de scripts carregados foi limpo');
  }
}

// Exporta a classe para uso global
window.ScriptManager = ScriptManager;